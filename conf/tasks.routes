# This file defines all application routes for tasks
# ~~~~

POST    /v1/coliving/recharge-prepaid-uses                               @controllers.tasks.v1.TasksController.rechargeColivingPrepaidUses()
POST    /v1/coliving/invalidate-confirmation-tokens                      @controllers.tasks.v1.TasksController.invalidateOldTokens()
POST    /v1/rates/building/admin/send-new-rate-email                     @domains.back_office.controllers.v1.rates.RateNotificationController.sendBuildingAdminsNewRatePricesEmail()
POST    /v1/rates/lavomat/admin/send-upcoming-rate-expiration-email      @domains.back_office.controllers.v1.rates.RateNotificationController.sendLavomatAdminUpcomingRatesExpirationEmail()
POST    /v1/exchange-rate/ui/update                                      @controllers.tasks.v1.TasksController.updateUIExchangeRate()
POST    /v1/exchange-rate/usd/update                                     @controllers.tasks.v1.TasksController.updateUSDExchangeRate()
POST    /v1/surge-pricing/c30                                            @controllers.tasks.v1.TasksController.c30()
POST    /v1/surge-pricing/c1                                             @controllers.tasks.v1.TasksController.c1()
POST    /v1/preventive-maintenance/hydrate                               @controllers.tasks.v1.TasksController.hydratePreventiveMaintenanceCache()
POST    /v1/reporting/laundromats                                        @domains.reporting.controllers.v1.ReportingLaundromatsController.doReports()