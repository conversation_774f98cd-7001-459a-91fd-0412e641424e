# ======= App settings =======
play.modules.enabled = [
    "play.filters.csrf.CSRFModule",
    "play.filters.cors.CORSModule",
    "play.filters.headers.SecurityHeadersModule",
    "play.filters.gzip.GzipFilterModule",
    "play.api.libs.mailer.MailerModule",
    "play.api.libs.mailer.SMTPConfigurationModule",
    "play.db.jpa.JPAModule",
    "play.api.libs.ws.ning.NingWSModule",
    "play.api.libs.openid.OpenIDModule",
    "play.libs.ws.ning.NingWSModule",
    "play.libs.openid.OpenIdModule",
    "play.api.cache.EhCacheModule",
    "play.api.db.DBModule",
    "play.api.db.HikariCPModule",
    "play.db.DBModule",
    "play.inject.BuiltInModule",
    "play.core.ObjectMapperModule",
    "play.api.inject.BuiltinModule",
    "play.api.i18n.I18nModule",
    "playconfig.ConfigModule"
]

play.crypto.secret = "changeme"
play.i18n.langs = [ "en", "es" ]
play.evolutions.enabled=false

# ======= Custom settings =======

# MQTT AWS BROKER IP
mqtt.broker.ip = "tcp://10.0.0.0:80"

## payment getaways ##
payment_getaways.transactions.disabled = false

# MERCADO PAGO SETTINGS - produccion mercado pagos -
mercadopago.access.token = "mp-access-token"
mercadopago.client.id = "mp-client-id"
mercadopago.client.secret = "mp-client-secret"

# Bancard settings - sandbox
bancard.keys.public = "bancard-keys-public"
bancard.keys.private = "bancard-keys-private"
bancard.webPayment.url = "https://example.com/bancard-test"
bancard.keys.secret = "fake-uid-data"

# Transact settings - sandbox
transact = {
    lavomat = {
        keys.empcode = "transact-keys-empcode"
        keys.termcode = "transact-keys-termcode"
        keys.emphash = "transact-keys-emphash"
        emulacion=true
    }
    lavamar = {
        keys.empcode = "transact-keys-empcode"
        keys.termcode = "transact-keys-termcode"
        keys.emphash = "transact-keys-emphash"
        emulacion=true
    }
}

# BambooPayment settings - sandbox
bambooPayment.physicalAgent.keyword = "bp-physicalAgent-keyword"
bambooPayment.physicalAgent.username = "bp-physicalAgent-username"
bambooPayment.physicalAgent.customer.id = "bp-physicalAgent-customer-id"
bambooPayment.physicalAgent.customer.key = "bp-physicalAgent-customer-key"
bambooPayment.webPayments.customer.key = "bp-webPayments.customer-key"
bambooPayment.webPayments.customer.id = "bp-webPayments.customer-id"
bambooPayment.webPayments.url = "https://testing.pagosweb.com.uy/v3.4/requestprocessor.aspx"

# Environment
api.env = "Testing"
api.baseUrl="https://app-sandbox-ec2.lavomat.com.uy"

# Assistant portal
assistant.url="http://localhost:8080"

# Coliving confirmation token
confirmation.token.url="/complete-signup?token="

# Public site base url
publicSite.baseUrl="http://localhost:3000"

email.lavomat.admin="<EMAIL>"
email.lavomat.ceo="<EMAIL>"
email.lavomat.cfo="<EMAIL>"
email.lavomat.asst="<EMAIL>"

# Totem
totem.booking.enabled=true

# Redis cache
play.modules.disabled += "play.api.cache.EhCacheModule"
play.modules.enabled += "services.cache.redis.RedisModule"

redis.host="localhost"
redis.port=6379
redis.enabled="false"

# Sicfe
sicfe = {
    lavomat = {
        url=""
        env=""
        user=""
        pass=""
        tenant=""
        ruc=""
        companyName=""
        commercialName=""
        phone=""
        email=""
    }
    lavamar = {
        url=""
        env=""
        user=""
        pass=""
        tenant=""
        ruc=""
        companyName=""
        commercialName=""
        phone=""
        email=""
    }
}
