# Routes
# This file defines all application routes (Higher priority routes first)
# ~~~~

GET     /api/v1/ping                                            controllers.WebController.ping()
POST    /api/v1/ping                                            controllers.WebController.ping()

# Home page
GET      /                                                      controllers.Application.index()

# Map static resources from the /public folder to the /assets URL path
GET      /assets/*file                                          controllers.Assets.versioned(path="/public/assets", file: Asset)
GET      /public/*file                                          controllers.Assets.versioned(path="/public", file: Asset)
GET      /access/*file                                          controllers.Assets.versioned(path="/public/access", file: Asset)

# Backoffice
GET     /api/v1/dashboard                                       controllers.DashboardController.getDashboardData()

# Administrations
POST     /api/v1/administrations/:aid/sendRefreshTokenReminder              controllers.AdministrationsController.sendRefreshTokenReminder(aid:Int)
GET      /api/v1/administrations/automaticRefreshToken 			            controllers.AdministrationsController.automaticRefreshTokenReminder()

# Users
#       balance
POST     /api/v1/users/accredit          						controllers.UsersController.accreditation()
GET      /api/v1/users/accredit/check					        controllers.UsersController.checkAccreditation(param1:String)
#       contact
GET 	 /api/v1/users/support									controllers.UsersController.sendSupportMessage(param1:String,param2:String,param3:String)
POST     /api/v1/users/support/info								controllers.PublicServicesController.sendInfoMessage()

# Buildings
#   doc: /doc/api_v1_buildings.apib
GET      /api/v1/buildings                                      controllers.BuildingsController.listBuildings()
GET      /api/v1/buildings/:bid                                 controllers.BuildingsController.getBuilding(bid:Int)
GET      /api/v1/buildings/:bid/bill                            controllers.BillingController.billBuilding(bid:Int)
DELETE   /api/v1/buildings/:bid                                 controllers.BuildingsController.deleteBuilding(bid:Int)
PUT      /api/v1/buildings/:bid/machines/:mid                   controllers.BuildingsController.assignMachine(bid:Int, mid:Int)
DELETE   /api/v1/buildings/:bid/machines/:mid                   controllers.BuildingsController.unassignMachine(bid:Int, mid:Int)
GET      /api/v1/buildings/:buildingId/rpi-count                controllers.BuildingsController.getRpiCount(buildingId:Int)
PUT      /api/v1/buildings/:bid/units/:uid/cards/:cid           controllers.BuildingsController.assignCardToUnit(bid:Int, uid:Int,  cid:String)
DELETE   /api/v1/buildings/:bid/units/:uid/cards/:cid           controllers.BuildingsController.unassignCardFromUnit(bid:Int, uid:Int, cid:String)
GET      /api/v1/buildings/:bid/units/:uid/cards/:cid/:action   controllers.BuildingsController.cardAction(bid:Int, uid:Int,  cid:String, action:String)
POST     /api/v1/buildings/upload_payment                       controllers.BillingController.uploadBuildingPayments()
GET      /api/v1/laundromats                                    controllers.BuildingsController.getLaundromats(userid:Int)
GET      /api/v1/buildings/:buildingId/machines/different-rates       controllers.BuildingsController.getMachinesWithDifferentsRatesByBuildingId(buildingId:Int)

#Units
POST     /api/v1/buildings/:bid/units                           controllers.BuildingsController.createUnit(bid:Int)
PUT      /api/v1/buildings/:bid/units/:uid                      controllers.BuildingsController.updateUnit(bid:Int, uid:Int)
DELETE   /api/v1/buildings/:bid/units/:uid                      controllers.BuildingsController.deleteUnit(bid:Int, uid:Int)

PUT      /api/v1/buildings/:bid/uses                            controllers.BuildingsController.pushUses(bid:Int)
POST     /api/v1/buildings/:bid/upload_uses                     controllers.BuildingsController.uploadUses(bid:Int)
GET      /api/v1/unit/block                                     controllers.PublicServicesController.blockUnit(param1:String, param2:String)


# Parts
# MACHINE, REPLACEMENT, TOOL, CARD
GET      /api/v1/parts                                          controllers.PartsController.listParts(type : String ?= "ALL")
PUT      /api/v1/parts/:pid                                     controllers.PartsController.updatePart(pid:Int)
DELETE   /api/v1/parts/:pid                                     controllers.PartsController.deletePart(pid:Int)

# MACHINE
#   doc: /doc/api_v1_machines.apib
PUT      /api/v1/machines/:machineId/zero-pending-uses          controllers.MachineController.zeroingPendingUses(machineId:Int)

# MACHINE RPI
PUT      /api/v1/machines/:parentId/assign-rpi-child/:childId   controllers.MachineController.assignRPIChild(parentId:Int, childId:Int)
PUT      /api/v1/machines/:parentId/unassign-rpi-child          controllers.MachineController.unassignRPIChild(parentId:Int)

# SOAP DISPENSER
GET      /api/v1/dispensers                                     controllers.MachineController.listSoapDispensers()
DELETE   /api/v1/dispensers/:pid                                controllers.MachineController.deleteSoapDispenser(pid:Int)


# Reports
GET      /api/v1/reports/uses                                   controllers.ReportsController.findUses()
GET      /api/v1/reports/uses/:bid/get_discredit                controllers.ReportsController.getDiscreditedUses(bid:Int)
GET      /api/v1/reports/uses/:bid/xls                          controllers.ReportsController.exportToExcel(bid:Int)
GET      /api/v1/reports/uses/:uid/accredit                     controllers.ReportsController.accreditUse(uid:Int)
GET      /api/v1/reports/uses/:uid/discredit                    controllers.ReportsController.discreditUse(uid:Int)
GET		 /api/v1/reports/transactions							controllers.ReportsController.findTransactions()
GET      /api/v1/reports/bills/xls                              controllers.ReportsController.exportBillsToExcel()

# Billing
#   doc: /doc/api_v1_bills.apib
POST     /api/v1/bills                            			    controllers.BillingController.bill()
POST     /api/v1/bills/:num/cancel                              controllers.BillingController.cancelBill(num:Int)
GET      /api/v1/bills/:num/pdf                                 controllers.BillingController.getPDF(num:Int)
GET      /api/v1/bills/:num                                     controllers.BillingController.billDetail(num:Int)
GET      /api/v1/bills/:num/shortDetail                         controllers.BillingController.billShortDetail(num:Int)
GET      /api/v1/bills                                          controllers.BillingController.getBills()
GET      /api/v1/billtypes                                      controllers.BillingController.getBillTypes()
GET      /api/v1/dginumbers                                     controllers.BillingController.getDGINumbers()
GET      /api/v1/bills/item/measures                               controllers.BillingController.getBillItemMeasures()

# Machine IoT
POST     /api/v1/alive                                          domains.rpi.controllers.v1.MachinesController.alive()
#   doc: /doc/api_v1_activate.apib
GET     /api/v1/activate/request								controllers.MachineController.activateMachineRequest(param1:String, param2:Int, param3:String, param4:Boolean, param5:String, param6:Int, param7:String, param8:String, param9:String)
GET     /api/v1/activate/request/simple					        controllers.MachineController.activateMachineSimpleRequest(param1:String, param2:Int, param3:String)

# Machine Model
GET      /api/v1/machine/model                                  controllers.MachineModelController.getMachineModels()
POST     /api/v1/machine/model                                  controllers.MachineModelController.createMachineModel()
PUT      /api/v1/machine/model/:mmid                            controllers.MachineModelController.editMachineModel(mmid:Int)
DELETE   /api/v1/machine/model/:mmid                            controllers.MachineModelController.deleteMachineModel(mmid:Int)

# Transaction
POST    /api/v1/transaction/internal							  controllers.TransactionController.createInternalTransaction()

# Group
POST    /api/v1/group/card                                         controllers.GroupController.addCardToGroup()
POST    /api/v1/group/user                                         controllers.GroupController.addUserToGroup()
GET     /api/v1/confirmGroupInivtation					           controllers.PublicServicesController.confirmGroupInivtation(param1:String,param2:String,param3:String)
GET     /api/v1/group								               controllers.GroupController.getUserGroups(param1:String)
GET     /api/v1/group/main								           controllers.GroupController.getMainUserGroups(param1:String)

# Card
#  doc: /api_v1_cards.apib
GET     /api/v1/card/balance								        controllers.CardController.getCardBalance(param1:String)
GET     /api/v1/card/uses/:param1								    controllers.CardController.getCardUses(param1:String)
GET     /api/v1/card/transactions/:param1						    controllers.CardController.getCardTransactions(param1:String)
GET     /api/v1/cards/:cardId/bills                                 controllers.CardController.findBillsByCard(cardId:Int)
# TODO: DELETE. temporal restoring until the FE is fully updated
GET     /api/v1/card/methods                                        domains.public_site.controllers.v1.CardsController.paymentMethods(param1:String)

# Cierre automatico
GET /api/v1/administrations/close 									controllers.BillingController.automaticClosure()
GET /api/v1/administrations/:aid/close								controllers.BillingController.closeAdministration(aid:Int)

PUT /api/v1/buildings/:buildingId/rechargePrepaidUnitsBalance       controllers.BuildingsController.rechargePrepaidUses(buildingId:Int)

# Machine Booking
POST    /api/v1/machine/booking									   controllers.MachineController.bookAMachine()
DELETE  /api/v1/machine/:mbid									   controllers.MachineController.deleteMachineBooking(mbid:Int)
GET     /api/v1/machine/status/:mbid							   controllers.MachineController.getAvailableMachines(mbid:Int)

# QR - TOTEM PAYMENTS
POST 	/api/v1/totem/order										   controllers.StoreController.createTotemTx()
POST    /api/v1/totem/order/:id/confirm							   controllers.StoreController.confirmTotemTx(id:String)
POST    /api/v1/totem/order/send								   controllers.StoreController.sendTotemTx()

# Maintenances
POST	/api/v1/maintenance/buildings/:bid						   controllers.MaintenanceController.createMaintenance(bid:Int)
PUT	    /api/v1/maintenance/:mid/buildings/:bid					   controllers.MaintenanceController.updateMaintenance(bid:Int, mid:Int)
DELETE	/api/v1/maintenance/:mid/buildings/:bid					   controllers.MaintenanceController.deleteMaintenance(bid:Int, mid:Int)
GET		/api/v1/maintenance/buildings/:bid						   controllers.MaintenanceController.getBuildingMaintenances(bid:Int)
GET		/api/v1/maintenance/preventive							   controllers.MaintenanceController.getPreventiveMaintenances()
GET		/api/v1/maintenance/preventive/building/:bid			   controllers.MaintenanceController.getPreventiveMaintenancesByBuilding(bid:Int)

POST    /api/v1/maintenance/building/:bid                          controllers.MaintenanceController.assignMaintenanceParameterToBuilding(bid:Int)
POST    /api/v1/maintenance/machineModel/:mmid                     controllers.MaintenanceController.assignMaintenanceParameterToMachineModel(mmid:Int)

# Bill Collection
GET     /api/v1/bills/user/:userId/status/:status                  controllers.BillingController.getAssociatedBillsByStatus(userId:Int, status:String)
PUT     /api/v1/bill/:billId/status/promote                        controllers.BillingController.promoteBillCollectionStatus(billId:Int)
PUT     /api/v1/bills/status/promote                               controllers.BillingController.bulkPromoteBillCollectionStatus()
PUT     /api/v1/bill/:billId/status/demote                         controllers.BillingController.demoteBillCollectionStatus(billId:Int)
PUT     /api/v1/bill/:billId/status/:status                        controllers.BillingController.updateBillCollectionStatus(billId:Int, status:String)
PUT     /api/v1/bill/:billId/user/assign/:userId/date/:date        controllers.BillingController.assignBillDebtCollector(billId:Int, userId:Int, date:String)
PUT     /api/v1/bill/:billId/user/unassign                         controllers.BillingController.unassignBillDebtCollector(billId:Int)

# Branding
#   doc: /doc/api_v1_branding.apib
GET     /api/v1/branding/items                                  controllers.BrandingController.getItems()
GET     /api/v1/branding/requests                               controllers.BrandingController.getRequestedItems()
PUT     /api/v1/branding/requests/:rid/delivered                controllers.BrandingController.deliverRequestedItem(rid:Int)
POST    /api/v1/branding/items                                  controllers.BrandingController.createItem()
PUT     /api/v1/branding/items/:id                              controllers.BrandingController.updateItem(id:Int)
DELETE  /api/v1/branding/items/:id                              controllers.BrandingController.deleteItem(id:Int)

# CardRequest
GET     /api/v1/cardRequest/confirm                               controllers.CardRequestController.confirmNewCard(token:String)
GET     /api/v1/cardRequest/reject                                controllers.CardRequestController.rejectNewCard(token:String)
GET     /api/v1/cardRequest/activate                              controllers.CardRequestController.activate(token:String)
GET     /api/v1/cardRequest/rejectActivation                      controllers.CardRequestController.rejectActivation(token:String)
POST    /api/v1/cardRequest/rejectActivationSubmit                controllers.CardRequestController.rejectActivationSubmit

# Audit
GET     /api/v1/audits                                             controllers.AuditController.getAudits()


# Utilities
POST    /api/v1/utilities/mqtt-message                              controllers.UtilitiesController.sendMQTTMessage()

PUT     /api/v1/utilities/run-task                                  controllers.UtilitiesController.runTask()

GET     /api/v1/utilities/exchange-rates                             controllers.UtilitiesController.getExchangeRates()

# TODO: verify if this is used by another client other than public-site. If not, remove
GET 	/api/v1/administrations/:aid/buildings					    controllers.AdministrationsController.listAdministrationBuildings(aid:Int)


# SimpleSolution
#   doc: /doc/simple-solutions_v1.apib
# POST    /simple-solutions/v1/signin

# Shortener
GET     /s/:token       controllers.s.ShortenerController.redirectByTinyUrl(token:String)

# Tasks
#   doc: /doc/tasks_v1.apib
-> /tasks tasks.Routes

# DOMAINS
# ~~~~

->  /asst               assistant.Routes
->  /bot                bot.Routes
->  /public-site        public_site.Routes
->  /rpi                rpi.Routes
->  /sale               sale.Routes
->  /totem              totem.Routes
->  /webhook            webhooks.Routes
->  /                   auth.Routes
->  /                   back_office.Routes
->  /                   payment_gateways.Routes
