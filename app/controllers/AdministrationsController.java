package controllers;

import com.play4jpa.jpa.db.Tx;
import global.APIException;
import global.ErrorMessage;
import global.PermissionValidator;
import global.PermissionValidator.PromiseCallback;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import models.Administration;
import models.Building;
import models.Role;
import org.apache.commons.mail.EmailException;
import org.json.JSONObject;
import play.Play;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import security.v1.Secured;
import serializers.BuildingSerializer;
import services.AdministrationService;
import services.ContactSupportService;
import utils.email.EmailService;

@ErrorMessage
@global.LoggingMessage
@Tx
@Security.Authenticated(Secured.class)
public class AdministrationsController extends AbstractController {

    // TODO: move to public_site domain
    public Promise<Result> listAdministrationBuildings(int administrationId) throws APIException {
        JSONObject response = new JSONObject();
        Map<String, String[]> queryString = request().queryString();

        dto.administration.GetAdministrationBuildingsParameters parameters = new dto.administration.GetAdministrationBuildingsParameters(
            administrationId,
            queryString
        )
            .validate();

        try {
            AdministrationService service = new AdministrationService();

            List<Building> buildings = service.getBuildings(
                parameters.getAdministration(),
                parameters.getListSplitOnly()
            );

            response = BuildingSerializer.buildingListToJsonPartial(buildings);
        } catch (APIException e) {
            e.printStackTrace();
            throw e;
        }

        return json(response.toString());
    }

    public Promise<Result> sendRefreshTokenReminder(final int adminId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    try {
                        Administration admin = Administration.findById(adminId);
                        String server = request().host();
                        // TODO: get this function from payment_gatway domain
                        String adminEmails = admin.getContact();
                        String id = admin.getId() + "";
                        String name = admin.getName();
                        String clientId = Play
                            .application()
                            .configuration()
                            .getString("mercadopago.client.id");
                        String redirect_url =
                            "https://auth.mercadopago.com.uy/authorization?client_id=" + clientId;
                        redirect_url += "&response_type=code&platform_id=mp&state=" + id;
                        redirect_url +=
                            "&redirect_uri=https://app.lavomat.com.uy/api/v1/transaction/bind";

                        String body = views.html.refreshMPToken.render(name, redirect_url).body();
                        if (adminEmails == "") {
                            EmailService.send(
                                ContactSupportService.SUPPORT_INFO_EMAIL,
                                "Este mail debe ser reenviado a la administración " + name,
                                body
                            );
                        } else {
                            EmailService.send(
                                adminEmails,
                                "LAVOMAT - Autenticación con Mercado de Pago",
                                body
                            );
                        }
                    } catch (EmailException | IOException e) {
                        e.printStackTrace();
                    }
                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN
        );
    }

    // TODO: move to tasks domain
    public Promise<Result> automaticRefreshTokenReminder() throws APIException {
        play.Logger.info("Starting automatic refresh token reminder...");
        List<Administration> adminsToSend = getAdministrationsToSendRefreshToken(new Date());

        play.Logger.info("Got " + adminsToSend.size() + " to send refresh token...");
        for (Administration a : adminsToSend) {
            play.Logger.info("Processing " + a.getName() + "...");
            sendRefreshTokenReminder(a.getId());
        }
        return Promise.<Result>pure(ok());
    }

    private List<Administration> getAdministrationsToSendRefreshToken(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -5);
        Date limitDate = calendar.getTime();
        List<Administration> result = Administration.filterByMPCratedDateBeforeOf(limitDate);

        return result;
    }
}
