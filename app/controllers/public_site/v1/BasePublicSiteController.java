package controllers.public_site.v1;

import controllers.AbstractController;
import global.APIException;
import models.Administration;
import models.Building;
import models.User;
import play.mvc.Http;

public abstract class BasePublicSiteController extends AbstractController {

    protected Administration getAdministration(Http.Context context) throws APIException {
        User user = getContextUser(context);
        if (user == null) {
            throw APIException.raise(APIException.APIErrors.UNAUTHORIZED);
        }

        return user.getAdministration();
    }

    protected Building getColiving(Http.Context context, int buildingId) throws APIException {
        Administration administration = getAdministration(context);
        if (administration.getBuildings() == null) {
            throw APIException.raise(APIException.APIErrors.BUILDING_NOT_FOUND);
        }

        Building building = administration.findBuildingById(buildingId);

        if (building == null) {
            throw APIException.raise(APIException.APIErrors.BUILDING_NOT_FOUND);
        }

        return building;
    }
}
