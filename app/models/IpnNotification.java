package models;

import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToOne;

@Entity
public class IpnNotification extends Model<IpnNotification> {

    private static final long serialVersionUID = -3557047328270703103L;

    @Id
    @GeneratedValue
    private int id;

    // Mercado Pago
    private String topic;
    private String mp_payment_id;
    private Date creationDate;

    @OneToOne
    private Transaction transaction;

    public IpnNotification() {}

    public IpnNotification(String topic, String id) {
        this.topic = topic;
        this.mp_payment_id = id;
        this.creationDate = new Date();
    }

    private static Finder<Integer, IpnNotification> find = new Finder<Integer, IpnNotification>(
        Integer.class,
        IpnNotification.class
    );

    public static IpnNotification findById(int id) {
        return find.byId(id);
    }

    public static IpnNotification findByMpPaymentId(String id) {
        return find.query().eq("mp_payment_id", id).findUnique();
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTopic() {
        return this.topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getMpPaymentId() {
        return this.mp_payment_id;
    }

    public void setMpPaymentId(String p_id) {
        this.mp_payment_id = p_id;
    }

    public Date getCreationDate() {
        return this.creationDate;
    }

    public void setCreationDate(Date d) {
        this.creationDate = d;
    }

    public Transaction getTransaction() {
        return this.transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }
}
