package models;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;
import services.bill.CFEItem;
import utils.TimeZoneUtils;

@Entity
public class CardEvent extends Model<CardEvent> implements CFEItem, BillableItem {

    private static final long serialVersionUID = -3557047328270703103L;

    @Id
    @GeneratedValue
    private int id;

    private Date timestamp;

    @ManyToOne(cascade = { CascadeType.PERSIST, CascadeType.REFRESH, CascadeType.MERGE })
    private Card card;

    private String headline;

    private String uid;

    @ManyToOne
    private Unit unit;

    @ManyToOne
    private Bill bill;

    @Enumerated(EnumType.STRING)
    private CardEventType eventType;

    private static Finder<Integer, CardEvent> find = new Finder<Integer, CardEvent>(
        Integer.class,
        CardEvent.class
    );

    public CardEvent() {}

    public CardEvent(CardEventType eventType, Date timestamp, Card card, Unit unit) {
        this.timestamp = timestamp;
        this.card = card;
        this.headline = eventType.itemName + ", UID: " + card.getUuid();
        this.unit = unit;
        this.eventType = eventType;
    }

    public CardEvent(CardEventType eventType, Card card) {
        Long newDateMilliseconds = new Date().getTime();
        this.timestamp = TimeZoneUtils.getLocalDate(newDateMilliseconds, true, false);
        this.card = card;
        this.headline = eventType.itemName + ", UID: " + card.getUuid();
        this.unit = card.getUnit();
        this.eventType = eventType;
    }

    public CardEvent(CardEventType eventType, Card card, String additionalReason) {
        this(eventType, card);
        this.headline += ". " + additionalReason;
    }

    public static List<CardEvent> findAll() {
        return find.all();
    }

    public static void post(CardEventType eventType, Card card) {
        CardEvent event = new CardEvent(eventType, new Date(), card, card.getUnit());
        event.save();
    }

    public static void post(CardEventType eventType, Card card, String additionalReason) {
        CardEvent event = new CardEvent(eventType, card, additionalReason);
        event.save();
    }

    public static List<CardEvent> find(
        Date from,
        Date to,
        int buildingId,
        int machineId,
        int unitId,
        String cardUId,
        String keyword,
        CardEventType eventType
    ) {
        DefaultQuery<CardEvent> query = (DefaultQuery<CardEvent>) find.query();

        if (eventType != null) {
            query.eq("eventType", eventType);
        }

        if (from != null) {
            query.ge("timestamp", from);
        }

        if (to != null) {
            query.le("timestamp", to);
        }

        if (buildingId > 0) {
            query
                .getCriteria()
                .createAlias("unit.building", "building")
                .add(Restrictions.eq("building.id", buildingId));
        }

        if (unitId > 0) {
            query
                .getCriteria()
                .createAlias("card.unit", "unit")
                .add(Restrictions.eq("unit.id", unitId));
        }

        if (cardUId != null) {
            query.join("card").eq("card.uuid", cardUId);
        }

        return query.findList();
    }

    /**
     * Get machine uses which are not associated to a bill or, in case they are,
     * the bill is not a Credit Note.
     */
    public static List<CardEvent> findForBilling(
        Date from,
        Date to,
        int buildingId,
        int unitId,
        CardEventType... eventTypes
    ) {
        DefaultQuery<CardEvent> query = (DefaultQuery<CardEvent>) find.query();

        SimpleDateFormat fromSdf = new SimpleDateFormat("yyyy-MM-dd '00:00:00'");
        SimpleDateFormat untilSdf = new SimpleDateFormat("yyyy-MM-dd '23:59:59'");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {
            if (from != null) {
                query.ge("timestamp", sdf.parse(fromSdf.format(from)));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        try {
            if (to != null) {
                query.le("timestamp", sdf.parse(untilSdf.format(to)));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (buildingId > 0) {
            query
                .getCriteria()
                .createAlias("unit.building", "building")
                .add(Restrictions.eq("building.id", buildingId));
        }

        if (unitId > 0) {
            query
                .getCriteria()
                .createAlias("card.unit", "unit")
                .add(Restrictions.eq("unit.id", unitId));
        }

        if (eventTypes != null && eventTypes.length > 0) {
            query.getCriteria().add(Restrictions.in("eventType", eventTypes));
        }

        // Restriction: No bill or credit note bill
        query
            .getCriteria()
            .createAlias("bill", "ce_bill", JoinType.LEFT_OUTER_JOIN)
            .add(
                Restrictions.or(
                    Restrictions.isNull("ce_bill.id"),
                    Restrictions.eq("ce_bill.billType", Bill.BillType.CREDITNOTE)
                )
            );

        return query.findList();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Card getCard() {
        return card;
    }

    public void setCard(Card card) {
        this.card = card;
    }

    public String getHeadline() {
        return headline;
    }

    public void setHeadline(String headline) {
        this.headline = headline;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Override
    @Transient
    public int getIndDet() {
        return getCard().getUnit().getBuilding().getRate().appliesIVA() ? 3 : 1;
    }

    @Override
    @Transient
    public String getNomItem() {
        return (
            eventType.itemName +
            " (Aplica Tarifa del Período: " +
            getCard().getUnit().getBuilding().getRate().getValidPeriod(this.getTimestamp()) +
            ")"
        );
    }

    @Override
    @Transient
    public int getCantidad() {
        return 0;
    }

    @Override
    @Transient
    public String getUnidadMedida() {
        return BillItem.MEASURE_UNIT_CARD;
    }

    @Override
    @Transient
    public double getPrecioUnitario() {
        return (
            getCard()
                .getUnit()
                .getBuilding()
                .getRate()
                .getPriceCardReplacement(this.getTimestamp()) /
            1.22
        );
    }

    public double getPrecioUnitarioReal() {
        return getCard()
            .getUnit()
            .getBuilding()
            .getRate()
            .getPriceCardReplacement(this.getTimestamp());
    }

    public Unit getUnit() {
        return unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public CardEventType getEventType() {
        return eventType;
    }

    public void setEventType(CardEventType eventType) {
        this.eventType = eventType;
    }

    @Override
    public String getItemType() {
        return eventType.name();
    }

    public Bill getBill() {
        return bill;
    }

    public void setBill(Bill bill) {
        this.bill = bill;
    }

    @Override
    public boolean isAccredited() {
        return true;
    }

    @Override
    public String getReason() {
        return null;
    }

    @Override
    public boolean isAlert() {
        return false;
    }

    @Override
    public Machine getMachine() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public ExternalSaleNotificationRecord getLastExternalSaleNotificationRecord() {
        return null;
    }

    public String getResult() {
        return "60";
    }
}
