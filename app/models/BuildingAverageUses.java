package models;

import com.play4jpa.jpa.models.DefaultQuery;
import java.util.Date;
import java.util.List;
import javax.persistence.Entity;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;

@Entity
public class BuildingAverageUses extends AverageUses {

    public BuildingAverageUses() {}

    public static List<BuildingAverageUses> findCurrent() {
        return BuildingAverageUses.findByDate(new Date());
    }

    public static List<BuildingAverageUses> findByDate(Date date) {
        DefaultQuery<BuildingAverageUses> query = (DefaultQuery<BuildingAverageUses>) finder.query();

        Date from = new DateTime(date).millisOfDay().withMinimumValue().toDate();
        Date to = new DateTime(date).millisOfDay().withMaximumValue().toDate();

        query
            .getCriteria()
            .add(Restrictions.between("createdAt", from, to))
            .add(Restrictions.eq("class", BuildingAverageUses.class.getSimpleName()));

        return query.findList();
    }
}
