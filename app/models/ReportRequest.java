package models;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

@Entity
public class ReportRequest extends Model<ReportRequest> {

    private static final long serialVersionUID = 99;

    @Id
    @GeneratedValue
    private int id;

    private String emailAddress;

    private int countErrors;

    private static Finder<Integer, ReportRequest> find = new Finder<Integer, ReportRequest>(
        Integer.class,
        ReportRequest.class
    );

    public static ReportRequest findByEmailAddress(String emailAddress) {
        DefaultQuery<ReportRequest> q = (DefaultQuery<ReportRequest>) find.query();

        if (emailAddress != null) q.eq("emailAddress", emailAddress + ""); else return null;

        return q.setMaxRows(1).findUnique();
    }

    public static boolean canSendReport(String emailAddress) {
        ReportRequest data = findByEmailAddress(emailAddress);

        return data == null || (data != null && data.getCountErrors() < 3);
    }

    public ReportRequest() {}

    public ReportRequest(String emailAddress, int countErrors) {
        this.emailAddress = emailAddress;
        this.countErrors = countErrors;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress.toLowerCase();
    }

    public int getCountErrors() {
        return countErrors;
    }

    public void setCountErrors(int countErrors) {
        this.countErrors = countErrors;
    }
}
