package models;

import javax.persistence.*;

@Entity
@Table(indexes = { @Index(name = "TOTEM_USER", columnList = "totem_user_id", unique = true) })
public class TotemUserPosTerminalMap extends BaseModel<TotemUserPosTerminalMap> {

    @Id
    @GeneratedValue
    private int id;

    @Column(nullable = false)
    private String posTerminalCode;

    @ManyToOne(optional = false)
    @JoinColumn(name = "totem_user_id")
    private User totemUser;

    public TotemUserPosTerminalMap(int totemUserId, String posTerminalCode) {
        this.posTerminalCode = posTerminalCode;
        this.totemUser = User.findById(totemUserId);
    }

    protected TotemUserPosTerminalMap() {}

    @Override
    public int getId() {
        return this.id;
    }

    public String getPosTerminalCode() {
        return posTerminalCode;
    }

    public User getTotemUser() {
        return totemUser;
    }
}
