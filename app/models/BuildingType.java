package models;

public enum BuildingType {
    BUILDING,
    HOTEL,
    /**
     * LAUNDROMATs are public access buildings
     */
    LAUNDROMAT,
    /**
     * COLIVINGs are buildings that mix a series of POSTPAID uses (free for users sponsored by administration)
     * and PREPAID uses (which users have to pay for them)
     */
    COLIVING,
    /**
     * These are like common LAUNDROMATs, but we make this separation to not show them on the APP and possible
     * it will become handy for other uses.
     */
    THIRD_PARTY_LAUNDROMAT,
}
