package models;

/**
 * === Indicador de facturación ===
 *
 * Indica si el descuento o recargo aplica a productos o servicios exentos, o a
 * que tasa están gravados o si corresponde a un concepto no facturable.
 *
 * Valores posibles:
 *   1: Exento de IVA
 *   2: Gravado a Tasa Mínima
 *   3: Gravado a Tasa Básica
 *   4: Gravado a Otra Tasa/IVA sobre fictos
 *   6: Producto o servicio no facturable
 *   7: Producto o servicio no facturable negativo
 *   10: Exportación y asimiladas
 *   11: Impuesto percibido
 *   12: IVA en suspenso
 *   13: Sólo para e-Boleta de entrada y sus notas de corrección: Ítem vendido
 *       por un no contribuyente (valida que A-C60≠2)
 *   14: Sólo para e-Boleta de entrada y sus notas de corrección: Ítem vendido
 *       por un contribuyente IVA mínimo, Monotributo o Monotributo MIDES
 *       (valida que A-C60=2)
 *   15: Sólo para e-Boleta de entrada y sus notas de corrección: Ítem vendido
 *       por un contribuyente IMEBA (valida que A-C60=2)
 *   16: Sólo para ítems vendidos por contribuyentes con obligación IVA mínimo,
 *       Monotributo o Monotributo MIDES, excepto si se trata de comprobantes
 *       de ventas por cuenta ajena
 *   17. Sólo para e-Boleta de entrada y sus notas de corrección si se trata de
 *       compra de moneda extranjera par
 */

public enum ItemBillingIndicator {
    EXENTO_DE_IVA,
    GRAVADO_A_TASA_BASICA;

    public static ItemBillingIndicator valueOf(int value) {
        switch (value) {
            case 1:
                return EXENTO_DE_IVA;
            default:
                return GRAVADO_A_TASA_BASICA;
        }
    }

    public int getCode() {
        switch (this) {
            case EXENTO_DE_IVA:
                return 1;
            case GRAVADO_A_TASA_BASICA:
            default:
                return 3;
        }
    }
}
