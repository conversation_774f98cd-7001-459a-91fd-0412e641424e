package models;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import domains.billing.dto.Branch;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import services.bill.CFERecipient;

@Entity
public class Unit extends Model<Unit> implements CFERecipient, Comparable<Unit> {

    /**
     *
     */
    private static final long serialVersionUID = 521321649324009350L;

    @Id
    @GeneratedValue
    private int id;

    private String tower;

    private String number;

    @OneToOne(cascade = { CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REFRESH })
    private User owner;

    private String contact;

    private String name;

    private String rut;

    private boolean accredited;

    private Date blockedAt;

    @OneToMany(cascade = { CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REFRESH })
    private List<Card> assignedCards;

    @OneToOne
    @JoinTable(
        name = "Building_Unit",
        joinColumns = @JoinColumn(name = "unit_id"),
        inverseJoinColumns = @JoinColumn(name = "building_id")
    )
    private Building building;

    @OneToMany(mappedBy = "unit")
    private List<CardEvent> cardEvents;

    public static Finder<Integer, Unit> find = new Finder<Integer, Unit>(Integer.class, Unit.class);

    public Unit() {
        this.assignedCards = new ArrayList<>();
    }

    public Unit(String number, String tower, String ownerEmail) {
        this();
        this.number = number;
        this.tower = tower;
        this.contact = ownerEmail;

        User user = User.findByEmailAddress(ownerEmail);
        if (user != null) {
            this.owner = user;
        }
    }

    public List<CardEvent> getCardReplacements(Date from, Date to) {
        return CardEvent.find(
            from,
            to,
            -1,
            -1,
            getId(),
            null,
            null,
            CardEventType.CARD_ASSIGNED_BILLABLE
        );
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public User getOwner() {
        return owner;
    }

    public void setOwner(User owner) {
        this.owner = owner;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public List<Card> getAssignedCards() {
        return assignedCards;
    }

    public List<String> getAssignedUUID(User owner) {
        ArrayList<String> uuids = new ArrayList<String>();

        for (Card c : this.assignedCards) {
            if (c.getUnit().getId() == this.getId()) {
                if (this.getOwner().getId() == owner.getId()) {
                    uuids.add(c.getUuid() + "-" + c.getAlias() + "-" + c.getBalance());
                }
            }
        }

        return uuids;
    }

    public void setAssignedCards(List<Card> assignedCards) {
        this.assignedCards = assignedCards;
    }

    public String getTower() {
        return tower;
    }

    public void setTower(String tower) {
        this.tower = tower;
    }

    public Building getBuilding() {
        return building;
    }

    public Double getRatePriceCostumer() {
        Double rate = 0.0;
        if (building != null && building.getRate() != null) {
            rate = building.getRate().getPriceCustomer(new Date());
        }
        return rate;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public String getRut() {
        return rut;
    }

    public void setRut(String rut) {
        this.rut = rut;
    }

    public Boolean isAccredited() {
        return this.accredited;
    }

    public void setAccredited(Boolean accredited) {
        this.accredited = accredited;
    }

    public Date getBlockedAt() {
        return blockedAt;
    }

    public void setBlockedAt(Date blocked_date) {
        this.blockedAt = blocked_date;
    }

    public static int getTowerCountByBulding(int buildingId) {
        DefaultQuery<Unit> query = (DefaultQuery<Unit>) find.query();
        query.join("building").eq("building.id", buildingId);

        return (int) query.findDistinctRowCount("tower");
    }

    public static Unit getUnitByBuildingAndNumber(int buildingId, String unitNumber, String tower) {
        DefaultQuery<Unit> query = (DefaultQuery<Unit>) find.query();
        query.eq("number", unitNumber);
        query.eq("tower", tower);
        query.join("building").eq("building.id", buildingId);

        List<Unit> results = query.findList();
        if (results.size() > 0) return results.get(0);

        return null;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!Unit.class.isAssignableFrom(obj.getClass())) return false;
        return ((Unit) obj).getId() == this.getId();
    }

    @Override
    public int compareTo(Unit u) {
        String localTower = "";
        String uTower = "";
        try {
            localTower = this.getTower();
            uTower = u.getTower();
        } catch (Exception e) {}

        if (localTower.equals(uTower)) {
            int localNumber = 0;
            int uNumber = 0;
            try {
                localNumber = Integer.parseInt(this.getNumber());
            } catch (Exception e) {
                localNumber = 0;
            }

            try {
                uNumber = Integer.parseInt(u.getNumber());
            } catch (Exception e) {
                uNumber = 0;
            }

            return (localNumber < uNumber) ? -1 : (localNumber > uNumber) ? 1 : 0;
        } else return localTower.compareTo(uTower); // return (localTower < uTower) ? -1 : (localTower > uTower) ? 1 : 0;
    }

    @Override
    public int getTipoDoc() {
        return hasRut() ? 2 : -1;
    }

    @Override
    public String getBillingName() {
        return (
            (name != null ? name + " - " : "") +
            getBuilding().getName() +
            " - unidad " +
            getNumber()
        );
    }

    public String getFullName() {
        return getNumber() + "/" + getTower();
    }

    @Override
    @Transient
    public String getDoc() {
        return hasRut() ? getRut() : "";
    }

    @Override
    public boolean hasRut() {
        return getRut() != null;
    }

    @Override
    public String getDireccion() {
        String buildingAddress = this.getBuilding().getAddress();
        String address = buildingAddress + " / " + this.number;

        if (address.length() > 70) {
            String separator = "... / ";
            return (
                buildingAddress.substring(0, 70 - separator.length() - this.number.length()) +
                separator +
                this.number
            );
        }

        return address;
    }

    @Override
    public String getCiudad() {
        return getBuilding().getCity();
    }

    @Override
    public String getDepartamento() {
        return getBuilding().getDepartment();
    }

    @Override
    public String getPais() {
        return getBuilding().getPais();
    }

    @Override
    public String getCodPais() {
        return getBuilding().getCodPais();
    }

    public List<CardEvent> getCardEvents() {
        return cardEvents;
    }

    public void setCardEvents(List<CardEvent> cardEvents) {
        this.cardEvents = cardEvents;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static List<Unit> getAccreditatedUnitsByOwnerId(User user) {
        DefaultQuery<Unit> q = (DefaultQuery<Unit>) find.query();
        q.join("owner").eq("owner.id", user.getId());
        q.eq("accredited", true);

        return q.findList();
    }

    public static Unit getAccreditatedUnitsByBuildingAndOwner(Building building, User user) {
        DefaultQuery<Unit> query = (DefaultQuery<Unit>) find.query();
        query.join("building").eq("building.id", building.getId());
        query.join("owner").eq("owner.id", user.getId());
        query.eq("accredited", true);

        return query.findUnique();
    }

    public static Unit findById(int id) {
        return find.byId(id);
    }

    public void block() {
        this.setBlockedAt(new Date());
        this.setAccredited(false);
    }

    @Override
    public Branch getBranch() {
        return this.getBuilding().getBranch();
    }
}
