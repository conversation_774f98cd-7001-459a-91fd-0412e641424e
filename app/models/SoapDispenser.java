package models;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Date;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToOne;
import org.hibernate.criterion.Restrictions;

@Entity
public class SoapDispenser extends Model<SoapDispenser> implements Comparable<SoapDispenser> {

    @Id
    @GeneratedValue
    private int id;

    private Date creationDate;

    private Date replenishDate;

    private String model;

    private String description;

    @OneToOne(cascade = { CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REFRESH })
    private Machine machine;

    private int uses;

    public static Finder<Integer, SoapDispenser> find = new Finder<Integer, SoapDispenser>(
        Integer.class,
        SoapDispenser.class
    );

    public SoapDispenser() {
        this.creationDate = new Date();
        this.replenishDate = new Date();
        this.uses = 0;
    }

    public SoapDispenser(String model, Machine machine, String desc) {
        this.creationDate = new Date();
        this.machine = machine;
        this.model = model;
        this.description = desc;
        this.uses = 0;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Machine getMachine() {
        return this.machine;
    }

    public void setMachine(Machine machine) {
        this.machine = machine;
    }

    public void setModel(String m) {
        this.model = m;
    }

    public String getModel() {
        return this.model;
    }

    public void setDescription(String d) {
        this.description = d;
    }

    public String getDescription() {
        return this.description;
    }

    public void setUses(int u) {
        this.uses = u;
    }

    public int getUses() {
        return this.uses;
    }

    public Date getCreationDate() {
        return this.creationDate;
    }

    public Date getReplenishDate() {
        return this.replenishDate;
    }

    public void setReplenishDate(Date date) {
        this.replenishDate = date;
    }

    public void addUseToCounter() {
        this.uses = this.uses + 1;
    }

    public static SoapDispenser findByMachineId(Machine m) {
        try {
            DefaultQuery<SoapDispenser> q = (DefaultQuery<SoapDispenser>) find.query();

            q
                .getCriteria()
                .createAlias("machine", "machine")
                .add(Restrictions.eq("machine.id", m.getId()));

            List<SoapDispenser> results = q.findList();
            if (results.size() > 0) return results.get(0);
        } catch (Exception e) {}

        return null;
    }

    public static SoapDispenser findById(int id) {
        return find.byId(id);
    }

    public static List<SoapDispenser> findAll() {
        return find.all();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!SoapDispenser.class.isAssignableFrom(obj.getClass())) return false;
        return ((SoapDispenser) obj).getId() == this.getId();
    }

    @Override
    public int compareTo(SoapDispenser o) {
        if (o.id > this.id) return 1; else if (o.id < this.id) return -1; else return 0;
    }
}
