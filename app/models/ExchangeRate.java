package models;

import java.util.Date;
import javax.persistence.*;

@Entity
@Table(indexes = { @Index(name = "CURRENCY_ISO_CODE", columnList = "currencyIsoCode") })
public class ExchangeRate extends BaseModel<ExchangeRate> {

    @Id
    @GeneratedValue
    private int id;

    private String currencyIsoCode;
    private double value;
    private Date updatedAt;

    public ExchangeRate() {}

    public ExchangeRate(String currencyIsoCode) {
        this.currencyIsoCode = currencyIsoCode;
    }

    @Override
    public int getId() {
        return this.id;
    }

    public String getCurrencyIsoCode() {
        return currencyIsoCode;
    }

    public double getValue() {
        return value;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setValue(double value) {
        this.value = value;
        this.updatedAt = new Date();
    }
}
