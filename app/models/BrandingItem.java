package models;

import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import javax.persistence.*;

@Entity
public class BrandingItem extends Model<BrandingItem> {

    @Id
    @GeneratedValue
    private int id;

    private String name;

    @Column(columnDefinition = "TEXT")
    private String description;

    private String imageName;
    private String reference;

    @OneToOne(cascade = { CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REFRESH })
    public Rate rate;

    private static Finder<Integer, BrandingItem> find = new Finder<Integer, BrandingItem>(
        Integer.class,
        BrandingItem.class
    );

    public static List<BrandingItem> findAll() {
        return find.all();
    }

    public static BrandingItem findById(int brandingItemId) {
        return find.byId(brandingItemId);
    }

    public int getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Rate getRate() {
        return this.rate;
    }

    public void setRate(Rate rate) {
        this.rate = rate;
    }

    public Double getPriceCustomer() throws APIException {
        if (this.rate == null) throw APIException.raise(APIErrors.RATE_NOT_FOUND);

        return this.rate.getPriceCustomer(null);
    }

    public String getImageName() {
        return this.imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public String getReference() {
        return this.reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }
}
