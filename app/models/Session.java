package models;

import com.play4jpa.jpa.db.Db;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

@Entity
public class Session extends Model<Session> {

    /**
     *
     */
    private static final long serialVersionUID = -6111608082703517322L;

    @Id
    private String token;

    private Date creationTimestamp;
    private Date lastAccess;
    private String ip;
    private String userAgent;
    private boolean valid;

    @ManyToOne(fetch = FetchType.LAZY)
    private User user;

    public Session(User user, String ip, String userAgent) {
        this.token = UUID.randomUUID().toString();
        this.user = user;
        this.creationTimestamp = new Date();
        this.valid = true;
        this.ip = ip;
        this.userAgent = userAgent;
    }

    public Session() {}

    public static Finder<String, Session> find = new Finder<String, Session>(
        String.class,
        Session.class
    );

    public static User findUserByAuthToken(String authToken) {
        Session session = findByAuthToken(authToken);

        return session != null ? session.user : null;
    }

    public static Session findByAuthToken(final String authToken) {
        if (authToken == null) {
            return null;
        }

        try {
            return find.query().eq("token", authToken).findUnique();
        } catch (Exception e) {
            return null;
        }
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Date getCreationTimestamp() {
        return creationTimestamp;
    }

    public void setCreationTimestamp(Date creationTimestamp) {
        this.creationTimestamp = creationTimestamp;
    }

    public Date getLastAccess() {
        return lastAccess;
    }

    public void setLastAccess(Date lastAccess) {
        this.lastAccess = lastAccess;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public Boolean isValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
