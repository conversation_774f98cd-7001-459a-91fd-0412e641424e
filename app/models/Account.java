package models;

import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import com.play4jpa.jpa.query.Query;
import global.APIException;
import global.APIException.APIErrors;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToOne;
import play.i18n.Messages;

@Entity
public class Account extends Model<Account> {

    /**
     *
     */
    private static final long serialVersionUID = 512870623865216657L;

    @Id
    @GeneratedValue
    private int id;

    private Date creationTimestamp;
    private Date validationDate;
    private Boolean validated = false;
    private String resetKey = "";
    private String validationKey = "";
    private boolean waitingForReset = false;
    private boolean waitingForValidation = false;
    // push notifications
    private String expo_device_token;

    @OneToOne
    private User owner;

    public Account() {}

    public static Finder<Integer, Account> find = new Finder<Integer, Account>(
        Integer.class,
        Account.class
    );

    public static Account findById(Integer id) {
        return find.byId(id);
    }

    public static Account findById(Integer id, boolean throwExceptionWhenMissing)
        throws APIException {
        Account account = find.byId(id);

        if (account == null && throwExceptionWhenMissing) throw APIException
            .raise(APIErrors.ACCOUNT_NOT_FOUND)
            .setDetailMessage(Messages.get("account_not_found", id));

        return account;
    }

    public static List<Account> findAll() {
        return find.all();
    }

    public static Account findByUUID(String uuid) {
        return Account.query().eq("uuid", uuid).findUnique();
    }

    public static Account findByToken(String token) {
        return Account.query().eq("expo_device_token", token).findUnique();
    }

    public static Query<Account> query() {
        return find.query();
    }

    public static Account findByOwnerId(int ownerId) {
        return Account.query().join("owner").eq("owner.id", ownerId).findUnique();
    }

    public static List<Account> findByOEM(Long oem_id) {
        return Account.query().join("oem").eq("oem.id", oem_id).findList();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Date getCreationTimestamp() {
        return creationTimestamp;
    }

    public void setCreationTimestamp(Date creationTimestamp) {
        this.creationTimestamp = creationTimestamp;
    }

    public User getOwner() {
        return owner;
    }

    public void setOwner(User owner) {
        this.owner = owner;
    }

    public Date getValidationDate() {
        return validationDate;
    }

    public void setValidationDate(Date validationDate) {
        this.validationDate = validationDate;
    }

    public Boolean getValidated() {
        return validated;
    }

    public void setValidated(Boolean validated) {
        this.validated = validated;
    }

    public String getValidationKey() {
        return validationKey;
    }

    public void setValidationKey(String validationKey) {
        this.validationKey = validationKey;
    }

    public boolean isWaitingForValidation() {
        return waitingForValidation;
    }

    public void setWaitingForValidation(boolean waitingForValidation) {
        this.waitingForValidation = waitingForValidation;
    }

    public String getResetKey() {
        return resetKey;
    }

    public void setResetKey(String resetKey) {
        this.resetKey = resetKey;
    }

    public boolean isWaitingForReset() {
        return waitingForReset;
    }

    public void setWaitingForReset(boolean waitingForReset) {
        this.waitingForReset = waitingForReset;
    }

    public String getExpoDeviceToken() {
        return expo_device_token;
    }

    public void setExpoDeviceToken(String token) {
        this.expo_device_token = token;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!Account.class.isAssignableFrom(obj.getClass())) return false;
        return ((Account) obj).getId() == this.getId();
    }

    /*
     * ===== Actions =====
     */

    public String waitForPasswordReset() {
        String key = UUID.randomUUID().toString();
        this.resetKey = key;
        this.waitingForReset = true;
        this.update();

        return key;
    }

    public String waitForValidation() {
        String key = UUID.randomUUID().toString();
        this.validationKey = key;
        this.waitingForValidation = true;

        return key;
    }

    public void validate() {
        this.setValidated(true);
        this.setValidationDate(new Date());
        this.setWaitingForValidation(false);

        this.update();
    }

    /**
     * Replace all people personal closureData and run saving action
     */
    public void anonymize() {
        this.creationTimestamp = null;
        this.validationDate = null;
        this.validated = false;
        this.resetKey = "";
        this.validationKey = "";
        this.waitingForReset = false;
        this.waitingForValidation = false;
        this.expo_device_token = "";

        this.update();
    }
}
