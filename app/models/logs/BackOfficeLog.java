package models.logs;

import com.play4jpa.jpa.models.Model;
import global.BackOfficeCopEntity;
import models.User;

import javax.persistence.*;
import java.util.Date;

@Entity
public class BackOfficeLog extends Model<BackOfficeLog> {

    @Id
    @GeneratedValue
    private int id;

    /**
     * Entity class name
     */
    private String fromClass;
    /**
     * Entity id
     */
    private int fromId;
    /**
     * operation performed over Entity
     */
    private String operation;

    /**
     * HTTP method
     */
    private String method;
    /**
     * request path
     */
    private String path;
    /**
     * request query string
     */
    @Column(columnDefinition = "TEXT")
    private String queryString;
    /**
     * request body
     */
    @Column(columnDefinition = "TEXT")
    private String body;
    /**
     * authenticated user email
     */
    private String user;
    /**
     * authenticated user role
     */
    private String role;
    /**
     * request user agent
     */
    private String userAgent;
    /**
     * code action path
     */
    private String actionPath;
    /**
     * custom action name.
     * Use this field to make it more clear what action is being performed.
     */
    private String actionName;
    /**
     * custom action description.
     * Use this field to make it more clear what action is being performed.
     */
    private String actionDescription;

    private Date createdAt;

    @Transient
    private boolean internalCreation;

    @Override
    protected void preSave() {
        if (!this.internalCreation) {
            // disable creating any record outside this class
            throw new UnsupportedOperationException();
        }

        super.preSave();
    }

    @Override
    protected void preUpdate() {
        // disable updating any record
        throw new UnsupportedOperationException();
    }

    @Override
    protected void preDelete() {
        // disable deleting any record
        throw new UnsupportedOperationException();
    }

    /**
     *
     * @param entity            which is being tracked
     * @param operation         Create, Update or Delete name
     * @param method            HTTP method
     * @param path              request path
     * @param queryString       request query string
     * @param body              request body
     * @param user              authenticated user
     * @param userAgent         request user agent
     * @param actionPath        code action path
     * @param actionName        custom action name
     * @param actionDescription custom action description
     */
    public static <T extends BackOfficeCopEntity> void log(
            T entity,
            String operation,
            String method,
            String path,
            String queryString,
            String body,
            User user,
            String userAgent,
            String actionPath,
            String actionName,
            String actionDescription) {
        BackOfficeLog record = new BackOfficeLog();
        record.internalCreation = true;

        record.fromClass = entity.getClass().getName();
        record.fromId = entity.getId();
        record.operation = operation;
        record.method = method;
        record.path = path;
        record.queryString = queryString;
        record.body = body;
        record.user = user.getEmailAddress();
        record.role = user.getRole().name();
        record.userAgent = userAgent;
        record.actionPath = actionPath;
        record.actionName = actionName;
        record.actionDescription = actionDescription;
        record.createdAt = new Date();

        record.save();
    }
}
