package services.machine;

import dto.report.UsageReportParameters;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import models.*;
import models.ReportRequestHistory.ReportRequestState;
import play.db.jpa.JPA;
import queries.transactions.TransactionQuery;
import services.BaseService;
import utils.MachineHelper;
import utils.NumberHelper;
import utils.WebReportWrapper;
import utils.WebReportWrapper.WebReportItemWrapper;
import utils.email.EmailService;
import views.html.usesReport;
import views.html.usesReportError;

public class MachineUsesReportService extends BaseService {

    private String cardUuid;
    private String email;
    private String htmlBody;
    private List<WebReportWrapper> webReportWrapped;
    private Date from;
    private Date until;

    public MachineUsesReportService(String cardUId, String email) {
        this.cardUuid = cardUId;
        this.email = email;
        this.htmlBody = "";
        this.webReportWrapped = new ArrayList<>();
    }

    public MachineUsesReportService(UsageReportParameters params) {
        this(params.getCardUid(), params.getEmail());
        this.from = params.getFrom();
        this.until = params.getUntil();
    }

    public void sendReport() {
        try {
            JPA.withTransaction(() -> {
                ReportRequestHistory reportRequestHistory = new ReportRequestHistory(
                    this.email,
                    this.cardUuid
                );

                Card card = Card.findByUID(this.cardUuid);
                if (card == null) {
                    ReportRequest reportRequest = ReportRequest.findByEmailAddress(email);
                    if (reportRequest == null) {
                        reportRequest = new ReportRequest(this.email, 1);
                        reportRequest.save();
                    } else {
                        reportRequest.setCountErrors(reportRequest.getCountErrors() + 1);
                        reportRequest.update();
                    }

                    reportRequestHistory.setState(ReportRequestState.ERROR);
                    reportRequestHistory.setReportRequest(reportRequest);
                    reportRequestHistory.save();

                    loggerError("Send Report Result: UId not found");
                    return;
                }

                boolean canSend = ReportRequest.canSendReport(this.email);
                if (canSend) {
                    Double balance = card.getContractType() == Card.ContractType.PREPAID
                        ? card.getBalance()
                        : null;
                    Integer closureDayOfBuilding = getClosureDayOfBuilding(card);
                    Date lastMachineUpdate = MachineHelper.getLastMachineUpdateByCard(card);
                    this.htmlBody =
                        getBodyReport(
                            card,
                            this.email,
                            lastMachineUpdate,
                            balance,
                            closureDayOfBuilding,
                            this.from,
                            this.until
                        );

                    reportRequestHistory.setState(ReportRequestState.OK);
                } else {
                    String cleanedCardUuid = card.getCleanUuid();
                    this.htmlBody = usesReportError.render(this.email, cleanedCardUuid).body();

                    reportRequestHistory.setState(ReportRequestState.BLOCKED);
                }

                ReportRequest reportRequest = ReportRequest.findByEmailAddress(this.email);
                if (reportRequest == null) {
                    reportRequest = new ReportRequest(this.email, 0);
                    reportRequest.save();
                }

                reportRequestHistory.setReportRequest(reportRequest);
                reportRequestHistory.save();

                EmailService.sendAsync(this.email, "Reporte de usos", this.htmlBody);
            });
        } catch (Throwable e) {
            loggerError("Send Report error while parallel action was running");
            e.printStackTrace();
        }
    }

    public String getHTMLBody() {
        return EmailService.doReplaces(htmlBody);
    }

    public List<WebReportItemWrapper> getReportItems(int limit) {
        if (limit == 0) return new ArrayList<>();

        return this.webReportWrapped.stream()
            .flatMap(r -> r.getItems().stream())
            .limit(limit)
            .collect(Collectors.toList());
    }

    private Integer getClosureDayOfBuilding(Card card) {
        try {
            Integer day = card.getUnit().getBuilding().getAdministration().getClosureDay();
            return day == 0 ? 1 : day;
        } catch (Exception t) {
            return 1;
        }
    }

    private LocalDate convertToLocalDateViaInstant(Date dateToConvert) {
        return dateToConvert.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    private Integer getMonthValueFromDate(Date date, Integer adminClosureDay) {
        LocalDate adjustedDate = adjustDateByAdminClosureDay(date, adminClosureDay);
        return adjustedDate.getMonthValue() - 1;
    }

    private LocalDate adjustDateByAdminClosureDay(Date from, Integer adminClosureDay) {
        LocalDate localDate = convertToLocalDateViaInstant(from);

        // if the number of the day is less than the adminClosureDay, then return the previous month
        // those uses are from the previous period
        if (localDate.getDayOfMonth() < adminClosureDay) {
            return localDate.minusMonths(1);
        }

        return localDate;
    }

    private boolean needsToAdjustYear(boolean isCardPostpaid, int adminClosureDay, int month) {
        return isCardPostpaid && adminClosureDay > 1 && month == 11;
    }

    private String getBodyReport(
        Card card,
        String user,
        Date lastMachineUpdate,
        Double balance,
        Integer closureDayOfBuilding,
        Date from,
        Date to
    ) {
        // get all uses
        List<MachineUse> uses = MachineUse.find(
            from,
            to,
            0,
            0,
            0,
            card.getUuid(),
            "",
            MachineUse.ACCREDITED_RESULT
        );

        if (lastMachineUpdate != null) {
            uses =
                uses
                    .stream()
                    .filter(x -> x.getTimestamp().before(lastMachineUpdate))
                    .collect(Collectors.toList());
        }

        // grouping uses
        Map<Integer, List<MachineUse>> usesByMonth = uses
            .stream()
            .collect(
                Collectors.groupingBy(x ->
                    getMonthValueFromDate(x.getTimestamp(), closureDayOfBuilding)
                )
            );

        // get all transactions
        List<Transaction> transactions = new TransactionQuery()
            .filterByCreationDate(from, to)
            .filterByCardUid(card.getUuid())
            .filterByErrorCode(Transaction.ErrorCode.OK)
            .find();

        if (lastMachineUpdate != null) {
            transactions =
                transactions
                    .stream()
                    .filter(x -> x.getCreationDate().before(lastMachineUpdate))
                    .collect(Collectors.toList());
        }

        // grouping transactions
        Map<Integer, List<Transaction>> transactionsByMonth = transactions
            .stream()
            .collect(Collectors.groupingBy(x -> x.getCreationDate().getMonth()));

        List<WebReportWrapper> webReportWrapped = new ArrayList<>();

        // Merge uses and transactions by month
        for (Integer month : usesByMonth.keySet()) {
            List<MachineUse> usesMonth = usesByMonth.getOrDefault(month, new ArrayList<>());
            List<Transaction> transactionsMonth = transactionsByMonth.getOrDefault(
                month,
                new ArrayList<>()
            );

            int year = usesMonth.stream().findFirst().isPresent()
                ? usesMonth.stream().findFirst().get().getTimestamp().getYear()
                : new Date().getYear();

            // edge case: POSTPAID card, closure day of building is > 1 and on one period has two years
            if (needsToAdjustYear(card.isPostpaid(), closureDayOfBuilding, month)) {
                year -= 1;
            }

            double monthTotal = usesMonth.stream().mapToDouble(MachineUse::getPriceCustomer).sum();
            WebReportWrapper webReportWrapper = new WebReportWrapper(
                year,
                month,
                monthTotal,
                closureDayOfBuilding,
                card.isPostpaid()
            );
            webReportWrapper.addMachineUses(usesMonth);
            webReportWrapper.addTransactions(transactionsMonth);

            webReportWrapped.add(webReportWrapper);

            webReportWrapper.sortItemsByTimestamp();
        }

        // Sort webReportWrapped by month and year
        webReportWrapped.sort(
            Comparator
                .comparingInt(WebReportWrapper::getYear)
                .thenComparingInt(WebReportWrapper::getDate)
                .reversed()
        );

        String uid = card.getCleanUuid();
        String dateFormatted = "Sin especificar";
        if (lastMachineUpdate != null) {
            dateFormatted = new SimpleDateFormat("dd-MM-yyyy").format(lastMachineUpdate);
        }

        String balanceFormatted = "";
        if (balance != null) {
            balanceFormatted = "$ " + NumberHelper.formatDoubleWithSeparators(balance);
        }

        return usesReport
            .render(webReportWrapped, user, uid, dateFormatted, balanceFormatted)
            .body();
    }
}
