package services.card;

import models.Card;
import models.Unit;
import models.User;
import org.apache.commons.lang3.StringUtils;
import services.BaseService;

public class AssignationUserService extends BaseService {

    private final Card card;
    private final User user;

    public AssignationUserService(Card card, User user) {
        this.card = card;
        this.user = user;
    }

    /**
     * Assign user as the card owner.
     * It could be through the unit or directly through the card depending on
     * whether the card is assigned to a unit or not
     */
    public void assign() {
        this.assign(null);
    }

    /**
     * @see AssignationUserService#assign()
     * @param alias the alias to assign to the card
     */
    public void assign(String alias) {
        assignThroughUnit();
        assignThroughCard();

        if (StringUtils.isNotBlank(alias)) {
            this.card.setAlias(alias);
            this.card.update();
        }
    }

    /**
     * Unassign user from the card.
     */
    public void unassign() {
        this.unassignThroughUnit();
        this.unassignThroughCard();
    }

    private void assignThroughUnit() {
        Unit unit = this.card.getUnit();
        if (unit == null) return;

        unit.setOwner(this.user);
        unit.update();
    }

    private void unassignThroughUnit() {
        Unit unit = this.card.getUnit();
        if (unit == null) return;

        unit.setOwner(null);
        unit.update();
    }

    private void assignThroughCard() {
        this.card.setPrePaidCardholder(this.user);
        this.card.update();
    }

    private void unassignThroughCard() {
        this.card.setPrePaidCardholder(null);
        this.card.update();
    }
}
