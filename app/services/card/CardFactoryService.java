package services.card;

import dto.card.UpdateCardParameters;
import global.APIException;
import javax.persistence.PersistenceException;
import models.Card;
import models.Part;
import models.User;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.exception.ConstraintViolationException;
import services.BaseService;

public class CardFactoryService extends BaseService {

    public void updateCard(UpdateCardParameters params) throws APIException {
        Card card = params.getCard();

        if (StringUtils.isNotBlank(params.getUid())) {
            card.setUuid(params.getUid());
        }

        if (params.getMaster() != null) {
            card.setMaster(params.getMaster());
        }

        if (params.getContractType() != null) {
            card.setContractType(params.getContractType());
        }

        if (params.getStartTime() != null) {
            card.setStartTimeOfUse(params.getStartTime());
        }

        if (params.getEndTime() != null) {
            card.setEndTimeOfUse(params.getEndTime());
        }

        if (params.getDiscount() != null) {
            card.setDiscount(params.getDiscount());
        }

        try {
            card.update();
        } catch (PersistenceException ex) {
            if (ex.getCause() instanceof ConstraintViolationException) {
                throw APIException
                    .raise(APIException.APIErrors.CARD_UUID_ALREADY_EXISTS)
                    .setDetailMessage(
                        "The uid \"" + params.getUid() + "\" belongs to another card."
                    );
            } else {
                loggerError("Error while updating card - error: {}", ex.getMessage());
                throw ex;
            }
        }
    }

    /**
     * Create the card for users who come from app-mobile
     */
    public Card createRegularUserVirtualCard(User user) {
        Card card = new Card();

        card.setUuid(Card.generateVirtualUuid());
        card.setState(Part.PartState.ACTIVE);
        card.setPrePaidCardholder(user);
        card.setContractType(Card.ContractType.PREPAID);

        card.save();

        return card;
    }
}
