package services;

import domains.billing.DiscountService;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import models.*;
import models.Machine.MachineType;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONException;
import org.json.JSONObject;
import play.db.jpa.JPA;
import services.building.ColivingBuildingService;
import utils.CardHelper;

public class ReportService {

    public static HashMap<String, List<? extends BillableItem>> getReportData(
        Date from,
        Date to,
        String accountIdString,
        int machineId,
        int buildingId,
        int unitId,
        String cardUId,
        String keyword
    ) {
        return getReportData(
            false,
            from,
            to,
            accountIdString,
            machineId,
            buildingId,
            unitId,
            cardUId,
            keyword
        );
    }

    public static HashMap<String, List<? extends BillableItem>> getReportData(
        boolean includeDisabledUses,
        Date from,
        Date to,
        String accountIdString,
        int machineId,
        int buildingId,
        int unitId,
        String cardUId,
        String keyword
    ) {
        HashMap<String, List<? extends BillableItem>> result = new HashMap<>();

        int accountId = -1;
        boolean unitFound = false;

        if (accountIdString != null) {
            accountId = Integer.parseInt(accountIdString);
            Account a = Account.findById(accountId);

            outerloop:for (Building building : Building.findAll()) {
                for (Unit unit : building.getUnits()) {
                    if (unit.getOwner() != null && unit.getOwner().equals(a.getOwner())) {
                        buildingId = building.getId();
                        unitId = unit.getId();
                        unitFound = true;
                        break outerloop;
                    }
                }
            }
        } else {
            unitFound = true;
        }

        if (!unitFound) {
            return new HashMap<>();
        }

        Building building = Building.findById(buildingId);

        List<MachineUse> uses;
        if (building != null && building.getPrepaidRechargeableUses() > 0) {
            uses =
                MachineUse.find(
                    from,
                    to,
                    buildingId,
                    machineId,
                    unitId,
                    cardUId,
                    keyword,
                    MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE,
                    MachineUseResult.POSTPAID_ACTIVATION,
                    MachineUseResult.POSTPAID_ACTIVATION_PRE_BLOCKED_USE_WITH_BALANCE
                );
        } else {
            uses =
                MachineUse.find(
                    from,
                    to,
                    buildingId,
                    machineId,
                    unitId,
                    cardUId,
                    keyword,
                    MachineUseResult.POSTPAID_ACTIVATION,
                    MachineUseResult.POSTPAID_ACTIVATION_PRE_BLOCKED_USE_WITH_BALANCE
                );
        }

        if (includeDisabledUses) {
            uses.addAll(
                MachineUse.find(
                    from,
                    to,
                    buildingId,
                    machineId,
                    unitId,
                    cardUId,
                    keyword,
                    MachineUseResult.POSTPAID_NOT_ACCREDITED,
                    MachineUseResult.POSTPAID_ACTIVATION_PRE_BLOCKED_USE_WITHOUT_BALANCE
                )
            );
        }

        List<CardEvent> cards = CardEvent.find(
            from,
            to,
            buildingId,
            machineId,
            unitId,
            cardUId,
            keyword,
            CardEventType.CARD_ASSIGNED_BILLABLE
        );

        result.put("uses", uses);
        result.put("cards", cards);

        return result;
    }

    public static byte[] generateExcel(
        Building building,
        Date from,
        Date to,
        String accountIdString,
        int machineId,
        int unitId,
        String cardUId,
        String keyword
    ) {
        HashMap<String, List<? extends BillableItem>> items = getReportData(
            from,
            to,
            accountIdString,
            machineId,
            building.getId(),
            unitId,
            cardUId,
            keyword
        );

        List<MachineUse> uses = items.containsKey("uses")
            ? (List<MachineUse>) items.get("uses")
            : new ArrayList<>();
        List<CardEvent> cards = items.containsKey("cards")
            ? (List<CardEvent>) items.get("cards")
            : new ArrayList<>();

        return generateExcel(building, from, to, uses, cards);
    }

    public static byte[] generateExcel(
        Building building,
        Date from,
        Date to,
        List<MachineUse> uses,
        List<CardEvent> newCardEvents
    ) {
        ColivingBuildingService colivingBuildingService = new ColivingBuildingService();
        DateTimeFormatter dateFormat = DateTimeFormat.forPattern("dd/MM/YYYY HH:mm");

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet summarySheet = workbook.createSheet("Resumen");
        HSSFSheet detailSheet = workbook.createSheet("Detalle");

        Rate specialRate = null;

        summarySheet.setDefaultColumnWidth(30);
        summarySheet.setDefaultRowHeightInPoints(20);

        detailSheet.setDefaultColumnWidth(50);
        detailSheet.setDefaultRowHeightInPoints(20);

        HSSFRow summaryTitleRow = summarySheet.createRow((short) 0);
        HSSFRow detailTitleRow = detailSheet.createRow((short) 0);
        HSSFRow summaryHeaderRow = summarySheet.createRow((short) 1);

        // HEADERS
        summaryTitleRow.createCell(0).setCellValue("*** " + building.getName() + " ***");
        detailTitleRow.createCell(0).setCellValue("*** " + building.getName() + " ***");
        summaryHeaderRow.createCell(0).setCellValue("Unidad");

        int minUsesPerUnit = building.getRate().getMinUsesPerUnit(from);

        if (building.isColiving()) {
            summaryHeaderRow.createCell(1).setCellValue("Cuenta asociada a la UID");
            summaryHeaderRow.createCell(2).setCellValue("Usos Realizados");

            if (minUsesPerUnit > 0) {
                summaryHeaderRow.createCell(3).setCellValue("Usos por cobro min");
                summaryHeaderRow.createCell(4).setCellValue("Tarjetas Adicionales");
                summaryHeaderRow
                    .createCell(5)
                    .setCellValue("Importe a cobrar en Gastos Comunes ($)");
                summaryHeaderRow.createCell(6).setCellValue("Período de facturación");
            } else {
                summaryHeaderRow.createCell(3).setCellValue("Tarjetas Adicionales");
                summaryHeaderRow
                    .createCell(4)
                    .setCellValue("Importe a cobrar en Gastos Comunes ($)");
                summaryHeaderRow.createCell(5).setCellValue("Período de facturación");
            }
        } else {
            summaryHeaderRow.createCell(1).setCellValue("Usos Realizados");

            if (minUsesPerUnit > 0) {
                summaryHeaderRow.createCell(2).setCellValue("Usos por cobro min");
                summaryHeaderRow.createCell(3).setCellValue("Tarjetas Adicionales");
                summaryHeaderRow
                    .createCell(4)
                    .setCellValue("Importe a cobrar en Gastos Comunes ($)");
            } else {
                summaryHeaderRow.createCell(2).setCellValue("Tarjetas Adicionales");
                summaryHeaderRow
                    .createCell(3)
                    .setCellValue("Importe a cobrar en Gastos Comunes ($)");
            }
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd' a las 'HH:mm:ss");

        // ROWS
        short detailsRowIndex = 2;
        short summaryRowIndex = 2;
        List<Unit> units = building
            .getUnits()
            .stream()
            .sorted(Unit::compareTo)
            .collect(Collectors.toList());

        Double reportTotalPriceCompany = 0.0;
        Double reportTotalPriceCardReplacement = 0.0;

        // TODO - Rate: Se obtienen desde el filtro
        Date reportMinDate = null;
        Date reportMaxDate = null;

        if (from != null) {
            reportMinDate = from;
        }

        if (to != null) {
            reportMaxDate = to;
        }

        int minUses = building.adjustToBuildingMinimumUses(reportMinDate);
        int totalUses = 0;
        for (int j = 0; j < units.size(); j++) {
            Unit unit = units.get(j);
            List<MachineUse> usesForUnit = getUsesByUnit(uses, unit);

            HSSFRow summaryRow = summarySheet.createRow(summaryRowIndex);
            HSSFRow detailRow = detailSheet.createRow(detailsRowIndex);

            int usesForUnitCount = usesForUnit.size();
            int usesToReachMinUsesPerUnit = Math.max(minUsesPerUnit - usesForUnitCount, 0);
            totalUses += usesForUnitCount + usesToReachMinUsesPerUnit;

            List<CardEvent> cardsForUnit = getAdditionalCardsByUnit(newCardEvents, unit);
            int cardsForUnitCount = cardsForUnit.size();

            double priceCompany = building.getRate().getPriceCompany(from);

            Double priceCustomerUsesForUnit =
                usesForUnit
                    .stream()
                    .mapToDouble(x ->
                        DiscountService.applyCustomerDiscount(
                            x.getMachine().getMachineRate().getRateEventByDate(x.getTimestamp()),
                            x.getCard()
                        )
                    )
                    .sum() +
                usesToReachMinUsesPerUnit *
                priceCompany;
            Double priceCompanyUsesForUnit =
                usesForUnit
                    .stream()
                    .mapToDouble(x ->
                        DiscountService.applyCompanyDiscount(
                            x.getMachine().getMachineRate().getRateEventByDate(x.getTimestamp()),
                            x.getCard()
                        )
                    )
                    .sum() +
                usesToReachMinUsesPerUnit *
                priceCompany;
            Double priceCardForUnit = cardsForUnit
                .stream()
                .mapToDouble(x -> building.getRate().getPriceCardReplacement(x.getTimestamp()))
                .sum();

            reportTotalPriceCompany += priceCompanyUsesForUnit;
            reportTotalPriceCardReplacement += priceCardForUnit;

            String userPeriodString = "";
            if (building.isColiving()) {
                DateTime[] userPeriod;
                if (unit.getOwner() != null && unit.getOwner().getMasterAccount() != null) {
                    userPeriod =
                        colivingBuildingService.validateUserPeriod(
                            unit.getOwner().getMasterAccount(),
                            to
                        );
                } else {
                    userPeriod = new DateTime[] { new DateTime(from), new DateTime(to) };
                }

                userPeriodString =
                    String.format(
                        "%s - %s",
                        dateFormat.print(
                            userPeriod[0].minusMonths(ColivingBuildingService.backwardBillingPeriod)
                        ),
                        dateFormat.print(userPeriod[1])
                    );
            }

            summaryRow.createCell(0).setCellValue(unit.getTower() + "-" + unit.getNumber());

            String ownerEmail = unit.getOwner() != null ? unit.getOwner().getEmailAddress() : "";

            if (building.isColiving()) {
                summaryRow.createCell(1).setCellValue(ownerEmail);
                summaryRow.createCell(2).setCellValue(usesForUnitCount);

                if (minUsesPerUnit > 0) {
                    summaryRow.createCell(3).setCellValue(usesToReachMinUsesPerUnit);
                    summaryRow.createCell(4).setCellValue(cardsForUnitCount);
                    summaryRow
                        .createCell(5)
                        .setCellValue(priceCustomerUsesForUnit + priceCardForUnit);

                    summaryRow.createCell(6).setCellValue(userPeriodString);
                } else {
                    summaryRow.createCell(3).setCellValue(cardsForUnitCount);
                    summaryRow
                        .createCell(4)
                        .setCellValue(priceCustomerUsesForUnit + priceCardForUnit);
                    summaryRow.createCell(5).setCellValue(userPeriodString);
                }
            } else {
                summaryRow.createCell(1).setCellValue(usesForUnitCount);

                if (minUsesPerUnit > 0) {
                    summaryRow.createCell(2).setCellValue(usesToReachMinUsesPerUnit);
                    summaryRow.createCell(3).setCellValue(cardsForUnitCount);
                    summaryRow
                        .createCell(4)
                        .setCellValue(priceCustomerUsesForUnit + priceCardForUnit);
                } else {
                    summaryRow.createCell(2).setCellValue(cardsForUnitCount);
                    summaryRow
                        .createCell(3)
                        .setCellValue(priceCustomerUsesForUnit + priceCardForUnit);
                }
            }

            detailRow
                .createCell(0)
                .setCellValue(
                    "Unidad " +
                    unit.getTower() +
                    "-" +
                    unit.getNumber() +
                    "/" +
                    ownerEmail +
                    " Usos Realizados: " +
                    usesForUnitCount +
                    (
                        usesToReachMinUsesPerUnit > 0
                            ? ", Ajuste por mínimo: " + usesToReachMinUsesPerUnit
                            : ""
                    ) +
                    (!userPeriodString.isEmpty() ? " (" + userPeriodString + ")" : "") +
                    ", Tarjetas Adicionales: " +
                    cardsForUnitCount
                );

            for (int u = 0; u < usesForUnit.size(); u++) {
                detailsRowIndex += 1;
                MachineUse unitUse = usesForUnit.get(u);
                HSSFRow drowx = detailSheet.createRow(detailsRowIndex);

                Card card = unitUse.getCard();
                String cardUUID = CardHelper.getUserFriendlyUUID(card.getUuid());

                drowx
                    .createCell(0)
                    .setCellValue(
                        (
                            unitUse.getMachine().getMachineType() == MachineType.WASHER
                                ? "    Lavadora: el "
                                : "    Secadora: el "
                        ) +
                        sdf.format(unitUse.getTimestamp()) +
                        ". Tarjeta: " +
                        cardUUID +
                        ". Tarifa: " +
                        unitUse
                            .getMachine()
                            .getMachineRate()
                            .getPriceCustomer(unitUse.getTimestamp())
                    );

                if (unitUse.getMachine().hasSpecialRate()) {
                    specialRate = unitUse.getMachine().getMachineRate();
                }
            }

            for (int c = 0; c < cardsForUnit.size(); c++) {
                detailsRowIndex += 1;
                CardEvent cardReplacement = cardsForUnit.get(c);
                HSSFRow drowx = detailSheet.createRow(detailsRowIndex);

                drowx
                    .createCell(0)
                    .setCellValue(
                        (cardReplacement.getHeadline() + " el ") +
                        sdf.format(cardReplacement.getTimestamp())
                    );
            }
            detailsRowIndex += 2;
            summaryRowIndex += 1;
        }

        List<MachineUse> notAssignedUID = getUsesByCardNotInBuilding(uses, building);

        int summaryLastRow = summaryRowIndex + 2;

        HSSFRow summary1 = summarySheet.createRow((short) ++summaryLastRow);
        HSSFRow summary2 = summarySheet.createRow((short) ++summaryLastRow);
        HSSFRow summary3 = summarySheet.createRow((short) ++summaryLastRow);

        HSSFRow dsummary1 = detailSheet.createRow(detailsRowIndex + 1);
        HSSFRow dsummary2 = detailSheet.createRow(detailsRowIndex + 2);
        HSSFRow dsummary3 = detailSheet.createRow(detailsRowIndex + 3);
        HSSFRow dsummary4 = detailSheet.createRow(detailsRowIndex + 5);

        int totalCards = newCardEvents.size();

        int usesToAchiveMinUses = Math.max(minUses - totalUses, 0);
        totalUses = Math.max(totalUses, minUses);

        summary1.createCell(0).setCellValue("Total de usos");
        summary1.createCell(1).setCellValue(totalUses);

        SimpleDateFormat sdfNote = new SimpleDateFormat("dd/MM");

        // TODO - Rate: Cual es el Precio Unitario Ficha Para Ususario ??
        Double priceCustomerForMinDate = building.getRate().getPriceCustomer(reportMinDate);
        Double priceCustomerForMaxDate = building.getRate().getPriceCustomer(reportMaxDate);
        if (priceCustomerForMinDate != priceCustomerForMaxDate) {
            summary2.createCell(0).setCellValue("Precio Unitario Uso Simple Para Usuario ($)");
            summary2
                .createCell(1)
                .setCellValue(priceCustomerForMinDate + " / " + priceCustomerForMaxDate);
            summary2
                .createCell(2)
                .setCellValue(
                    "Cambio de precios " +
                    sdfNote.format(building.getRate().getValidFrom(reportMaxDate))
                );
        } else {
            summary2.createCell(0).setCellValue("Precio Unitario Uso Simple Para Usuario ($)");
            summary2.createCell(1).setCellValue(priceCustomerForMaxDate);
        }

        // TODO - Rate: Cual es el Precio Unitario Ficha Por LAVOMAT ??
        Double priceCompanyForMinDate = building.getRate().getPriceCompany(reportMinDate);
        Double priceCompanyForMaxDate = building.getRate().getPriceCompany(reportMaxDate);
        if (priceCompanyForMinDate != priceCompanyForMaxDate) {
            summary3.createCell(0).setCellValue("Precio Unitario Uso Simple Por LAVOMAT ($)");
            summary3
                .createCell(1)
                .setCellValue(priceCompanyForMinDate + " / " + priceCompanyForMaxDate);
            summary3
                .createCell(2)
                .setCellValue(
                    "Cambio de precios " +
                    sdfNote.format(building.getRate().getValidFrom(reportMaxDate))
                );
        } else {
            summary3.createCell(0).setCellValue("Precio Unitario Uso Simple Por LAVOMAT ($)");
            summary3.createCell(1).setCellValue(priceCompanyForMaxDate);
        }

        // Precio Unitario Uso Dosif.Jabón Para Usuario
        if (specialRate != null) {
            HSSFRow summary7 = summarySheet.createRow((short) ++summaryLastRow);
            HSSFRow summary6 = summarySheet.createRow((short) ++summaryLastRow);

            Double priceCompanyForMinDateSpecial = specialRate.getPriceCustomer(reportMinDate);
            Double priceCompanyForMaxDateSpecial = specialRate.getPriceCustomer(reportMaxDate);
            if (priceCompanyForMinDateSpecial != priceCompanyForMaxDateSpecial) {
                summary7
                    .createCell(0)
                    .setCellValue("Precio Unitario Uso Dosif. Jabón Para Usuario ($)");
                summary7
                    .createCell(1)
                    .setCellValue(
                        priceCompanyForMinDateSpecial + " / " + priceCompanyForMaxDateSpecial
                    );
                summary7
                    .createCell(2)
                    .setCellValue(
                        "Cambio de precios " +
                        sdfNote.format(building.getRate().getValidFrom(reportMaxDate))
                    );
            } else {
                summary7
                    .createCell(0)
                    .setCellValue("Precio Unitario Uso Dosif. Jabón Para Usuario ($)");
                summary7.createCell(1).setCellValue(priceCompanyForMaxDateSpecial);
            }

            // Precio Unitario Uso Dosif.Jabón Por LAVOMAT - company price especial

            Double priceCompanyForMinDateSpecialLMAT = specialRate.getPriceCompany(reportMinDate);
            Double priceCompanyForMaxDateSpecialLMAT = specialRate.getPriceCompany(reportMaxDate);
            if (priceCompanyForMinDateSpecialLMAT != priceCompanyForMaxDateSpecialLMAT) {
                summary6
                    .createCell(0)
                    .setCellValue("Precio Unitario Uso Dosif. Jabón Por LAVOMAT ($)");
                summary6
                    .createCell(1)
                    .setCellValue(
                        priceCompanyForMinDateSpecialLMAT +
                        " / " +
                        priceCompanyForMaxDateSpecialLMAT
                    );
                summary6
                    .createCell(2)
                    .setCellValue(
                        "Cambio de precios " +
                        sdfNote.format(building.getRate().getValidFrom(reportMaxDate))
                    );
            } else {
                summary6
                    .createCell(0)
                    .setCellValue("Precio Unitario Uso Dosif. Jabón Por LAVOMAT ($)");
                summary6.createCell(1).setCellValue(priceCompanyForMaxDateSpecialLMAT);
            }
        }

        HSSFRow summary4 = summarySheet.createRow((short) ++summaryLastRow);
        HSSFRow summary5 = summarySheet.createRow((short) ++summaryLastRow);

        // TODO - Rate: Cual es el Precio Unitario Ficha Por LAVOMAT ??
        Double priceCardReplacementForMinDate = building
            .getRate()
            .getPriceCardReplacement(reportMinDate);
        Double priceCardReplacementForMaxDate = building
            .getRate()
            .getPriceCardReplacement(reportMaxDate);
        if (priceCardReplacementForMinDate != priceCardReplacementForMaxDate) {
            summary4.createCell(0).setCellValue("Precio Unitario por Tarjeta Adicional ($)");
            summary4
                .createCell(1)
                .setCellValue(
                    priceCardReplacementForMinDate + " / " + priceCardReplacementForMaxDate
                );
            summary4
                .createCell(2)
                .setCellValue(
                    "Cambio de precios " +
                    sdfNote.format(building.getRate().getValidFrom(reportMaxDate))
                );
        } else {
            summary4.createCell(0).setCellValue("Precio Unitario por Tarjeta Adicional ($)");
            summary4.createCell(1).setCellValue(priceCardReplacementForMaxDate);
        }

        summary5.createCell(0).setCellValue("Importe a liquidar por LAVOMAT ($)");
        // TODO - Rate: Sumar la diferencia si no se llega al minimo de usos
        Double diffToAchiveMinUses = priceCompanyForMaxDate * usesToAchiveMinUses;
        // summary5.createCell(1).setCellValue(totalUses * b.getRate().getPriceCompany()
        // + totalCards * b.getRate().getPriceCardReplacement());
        summary5
            .createCell(1)
            .setCellValue(
                reportTotalPriceCompany + reportTotalPriceCardReplacement + diffToAchiveMinUses
            );

        dsummary1.createCell(0).setCellValue("Total de usos en el mes: " + totalUses);
        dsummary2
            .createCell(0)
            .setCellValue("Total de tarjetas adicionales en el mes: " + totalCards);
        dsummary3
            .createCell(0)
            .setCellValue(
                "Total UY$ " +
                (reportTotalPriceCompany + reportTotalPriceCardReplacement + diffToAchiveMinUses)
            );
        dsummary4
            .createCell(0)
            .setCellValue("-------------- UID NO ASIGNADAS AL EDIFICIO ---------------");

        detailsRowIndex += 6;
        for (int n = 0; n < notAssignedUID.size(); n++) {
            MachineUse u = notAssignedUID.get(n);
            detailsRowIndex++;
            HSSFRow dsummary5 = detailSheet.createRow((short) detailsRowIndex);
            dsummary5
                .createCell(0)
                .setCellValue(
                    "Se utilizo la máquina " +
                    u.getMachine().getSerialNumber() +
                    ", el " +
                    sdf.format(u.getTimestamp()) +
                    ", UID: " +
                    u.getUid()
                );
        }

        byte[] data = null;

        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            outputStream.flush();
            workbook.write(outputStream);
            workbook.close();

            data = outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return data;
    }

    private static List<MachineUse> getUsesByUnit(List<MachineUse> uses, Unit unit) {
        return uses
            .stream()
            .filter(use -> use.getCard() != null && unit.equals(use.getCard().getUnit()))
            .sorted(Comparator.comparing(MachineUse::getTimestamp).reversed())
            .collect(Collectors.toList());
    }

    private static List<CardEvent> getAdditionalCardsByUnit(List<CardEvent> cardEvents, Unit unit) {
        List<CardEvent> cardsForUnit = new ArrayList<>();
        for (CardEvent event : cardEvents) {
            if (event.getCard() != null && unit.equals(event.getCard().getUnit())) cardsForUnit.add(
                event
            );
        }
        return cardsForUnit;
    }

    private static List<MachineUse> getUsesByCardNotInBuilding(List<MachineUse> uses, Building b) {
        List<MachineUse> usesForUnit = new ArrayList<>();
        for (MachineUse use : uses) {
            if (!cardInBuilding(use, b)) usesForUnit.add(use);
        }
        return usesForUnit;
    }

    private static boolean cardInBuilding(MachineUse use, Building b) {
        for (Unit u : b.getUnits()) {
            if (use.getCard() != null && u.equals(use.getCard().getUnit())) return true;
        }

        return false;
    }

    // CIERRE PREPAGO
    public static JSONObject prepaidClosure(Building building, Date from_date, Date to_date) {
        Rate rate = building.getRate();
        Date now = new Date();

        if (rate.getPriceCompany(now).equals(rate.getPriceCustomer(now))) {
            // no aplica splitting no se manda cierre prepago
            return null;
        } else {
            Session session = JPA.em().unwrap(Session.class);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String from = null;
            String to = null;

            SimpleDateFormat parser = new SimpleDateFormat("EEE MMM d HH:mm:ss zzz yyyy");
            Date date = null;
            try {
                date = parser.parse(from_date.toString());
            } catch (ParseException e2) {
                // TODO Auto-generated catch block
                e2.printStackTrace();
            }
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            from = formatter.format(date);

            SimpleDateFormat parser2 = new SimpleDateFormat("EEE MMM d HH:mm:ss zzz yyyy");
            Date date2 = null;
            try {
                date2 = parser2.parse(to_date.toString());
            } catch (ParseException e3) {
                // TODO Auto-generated catch block
                e3.printStackTrace();
            }
            SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy-MM-dd");
            to = formatter2.format(date2);

            String sqlString =
                "select tx.name, SUM(lavados) as lavados, SUM(total_trx-total_comision_lmat) as comision_adm, SUM(total_lavados) - (SUM(total_lavados) * comision_lmat) as bajar_cuenta_adm " +
                "from " +
                "(select b.name, p.uuid, SUM(t.amount) as total_trx, SUM(t.comission) as total_comision_lmat , (r.price_company / r.price_customer) as comision_lmat " +
                "from lavomat.part p inner join lavomat.transaction t on t.uid = p.uuid " +
                "inner join lavomat.building_unit u on p.unit_id = u.unit_id " +
                "inner join lavomat.building b on u.building_id = b.id " +
                "inner join lavomat.rate_event r on b.rate_id = r.rate_id " +
                "where b.id in ('" +
                building.getId() +
                "') " +
                "and t.creation_date between '" +
                from +
                "' and '" +
                to +
                "' " +
                "and t.authorization_result = '0'" +
                "and '" +
                from +
                "' > r.valid_from and '" +
                to +
                "' < r.valid_until " +
                "group by b.name, p.uuid) tx " +
                "inner join " +
                "(select b.name, p.uuid, COUNT(*) as lavados, COUNT(*)*r.price_customer as total_lavados " +
                "from lavomat.part p inner join lavomat.machine_use m on m.uid = p.uuid " +
                "inner join lavomat.building_unit u on p.unit_id = u.unit_id " +
                "inner join lavomat.building b on u.building_id = b.id " +
                "inner join lavomat.rate_event r on b.rate_id = r.rate_id " +
                "where b.id in ('" +
                building.getId() +
                "') " +
                "and m.result = '1' " +
                "and m.timestamp between '" +
                from +
                "' and '" +
                to +
                "' " +
                "and '" +
                from +
                "' > r.valid_from and '" +
                to +
                "' < r.valid_until " +
                "group by b.name, p.uuid) uses on tx.uuid = uses.uuid";

            SQLQuery query = session.createSQLQuery(sqlString.toString());

            List<Object[]> resultSet = query.list();

            JSONObject jsonObject = null;

            if (resultSet.size() > 0) {
                jsonObject = new JSONObject();

                for (Object[] record : resultSet) {
                    try {
                        jsonObject.put("building_name", record[0]);
                        jsonObject.put("uses", record[1]);
                        jsonObject.put("adm_income", record[2]);
                        jsonObject.put("adm_outcome", record[3]);
                    } catch (JSONException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }

            return jsonObject;
        }
    }

    // excel para bills

    public static byte[] generateBillExcel(List<Bill> bills) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("Resumen");

        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeightInPoints(20);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        HSSFRow buildingHead = sheet.createRow((short) 0);
        HSSFRow rowhead = sheet.createRow((short) 1);

        // HEADERS
        buildingHead.createCell(0).setCellValue("*** LISTADO DE FACTURAS ***");

        rowhead.createCell(0).setCellValue("Id");
        rowhead.createCell(1).setCellValue("Tipo");
        rowhead.createCell(2).setCellValue("Receptor");
        rowhead.createCell(3).setCellValue("Fecha de Factura");
        rowhead.createCell(4).setCellValue("Estado");
        rowhead.createCell(5).setCellValue("Numero");
        rowhead.createCell(6).setCellValue("Periodo");
        rowhead.createCell(7).setCellValue("Monto");
        rowhead.createCell(8).setCellValue("POST/PRE");

        for (int j = 0; j < bills.size(); j++) {
            HSSFRow xrow = sheet.createRow((short) j + 2);

            Bill bill = bills.get(j);
            String timestamp = null;

            SimpleDateFormat parser = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            Date date = null;
            try {
                date = parser.parse(bill.getTimestamp().toString());
            } catch (ParseException e2) {
                // TODO Auto-generated catch block
                e2.printStackTrace();
            }
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy-MM-dd");
            timestamp = formatter.format(date);

            String value_s = bill.getSerie() != null ? bill.getSerie() : "";
            String value_n = bill.getNumber() != null ? bill.getNumber().toString() : "";

            String value_b = Building.findById(bill.getBillTo()) != null
                ? Building.findById(bill.getBillTo()).getName()
                : "";

            String value_ps = bill.getBilledPeriodStart() != null
                ? formatter2.format(bill.getBilledPeriodStart())
                : "";
            String value_pe = bill.getBilledPeriodEnd() != null
                ? formatter2.format(bill.getBilledPeriodEnd())
                : "";

            xrow.createCell(0).setCellValue(bill.getId());
            xrow.createCell(1).setCellValue(bill.getBillType().name());
            xrow.createCell(2).setCellValue(value_b);
            xrow.createCell(3).setCellValue(timestamp);
            xrow.createCell(4).setCellValue(bill.getState().name());
            xrow.createCell(5).setCellValue(value_s + " - " + value_n);
            xrow.createCell(6).setCellValue(value_ps + "/" + value_pe);
            xrow.createCell(7).setCellValue(bill.getTotal());
            xrow.createCell(8).setCellValue(bill.getBillToType());
        }

        byte[] data = null;

        try {
            outputStream.flush();
            workbook.write(outputStream);
            workbook.close();

            data = outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return data;
    }
}
