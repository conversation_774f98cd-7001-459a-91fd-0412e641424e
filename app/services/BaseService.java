package services;

import org.apache.commons.lang3.StringUtils;

public abstract class BaseService {

    private static final String CLASS_SUFFIX = "Service";

    public void logger(String message, java.lang.Object... args) {
        play.Logger.debug(messagePrefix() + message, args);
    }

    public void loggerError(String message, java.lang.Object... args) {
        play.Logger.error(messagePrefix() + message, args);
    }

    public void loggerErrorWithException(String message, Throwable error) {
        play.Logger.error(messagePrefix() + message, error);
    }

    protected String getLoggerProviderName() {
        return getClass().getSimpleName().replace(getLoggerClassSuffix(), StringUtils.EMPTY);
    }

    protected String getLoggerClassSuffix() {
        return CLASS_SUFFIX;
    }

    private String messagePrefix() {
        return "## " + getLoggerProviderName() + " ## ";
    }
}
