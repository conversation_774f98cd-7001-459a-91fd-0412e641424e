package services;

import global.APIException;
import global.APIException.APIErrors;
import java.io.IOException;
import java.util.Date;
import models.Building;
import models.Card;
import models.CardRequest;
import models.Unit;
import org.apache.commons.mail.EmailException;
import utils.email.EmailService;

public class RequestNewCardService {

    public void confirmNewCard(String token) throws APIException {
        if (token == null || token.isEmpty()) {
            throw APIException.raise(APIErrors.MISSING_PARAMETERS);
        }

        CardRequest cardRequest = CardRequest.findByToken(token);

        if (cardRequest == null || cardRequest.getAcceptanceDate() != null) {
            APIException ex = APIException.raise(APIErrors.INVALID_CARD_REQUEST_TOKEN);
            ex.setArg("INVALID_CARD_REQUEST_TOKEN", "true");
            throw ex;
        }

        domains.bot.services.support.ContactSupportService service = new domains.bot.services.support.ContactSupportService();
        service.requestNewCard(cardRequest);

        cardRequest.setAcceptanceDate(new Date());
        cardRequest.setToken("");
        cardRequest.update();
    }

    public void rejectNewCard(String token) throws APIException {
        CardRequest cardRequest = CardRequest.findByToken(token);

        if (cardRequest == null || cardRequest.getRejectionDate() != null) {
            APIException ex = APIException.raise(APIErrors.INVALID_CARD_REQUEST_TOKEN);
            ex.setArg("INVALID_CARD_REQUEST_TOKEN", "true");
            throw ex;
        }

        cardRequest.setRejectionDate(new Date());
        cardRequest.update();
    }

    public void rejectActivationSubmit(String token, String reason)
        throws APIException, EmailException, IOException {
        CardRequest cardRequest = CardRequest.findByToken(token);
        if (cardRequest == null || cardRequest.getRejectionDate() != null) {
            APIException ex = APIException.raise(APIErrors.INVALID_CARD_REQUEST_TOKEN);
            ex.setArg("INVALID_CARD_REQUEST_TOKEN", "true");
            throw ex;
        }

        String subject = "LAVOMAT - Solicitud de Aceptación de Tarjeta";
        String message =
            "La administración del edificio rechazo la solicitud de aceptación de tarjeta, por el siguiente motivo: " +
            reason +
            ".";

        String body = views.html.generalMessage.render(message, cardRequest.getFirstName()).body();

        EmailService.sendAsync(cardRequest.getEmail(), subject, body);

        cardRequest.setRejectionDate(new Date());
        cardRequest.setRejectReason(reason);
        cardRequest.update();
    }

    public void acceptCard(String token) throws APIException {
        if (token == null || token.isEmpty()) {
            throw APIException.raise(APIErrors.MISSING_PARAMETERS);
        }
        CardRequest cardRequest = CardRequest.findByToken(token);
        if (cardRequest == null || cardRequest.getRejectionDate() != null) {
            APIException ex = APIException.raise(APIErrors.INVALID_CARD_REQUEST_TOKEN);
            ex.setArg("INVALID_CARD_REQUEST_TOKEN", "true");
            throw ex;
        }
        Building building = cardRequest.getBuilding();
        Card card = cardRequest.getCard();
        Unit unit = cardRequest.getUnit();

        if (unit == null) throw APIException.raise(APIErrors.UNIT_NOT_FOUND);

        if (card.getUnit() != null) throw APIException
            .raise(APIErrors.NOT_ASSIGNABLE)
            .setDetailMessage("Esta tarjeta ya se encuentra asignada");

        unit.getAssignedCards().add(card);
        unit.update();
        card.setUnit(unit);
        card.update();
        cardRequest.setAcceptanceDate(new Date());
        cardRequest.update();
    }
}
