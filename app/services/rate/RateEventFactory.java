package services.rate;

import domains.back_office.dto.rates.RateEventUpdateParameters;
import models.Rate;
import models.RateEvent;
import services.building.BuildingFactoryService;

public class RateEventFactory {

    protected RateEvent rateEvent;

    public RateEventFactory(RateEvent event) {
        this.rateEvent = event;
    }

    public void create() {
        this.rateEvent.save();
        this.rateEvent.getRate().resetEmailNotificationCountToLavomat();
        this.rateEvent.getRate().update();

        updateBuildings();
    }

    public void update(RateEventUpdateParameters dto) {
        this.rateEvent.setPriceCustomer(dto.getCustomerPrice());
        this.rateEvent.setPriceCompany(dto.getCompanyPrice());
        this.rateEvent.setPriceM3(dto.getM3Price());
        this.rateEvent.setPriceKWh(dto.getKwhPrice());
        this.rateEvent.setPriceCardReplacement(dto.getCardReplacementPrice());
        this.rateEvent.setMinUsesPerWasher(dto.getMinUses());
        this.rateEvent.setMinUsesPerUnit(dto.getMinUsesPerUnit());
        this.rateEvent.setValidFrom(dto.getValidFrom());
        this.rateEvent.setValidUntil(dto.getValidUntil());

        this.rateEvent.update();

        updateBuildings();
    }

    public void delete() {
        Rate rate = this.rateEvent.getRate();

        rate.removeRateEvent(this.rateEvent);
        rate.update();

        this.rateEvent.setRate(null);
        this.rateEvent.update();

        this.rateEvent.delete();

        updateBuildings(rate);
    }

    private void updateBuildings() {
        updateBuildings(this.rateEvent.getRate());
    }

    private void updateBuildings(Rate rate) {
        BuildingFactoryService buildingService = new BuildingFactoryService();
        buildingService.bulkUpdatePaymentMethod(rate);
    }
}
