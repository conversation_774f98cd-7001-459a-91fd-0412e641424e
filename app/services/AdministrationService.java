package services;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import models.Administration;
import models.Building;
import models.Building.InvoicingType;
import models.Card.ContractType;

public class AdministrationService {

    public List<Building> getBuildings(Administration admin, boolean listSplitOnly) {
        if (listSplitOnly) {
            return filterOnlySplitBuildings(admin.getBuildings());
        }

        return admin.getBuildings();
    }

    private List<Building> filterOnlySplitBuildings(List<Building> buildings) {
        return buildings
            .stream()
            .filter(b ->
                (
                    b.getContractType() == ContractType.PREPAID ||
                    b.getContractType() == ContractType.MIXED
                ) &&
                (
                    (b.getRate() != null && b.getRate().isSplittingApplicable()) ||
                    b.getInvoicingType() == InvoicingType.PER_UNIT ||
                    b.getInvoicingType() == InvoicingType.HYBRID
                )
            )
            .collect(Collectors.toList());
    }
}
