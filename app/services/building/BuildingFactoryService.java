package services.building;

import domains.billing.BuildingService;
import dto.building.BuildingParameters;
import dto.building.UpdateBuildingParameters;
import java.util.Date;
import java.util.List;
import models.*;
import models.Building.InvoicingType;
import models.Building.PaymentMethod;
import models.Card.ContractType;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import queries.buildings.BuildingQuery;
import utils.StringHelper;

public class BuildingFactoryService {

    public void createBuilding(BuildingParameters params) {
        Administration administration = null;
        if (params.getAdministrationId() > 0) {
            administration = Administration.findById(params.getAdministrationId());
        }

        Rate rate = null;
        if (params.getRateId() > 0) {
            rate = Rate.findById(params.getRateId());
        }

        Building building = new Building(
            params.getName(),
            params.getAddress(),
            params.getContact(),
            administration,
            rate,
            params.getCountry(),
            params.getCity(),
            params.getState()
        );
        building.setLatitude(params.getLatitude());
        building.setLongitude(params.getLongitude());
        building.generateSlug();

        PaymentMethod paymentMethod = calculatePaymentMethod(rate);
        building.setPaymentMethod(paymentMethod);

        if (!StringHelper.isBlank(params.getInvoicingType())) {
            building.setInvoicingType(InvoicingType.valueOf(params.getInvoicingType()));
        }

        if (!StringHelper.isBlank(params.getInvoicingMethod())) {
            building.setInvoicingMethod(InvoicePaymentMethod.valueOf(params.getInvoicingMethod()));
        }

        if (!StringHelper.isBlank(params.getContractType())) {
            building.setContractType(ContractType.valueOf(params.getContractType()));
        }

        if (params.getClosureType() != null) {
            building.setClosureType(params.getClosureType());
        }

        if (!StringHelper.isBlank(params.getRut())) {
            building.setRut(params.getRut());
        }

        if (params.getBuildingType() != null) {
            building.setBuildingType(params.getBuildingType());
        }

        if (ContractType.PREPAID != building.getContractType()) {
            Date defaultLastBillingDate = new DateTime()
                .minusDays(1)
                .withTime(23, 59, 59, 59)
                .toDate();
            building.setLastBillingDate(defaultLastBillingDate);
        }

        building.setIsIncludedInClosure(true);
        building.save();

        if (StringUtils.isNotBlank(params.getBillingName())) {
            setBillingName(building, params.getBillingName());
        }
    }

    public PaymentMethod calculatePaymentMethod(Rate rate) {
        if (rate == null) {
            return PaymentMethod.NOT_SPECIFIED;
        }

        RateEvent lastRateEvent = rate.getCurrentRateEvent();

        if (lastRateEvent == null) {
            return PaymentMethod.NOT_SPECIFIED;
        }

        Double priceCustomer = lastRateEvent.getPriceCustomer();
        Double priceCompany = lastRateEvent.getPriceCompany();

        if (priceCustomer == null || priceCompany == null) {
            return PaymentMethod.NOT_SPECIFIED;
        }

        return Double.compare(priceCustomer, priceCompany) == 0
            ? PaymentMethod.WEB_AND_REDPAGOS
            : PaymentMethod.WEB_MP_ONLY;
    }

    public void updateBuilding(Building building, UpdateBuildingParameters params) {
        Administration administration = null;

        if (params.getAdministrationId() > 0) {
            administration = Administration.findById(params.getAdministrationId());
        }

        Rate rate = null;
        if (params.getRateId() > 0) {
            rate = Rate.findById(params.getRateId());
            building.setPaymentMethod(calculatePaymentMethod(rate));
        }

        if (!StringHelper.isBlank(params.getName())) {
            building.setName(params.getName());
        }

        if (!StringHelper.isBlank(params.getAddress())) {
            building.setAddress(params.getAddress());
        }

        if (!StringHelper.isBlank(params.getContact())) {
            building.setCountry(params.getCountry());
        }

        if (!StringHelper.isBlank(params.getCity())) {
            building.setCity(params.getCity());
        }

        if (!StringHelper.isBlank(params.getState())) {
            building.setDepartment(params.getState());
        }

        if (!StringHelper.isBlank(params.getRut())) {
            building.setRut(params.getRut());
        } else {
            building.setRut(null);
        }

        if (!StringHelper.isBlank(params.getContact())) {
            building.setContact(params.getContact());
        }

        if (administration != null) {
            building.setAdministration(administration);
        }

        if (rate != null) {
            building.setRate(rate);
        }

        if (!StringHelper.isBlank(params.getInvoicingType())) {
            building.setInvoicingType(InvoicingType.valueOf(params.getInvoicingType()));
        }

        if (!StringHelper.isBlank(params.getInvoicingMethod())) {
            building.setInvoicingMethod(InvoicePaymentMethod.valueOf(params.getInvoicingMethod()));
        }

        if (!StringHelper.isBlank(params.getContractType())) {
            building.setContractType(ContractType.valueOf(params.getContractType()));
        }

        if (params.getClosureType() != null) {
            building.setClosureType(params.getClosureType());
        }

        if (!StringHelper.isBlank(params.getPaymentMethod())) {
            building.setPaymentMethod(PaymentMethod.valueOf(params.getPaymentMethod()));
        }

        building.setLatitude(params.getLatitude());
        building.setLongitude(params.getLongitude());
        building.setPrepaidRechargeableUses(params.getPrepaidRechargeableUses());
        building.setMaxNumberOfUnits(params.getMaxNumberOfUnits());

        if (params.getMp100() >= 0 || params.getMp500() >= 0 || params.getMp1200() >= 0) {
            MaintenanceParameter parameter = null;
            if (building.getMaintenanceParameter() != null) {
                parameter = building.getMaintenanceParameter();
                parameter.setMp100(params.getMp100());
                parameter.setMp500(params.getMp500());
                parameter.setMp1200(params.getMp1200());
            } else {
                parameter =
                    new MaintenanceParameter(
                        params.getMp100(),
                        params.getMp500(),
                        params.getMp1200()
                    );
            }
            building.setMaintenanceParameter(parameter);
        }

        if (params.getBuildingType() != null) {
            building.setBuildingType(params.getBuildingType());
        }

        building
            .getBuildingSetting()
            .setPreBlockedUseEnabled(params.getBuildingSettingParameters().getPreBlockedUses());
        building
            .getBuildingSetting()
            .setOpeningTime(params.getBuildingSettingParameters().getOpeningTime());
        building
            .getBuildingSetting()
            .setClosingTime(params.getBuildingSettingParameters().getClosingTime());

        building.update();

        updateBuildingPaymentMethod(building, rate);

        if (StringUtils.isNotBlank(params.getBillingName())) {
            setBillingName(building, params.getBillingName());
        }
    }

    public void setBillingName(Building building, String billingName) {
        BuildingService buildingService = new BuildingService();
        buildingService.setBillingName(building, billingName);
    }

    public void bulkUpdatePaymentMethod(Rate rate) {
        List<Building> buildingsToUpdate =
            this.query()
                .filterByRateId(rate.getId())
                .filterByContractTypes(ContractType.PREPAID, ContractType.MIXED)
                .find();

        for (int i = 0; i < buildingsToUpdate.size(); i++) {
            Building building = buildingsToUpdate.get(i);
            updateBuildingPaymentMethod(building, rate);
        }
    }

    protected BuildingQuery query() {
        return new BuildingQuery();
    }

    private void updateBuildingPaymentMethod(Building building, Rate rate) {
        PaymentMethod paymentMethod = this.calculatePaymentMethod(rate);
        if (paymentMethod.equals(building.getPaymentMethod())) {
            return;
        }

        building.setPaymentMethod(paymentMethod);
        building.update();
    }
}
