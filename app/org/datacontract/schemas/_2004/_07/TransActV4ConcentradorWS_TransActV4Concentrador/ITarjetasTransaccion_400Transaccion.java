/**
 * ITarjetasTransaccion_400Transaccion.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador;

public class ITarjetasTransaccion_400Transaccion implements java.io.Serializable {
    private org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400Comportamiento comportamiento;

    private org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400Configuracion configuracion;

    private java.lang.Integer emisorId;

    private java.lang.String empCod;

    private java.lang.String empHASH;

    private org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400TransaccionExtendida extendida;

    private java.lang.Boolean facturaConsumidorFinal;

    private java.lang.Double facturaMonto;

    private java.lang.Double facturaMontoGravado;

    private java.lang.Double facturaMontoIVA;

    private java.lang.Double facturaNro;

    private java.lang.String monedaISO;

    private double monto;

    private java.lang.Double montoCashBack;

    private java.lang.Double montoPropina;

    private java.lang.Integer multiEmp;

    private java.lang.String operacion;

    private java.lang.Boolean tarjetaAlimentacion;

    private java.lang.Integer tarjetaId;

    private java.lang.String tarjetaTipo;

    private java.lang.String termCod;

    private java.lang.Integer ticketOriginal;

    public ITarjetasTransaccion_400Transaccion() {
    }

    public ITarjetasTransaccion_400Transaccion(
            org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400Comportamiento comportamiento,
            org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400Configuracion configuracion,
            java.lang.Integer emisorId, java.lang.String empCod, java.lang.String empHASH,
            org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400TransaccionExtendida extendida,
            java.lang.Boolean facturaConsumidorFinal, java.lang.Double facturaMonto,
            java.lang.Double facturaMontoGravado, java.lang.Double facturaMontoIVA, java.lang.Double facturaNro,
            java.lang.String monedaISO, double monto, java.lang.Double montoCashBack, java.lang.Double montoPropina,
            java.lang.Integer multiEmp, java.lang.String operacion, java.lang.Boolean tarjetaAlimentacion,
            java.lang.Integer tarjetaId, java.lang.String tarjetaTipo, java.lang.String termCod,
            java.lang.Integer ticketOriginal) {
        this.comportamiento = comportamiento;
        this.configuracion = configuracion;
        this.emisorId = emisorId;
        this.empCod = empCod;
        this.empHASH = empHASH;
        this.extendida = extendida;
        this.facturaConsumidorFinal = facturaConsumidorFinal;
        this.facturaMonto = facturaMonto;
        this.facturaMontoGravado = facturaMontoGravado;
        this.facturaMontoIVA = facturaMontoIVA;
        this.facturaNro = facturaNro;
        this.monedaISO = monedaISO;
        this.monto = monto;
        this.montoCashBack = montoCashBack;
        this.montoPropina = montoPropina;
        this.multiEmp = multiEmp;
        this.operacion = operacion;
        this.tarjetaAlimentacion = tarjetaAlimentacion;
        this.tarjetaId = tarjetaId;
        this.tarjetaTipo = tarjetaTipo;
        this.termCod = termCod;
        this.ticketOriginal = ticketOriginal;
    }

    /**
     * Gets the comportamiento value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return comportamiento
     */
    public org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400Comportamiento getComportamiento() {
        return comportamiento;
    }

    /**
     * Sets the comportamiento value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param comportamiento
     */
    public void setComportamiento(
            org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400Comportamiento comportamiento) {
        this.comportamiento = comportamiento;
    }

    /**
     * Gets the configuracion value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return configuracion
     */
    public org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400Configuracion getConfiguracion() {
        return configuracion;
    }

    /**
     * Sets the configuracion value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param configuracion
     */
    public void setConfiguracion(
            org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400Configuracion configuracion) {
        this.configuracion = configuracion;
    }

    /**
     * Gets the emisorId value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return emisorId
     */
    public java.lang.Integer getEmisorId() {
        return emisorId;
    }

    /**
     * Sets the emisorId value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param emisorId
     */
    public void setEmisorId(java.lang.Integer emisorId) {
        this.emisorId = emisorId;
    }

    /**
     * Gets the empCod value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return empCod
     */
    public java.lang.String getEmpCod() {
        return empCod;
    }

    /**
     * Sets the empCod value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param empCod
     */
    public void setEmpCod(java.lang.String empCod) {
        this.empCod = empCod;
    }

    /**
     * Gets the empHASH value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return empHASH
     */
    public java.lang.String getEmpHASH() {
        return empHASH;
    }

    /**
     * Sets the empHASH value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param empHASH
     */
    public void setEmpHASH(java.lang.String empHASH) {
        this.empHASH = empHASH;
    }

    /**
     * Gets the extendida value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return extendida
     */
    public org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400TransaccionExtendida getExtendida() {
        return extendida;
    }

    /**
     * Sets the extendida value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param extendida
     */
    public void setExtendida(
            org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.ITarjetasTransaccion_400TransaccionExtendida extendida) {
        this.extendida = extendida;
    }

    /**
     * Gets the facturaConsumidorFinal value for this
     * ITarjetasTransaccion_400Transaccion.
     * 
     * @return facturaConsumidorFinal
     */
    public java.lang.Boolean getFacturaConsumidorFinal() {
        return facturaConsumidorFinal;
    }

    /**
     * Sets the facturaConsumidorFinal value for this
     * ITarjetasTransaccion_400Transaccion.
     * 
     * @param facturaConsumidorFinal
     */
    public void setFacturaConsumidorFinal(java.lang.Boolean facturaConsumidorFinal) {
        this.facturaConsumidorFinal = facturaConsumidorFinal;
    }

    /**
     * Gets the facturaMonto value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return facturaMonto
     */
    public java.lang.Double getFacturaMonto() {
        return facturaMonto;
    }

    /**
     * Sets the facturaMonto value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param facturaMonto
     */
    public void setFacturaMonto(java.lang.Double facturaMonto) {
        this.facturaMonto = facturaMonto;
    }

    /**
     * Gets the facturaMontoGravado value for this
     * ITarjetasTransaccion_400Transaccion.
     * 
     * @return facturaMontoGravado
     */
    public java.lang.Double getFacturaMontoGravado() {
        return facturaMontoGravado;
    }

    /**
     * Sets the facturaMontoGravado value for this
     * ITarjetasTransaccion_400Transaccion.
     * 
     * @param facturaMontoGravado
     */
    public void setFacturaMontoGravado(java.lang.Double facturaMontoGravado) {
        this.facturaMontoGravado = facturaMontoGravado;
    }

    /**
     * Gets the facturaMontoIVA value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return facturaMontoIVA
     */
    public java.lang.Double getFacturaMontoIVA() {
        return facturaMontoIVA;
    }

    /**
     * Sets the facturaMontoIVA value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param facturaMontoIVA
     */
    public void setFacturaMontoIVA(java.lang.Double facturaMontoIVA) {
        this.facturaMontoIVA = facturaMontoIVA;
    }

    /**
     * Gets the facturaNro value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return facturaNro
     */
    public java.lang.Double getFacturaNro() {
        return facturaNro;
    }

    /**
     * Sets the facturaNro value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param facturaNro
     */
    public void setFacturaNro(java.lang.Double facturaNro) {
        this.facturaNro = facturaNro;
    }

    /**
     * Gets the monedaISO value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return monedaISO
     */
    public java.lang.String getMonedaISO() {
        return monedaISO;
    }

    /**
     * Sets the monedaISO value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param monedaISO
     */
    public void setMonedaISO(java.lang.String monedaISO) {
        this.monedaISO = monedaISO;
    }

    /**
     * Gets the monto value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return monto
     */
    public double getMonto() {
        return monto;
    }

    /**
     * Sets the monto value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param monto
     */
    public void setMonto(double monto) {
        this.monto = monto;
    }

    /**
     * Gets the montoCashBack value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return montoCashBack
     */
    public java.lang.Double getMontoCashBack() {
        return montoCashBack;
    }

    /**
     * Sets the montoCashBack value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param montoCashBack
     */
    public void setMontoCashBack(java.lang.Double montoCashBack) {
        this.montoCashBack = montoCashBack;
    }

    /**
     * Gets the montoPropina value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return montoPropina
     */
    public java.lang.Double getMontoPropina() {
        return montoPropina;
    }

    /**
     * Sets the montoPropina value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param montoPropina
     */
    public void setMontoPropina(java.lang.Double montoPropina) {
        this.montoPropina = montoPropina;
    }

    /**
     * Gets the multiEmp value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return multiEmp
     */
    public java.lang.Integer getMultiEmp() {
        return multiEmp;
    }

    /**
     * Sets the multiEmp value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param multiEmp
     */
    public void setMultiEmp(java.lang.Integer multiEmp) {
        this.multiEmp = multiEmp;
    }

    /**
     * Gets the operacion value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return operacion
     */
    public java.lang.String getOperacion() {
        return operacion;
    }

    /**
     * Sets the operacion value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param operacion
     */
    public void setOperacion(java.lang.String operacion) {
        this.operacion = operacion;
    }

    /**
     * Gets the tarjetaAlimentacion value for this
     * ITarjetasTransaccion_400Transaccion.
     * 
     * @return tarjetaAlimentacion
     */
    public java.lang.Boolean getTarjetaAlimentacion() {
        return tarjetaAlimentacion;
    }

    /**
     * Sets the tarjetaAlimentacion value for this
     * ITarjetasTransaccion_400Transaccion.
     * 
     * @param tarjetaAlimentacion
     */
    public void setTarjetaAlimentacion(java.lang.Boolean tarjetaAlimentacion) {
        this.tarjetaAlimentacion = tarjetaAlimentacion;
    }

    /**
     * Gets the tarjetaId value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return tarjetaId
     */
    public java.lang.Integer getTarjetaId() {
        return tarjetaId;
    }

    /**
     * Sets the tarjetaId value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param tarjetaId
     */
    public void setTarjetaId(java.lang.Integer tarjetaId) {
        this.tarjetaId = tarjetaId;
    }

    /**
     * Gets the tarjetaTipo value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return tarjetaTipo
     */
    public java.lang.String getTarjetaTipo() {
        return tarjetaTipo;
    }

    /**
     * Sets the tarjetaTipo value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param tarjetaTipo
     */
    public void setTarjetaTipo(java.lang.String tarjetaTipo) {
        this.tarjetaTipo = tarjetaTipo;
    }

    /**
     * Gets the termCod value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return termCod
     */
    public java.lang.String getTermCod() {
        return termCod;
    }

    /**
     * Sets the termCod value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param termCod
     */
    public void setTermCod(java.lang.String termCod) {
        this.termCod = termCod;
    }

    /**
     * Gets the ticketOriginal value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @return ticketOriginal
     */
    public java.lang.Integer getTicketOriginal() {
        return ticketOriginal;
    }

    /**
     * Sets the ticketOriginal value for this ITarjetasTransaccion_400Transaccion.
     * 
     * @param ticketOriginal
     */
    public void setTicketOriginal(java.lang.Integer ticketOriginal) {
        this.ticketOriginal = ticketOriginal;
    }

    private java.lang.Object __equalsCalc = null;

    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof ITarjetasTransaccion_400Transaccion))
            return false;
        ITarjetasTransaccion_400Transaccion other = (ITarjetasTransaccion_400Transaccion) obj;
        if (obj == null)
            return false;
        if (this == obj)
            return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true
                && ((this.comportamiento == null && other.getComportamiento() == null)
                        || (this.comportamiento != null && this.comportamiento.equals(other.getComportamiento())))
                && ((this.configuracion == null && other.getConfiguracion() == null)
                        || (this.configuracion != null && this.configuracion.equals(other.getConfiguracion())))
                && ((this.emisorId == null && other.getEmisorId() == null)
                        || (this.emisorId != null && this.emisorId.equals(other.getEmisorId())))
                && ((this.empCod == null && other.getEmpCod() == null)
                        || (this.empCod != null && this.empCod.equals(other.getEmpCod())))
                && ((this.empHASH == null && other.getEmpHASH() == null)
                        || (this.empHASH != null && this.empHASH.equals(other.getEmpHASH())))
                && ((this.extendida == null && other.getExtendida() == null)
                        || (this.extendida != null && this.extendida.equals(other.getExtendida())))
                && ((this.facturaConsumidorFinal == null && other.getFacturaConsumidorFinal() == null)
                        || (this.facturaConsumidorFinal != null
                                && this.facturaConsumidorFinal.equals(other.getFacturaConsumidorFinal())))
                && ((this.facturaMonto == null && other.getFacturaMonto() == null)
                        || (this.facturaMonto != null && this.facturaMonto.equals(other.getFacturaMonto())))
                && ((this.facturaMontoGravado == null && other.getFacturaMontoGravado() == null)
                        || (this.facturaMontoGravado != null
                                && this.facturaMontoGravado.equals(other.getFacturaMontoGravado())))
                && ((this.facturaMontoIVA == null && other.getFacturaMontoIVA() == null)
                        || (this.facturaMontoIVA != null && this.facturaMontoIVA.equals(other.getFacturaMontoIVA())))
                && ((this.facturaNro == null && other.getFacturaNro() == null)
                        || (this.facturaNro != null && this.facturaNro.equals(other.getFacturaNro())))
                && ((this.monedaISO == null && other.getMonedaISO() == null)
                        || (this.monedaISO != null && this.monedaISO.equals(other.getMonedaISO())))
                && this.monto == other.getMonto()
                && ((this.montoCashBack == null && other.getMontoCashBack() == null)
                        || (this.montoCashBack != null && this.montoCashBack.equals(other.getMontoCashBack())))
                && ((this.montoPropina == null && other.getMontoPropina() == null)
                        || (this.montoPropina != null && this.montoPropina.equals(other.getMontoPropina())))
                && ((this.multiEmp == null && other.getMultiEmp() == null)
                        || (this.multiEmp != null && this.multiEmp.equals(other.getMultiEmp())))
                && ((this.operacion == null && other.getOperacion() == null)
                        || (this.operacion != null && this.operacion.equals(other.getOperacion())))
                && ((this.tarjetaAlimentacion == null && other.getTarjetaAlimentacion() == null)
                        || (this.tarjetaAlimentacion != null
                                && this.tarjetaAlimentacion.equals(other.getTarjetaAlimentacion())))
                && ((this.tarjetaId == null && other.getTarjetaId() == null)
                        || (this.tarjetaId != null && this.tarjetaId.equals(other.getTarjetaId())))
                && ((this.tarjetaTipo == null && other.getTarjetaTipo() == null)
                        || (this.tarjetaTipo != null && this.tarjetaTipo.equals(other.getTarjetaTipo())))
                && ((this.termCod == null && other.getTermCod() == null)
                        || (this.termCod != null && this.termCod.equals(other.getTermCod())))
                && ((this.ticketOriginal == null && other.getTicketOriginal() == null)
                        || (this.ticketOriginal != null && this.ticketOriginal.equals(other.getTicketOriginal())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;

    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getComportamiento() != null) {
            _hashCode += getComportamiento().hashCode();
        }
        if (getConfiguracion() != null) {
            _hashCode += getConfiguracion().hashCode();
        }
        if (getEmisorId() != null) {
            _hashCode += getEmisorId().hashCode();
        }
        if (getEmpCod() != null) {
            _hashCode += getEmpCod().hashCode();
        }
        if (getEmpHASH() != null) {
            _hashCode += getEmpHASH().hashCode();
        }
        if (getExtendida() != null) {
            _hashCode += getExtendida().hashCode();
        }
        if (getFacturaConsumidorFinal() != null) {
            _hashCode += getFacturaConsumidorFinal().hashCode();
        }
        if (getFacturaMonto() != null) {
            _hashCode += getFacturaMonto().hashCode();
        }
        if (getFacturaMontoGravado() != null) {
            _hashCode += getFacturaMontoGravado().hashCode();
        }
        if (getFacturaMontoIVA() != null) {
            _hashCode += getFacturaMontoIVA().hashCode();
        }
        if (getFacturaNro() != null) {
            _hashCode += getFacturaNro().hashCode();
        }
        if (getMonedaISO() != null) {
            _hashCode += getMonedaISO().hashCode();
        }
        _hashCode += new Double(getMonto()).hashCode();
        if (getMontoCashBack() != null) {
            _hashCode += getMontoCashBack().hashCode();
        }
        if (getMontoPropina() != null) {
            _hashCode += getMontoPropina().hashCode();
        }
        if (getMultiEmp() != null) {
            _hashCode += getMultiEmp().hashCode();
        }
        if (getOperacion() != null) {
            _hashCode += getOperacion().hashCode();
        }
        if (getTarjetaAlimentacion() != null) {
            _hashCode += getTarjetaAlimentacion().hashCode();
        }
        if (getTarjetaId() != null) {
            _hashCode += getTarjetaId().hashCode();
        }
        if (getTarjetaTipo() != null) {
            _hashCode += getTarjetaTipo().hashCode();
        }
        if (getTermCod() != null) {
            _hashCode += getTermCod().hashCode();
        }
        if (getTicketOriginal() != null) {
            _hashCode += getTicketOriginal().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc = new org.apache.axis.description.TypeDesc(
            ITarjetasTransaccion_400Transaccion.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "ITarjetasTransaccion_400.Transaccion"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("comportamiento");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "Comportamiento"));
        elemField.setXmlType(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "ITarjetasTransaccion_400.Comportamiento"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("configuracion");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "Configuracion"));
        elemField.setXmlType(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "ITarjetasTransaccion_400.Configuracion"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("emisorId");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador", "EmisorId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("empCod");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador", "EmpCod"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("empHASH");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador", "EmpHASH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("extendida");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "Extendida"));
        elemField.setXmlType(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "ITarjetasTransaccion_400.TransaccionExtendida"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaConsumidorFinal");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaConsumidorFinal"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaMonto");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaMonto"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaMontoGravado");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaMontoGravado"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaMontoIVA");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaMontoIVA"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaNro");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaNro"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("monedaISO");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "MonedaISO"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("monto");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador", "Monto"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("montoCashBack");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "MontoCashBack"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("montoPropina");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "MontoPropina"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("multiEmp");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador", "MultiEmp"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("operacion");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "Operacion"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tarjetaAlimentacion");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TarjetaAlimentacion"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tarjetaId");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TarjetaId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tarjetaTipo");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TarjetaTipo"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("termCod");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador", "TermCod"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ticketOriginal");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TicketOriginal"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(java.lang.String mechType,
            java.lang.Class _javaType, javax.xml.namespace.QName _xmlType) {
        return new org.apache.axis.encoding.ser.BeanSerializer(_javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(java.lang.String mechType,
            java.lang.Class _javaType, javax.xml.namespace.QName _xmlType) {
        return new org.apache.axis.encoding.ser.BeanDeserializer(_javaType, _xmlType, typeDesc);
    }

}
