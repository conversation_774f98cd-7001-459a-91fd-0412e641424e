
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

/**
 * <p>
 * Java class for Certificado complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="Certificado">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="DiasRestantes" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="Estado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="VigenciaDesde" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="VigenciaHasta" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Certificado", propOrder = { "diasRestantes", "estado", "vigenciaDesde", "vigenciaHasta" })
public class Certificado {

    @XmlElement(name = "DiasRestantes")
    protected Integer diasRestantes;
    @XmlElementRef(name = "Estado", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> estado;
    @XmlElement(name = "VigenciaDesde")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar vigenciaDesde;
    @XmlElement(name = "VigenciaHasta")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar vigenciaHasta;

    /**
     * Gets the value of the diasRestantes property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getDiasRestantes() {
        return diasRestantes;
    }

    /**
     * Sets the value of the diasRestantes property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setDiasRestantes(Integer value) {
        this.diasRestantes = value;
    }

    /**
     * Gets the value of the estado property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getEstado() {
        return estado;
    }

    /**
     * Sets the value of the estado property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setEstado(JAXBElement<String> value) {
        this.estado = value;
    }

    /**
     * Gets the value of the vigenciaDesde property.
     * 
     * @return possible object is {@link XMLGregorianCalendar }
     * 
     */
    public XMLGregorianCalendar getVigenciaDesde() {
        return vigenciaDesde;
    }

    /**
     * Sets the value of the vigenciaDesde property.
     * 
     * @param value allowed object is {@link XMLGregorianCalendar }
     * 
     */
    public void setVigenciaDesde(XMLGregorianCalendar value) {
        this.vigenciaDesde = value;
    }

    /**
     * Gets the value of the vigenciaHasta property.
     * 
     * @return possible object is {@link XMLGregorianCalendar }
     * 
     */
    public XMLGregorianCalendar getVigenciaHasta() {
        return vigenciaHasta;
    }

    /**
     * Sets the value of the vigenciaHasta property.
     * 
     * @param value allowed object is {@link XMLGregorianCalendar }
     * 
     */
    public void setVigenciaHasta(XMLGregorianCalendar value) {
        this.vigenciaHasta = value;
    }

}
