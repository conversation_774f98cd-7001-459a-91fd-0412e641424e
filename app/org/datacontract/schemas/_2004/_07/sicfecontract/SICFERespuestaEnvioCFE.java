
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for SICFERespuestaEnvioCFE complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SICFERespuestaEnvioCFE">
 *   &lt;complexContent>
 *     &lt;extension base="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta">
 *       &lt;sequence>
 *         &lt;element name="EsReceptorElectronico" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="IdCFE" type="{http://schemas.datacontract.org/2004/07/SICFEContract}IdCFE" minOccurs="0"/>
 *         &lt;element name="ImagenQR" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LinkQR" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="datosCAE" type="{http://schemas.datacontract.org/2004/07/SICFEContract}datosCAE" minOccurs="0"/>
 *         &lt;element name="hash" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="xml" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SICFERespuestaEnvioCFE", propOrder = { "esReceptorElectronico", "idCFE", "imagenQR", "linkQR",
        "datosCAE", "hash", "xml" })
public class SICFERespuestaEnvioCFE extends SICFERespuesta {

    @XmlElement(name = "EsReceptorElectronico")
    protected Boolean esReceptorElectronico;
    @XmlElementRef(name = "IdCFE", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<IdCFE> idCFE;
    @XmlElementRef(name = "ImagenQR", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> imagenQR;
    @XmlElementRef(name = "LinkQR", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> linkQR;
    @XmlElementRef(name = "datosCAE", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<DatosCAE> datosCAE;
    @XmlElementRef(name = "hash", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> hash;
    @XmlElementRef(name = "xml", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> xml;

    /**
     * Gets the value of the esReceptorElectronico property.
     * 
     * @return possible object is {@link Boolean }
     * 
     */
    public Boolean isEsReceptorElectronico() {
        return esReceptorElectronico;
    }

    /**
     * Sets the value of the esReceptorElectronico property.
     * 
     * @param value allowed object is {@link Boolean }
     * 
     */
    public void setEsReceptorElectronico(Boolean value) {
        this.esReceptorElectronico = value;
    }

    /**
     * Gets the value of the idCFE property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link IdCFE
     *         }{@code >}
     * 
     */
    public JAXBElement<IdCFE> getIdCFE() {
        return idCFE;
    }

    /**
     * Sets the value of the idCFE property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link IdCFE
     *              }{@code >}
     * 
     */
    public void setIdCFE(JAXBElement<IdCFE> value) {
        this.idCFE = value;
    }

    /**
     * Gets the value of the imagenQR property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getImagenQR() {
        return imagenQR;
    }

    /**
     * Sets the value of the imagenQR property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setImagenQR(JAXBElement<String> value) {
        this.imagenQR = value;
    }

    /**
     * Gets the value of the linkQR property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getLinkQR() {
        return linkQR;
    }

    /**
     * Sets the value of the linkQR property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setLinkQR(JAXBElement<String> value) {
        this.linkQR = value;
    }

    /**
     * Gets the value of the datosCAE property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link DatosCAE
     *         }{@code >}
     * 
     */
    public JAXBElement<DatosCAE> getDatosCAE() {
        return datosCAE;
    }

    /**
     * Sets the value of the datosCAE property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link DatosCAE
     *              }{@code >}
     * 
     */
    public void setDatosCAE(JAXBElement<DatosCAE> value) {
        this.datosCAE = value;
    }

    /**
     * Gets the value of the hash property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getHash() {
        return hash;
    }

    /**
     * Sets the value of the hash property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setHash(JAXBElement<String> value) {
        this.hash = value;
    }

    /**
     * Gets the value of the xml property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getXml() {
        return xml;
    }

    /**
     * Sets the value of the xml property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setXml(JAXBElement<String> value) {
        this.xml = value;
    }

}
