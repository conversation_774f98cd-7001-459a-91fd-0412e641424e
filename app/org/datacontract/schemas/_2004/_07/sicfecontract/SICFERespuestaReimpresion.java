
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for SICFERespuestaReimpresion complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SICFERespuestaReimpresion">
 *   &lt;complexContent>
 *     &lt;extension base="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta">
 *       &lt;sequence>
 *         &lt;element name="ResultadosPorCFE" type="{http://schemas.datacontract.org/2004/07/SICFEContract}ArrayOfResultadoReimpresion" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SICFERespuestaReimpresion", propOrder = { "resultadosPorCFE" })
public class SICFERespuestaReimpresion extends SICFERespuesta {

    @XmlElementRef(name = "ResultadosPorCFE", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<ArrayOfResultadoReimpresion> resultadosPorCFE;

    /**
     * Gets the value of the resultadosPorCFE property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link ArrayOfResultadoReimpresion }{@code >}
     * 
     */
    public JAXBElement<ArrayOfResultadoReimpresion> getResultadosPorCFE() {
        return resultadosPorCFE;
    }

    /**
     * Sets the value of the resultadosPorCFE property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link ArrayOfResultadoReimpresion }{@code >}
     * 
     */
    public void setResultadosPorCFE(JAXBElement<ArrayOfResultadoReimpresion> value) {
        this.resultadosPorCFE = value;
    }

}
