
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

/**
 * <p>
 * Java class for SICFERespuestaObtenerCFE complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SICFERespuestaObtenerCFE">
 *   &lt;complexContent>
 *     &lt;extension base="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta">
 *       &lt;sequence>
 *         &lt;element name="docreceptor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="estadoEnvioReceptorNE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="estadoenvioDGI" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="estadoenvioreceptor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="fechafirma" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="femision" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="fyhrecibido" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="numero" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="observado" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="referenciaerp" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="serie" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="sucursal" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="tipo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="xml" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SICFERespuestaObtenerCFE", propOrder = { "docreceptor", "estadoEnvioReceptorNE", "estadoenvioDGI",
        "estadoenvioreceptor", "fechafirma", "femision", "fyhrecibido", "numero", "observado", "referenciaerp", "serie",
        "sucursal", "tipo", "xml" })
public class SICFERespuestaObtenerCFE extends SICFERespuesta {

    @XmlElementRef(name = "docreceptor", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> docreceptor;
    @XmlElementRef(name = "estadoEnvioReceptorNE", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> estadoEnvioReceptorNE;
    @XmlElementRef(name = "estadoenvioDGI", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> estadoenvioDGI;
    @XmlElementRef(name = "estadoenvioreceptor", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> estadoenvioreceptor;
    @XmlElementRef(name = "fechafirma", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> fechafirma;
    @XmlElementRef(name = "femision", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> femision;
    @XmlElementRef(name = "fyhrecibido", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<XMLGregorianCalendar> fyhrecibido;
    protected Integer numero;
    protected Integer observado;
    @XmlElementRef(name = "referenciaerp", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> referenciaerp;
    @XmlElementRef(name = "serie", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> serie;
    protected Integer sucursal;
    protected Integer tipo;
    @XmlElementRef(name = "xml", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> xml;

    /**
     * Gets the value of the docreceptor property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getDocreceptor() {
        return docreceptor;
    }

    /**
     * Sets the value of the docreceptor property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setDocreceptor(JAXBElement<String> value) {
        this.docreceptor = value;
    }

    /**
     * Gets the value of the estadoEnvioReceptorNE property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getEstadoEnvioReceptorNE() {
        return estadoEnvioReceptorNE;
    }

    /**
     * Sets the value of the estadoEnvioReceptorNE property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setEstadoEnvioReceptorNE(JAXBElement<String> value) {
        this.estadoEnvioReceptorNE = value;
    }

    /**
     * Gets the value of the estadoenvioDGI property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getEstadoenvioDGI() {
        return estadoenvioDGI;
    }

    /**
     * Sets the value of the estadoenvioDGI property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setEstadoenvioDGI(JAXBElement<String> value) {
        this.estadoenvioDGI = value;
    }

    /**
     * Gets the value of the estadoenvioreceptor property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getEstadoenvioreceptor() {
        return estadoenvioreceptor;
    }

    /**
     * Sets the value of the estadoenvioreceptor property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setEstadoenvioreceptor(JAXBElement<String> value) {
        this.estadoenvioreceptor = value;
    }

    /**
     * Gets the value of the fechafirma property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     */
    public JAXBElement<XMLGregorianCalendar> getFechafirma() {
        return fechafirma;
    }

    /**
     * Sets the value of the fechafirma property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     */
    public void setFechafirma(JAXBElement<XMLGregorianCalendar> value) {
        this.fechafirma = value;
    }

    /**
     * Gets the value of the femision property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     */
    public JAXBElement<XMLGregorianCalendar> getFemision() {
        return femision;
    }

    /**
     * Sets the value of the femision property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     */
    public void setFemision(JAXBElement<XMLGregorianCalendar> value) {
        this.femision = value;
    }

    /**
     * Gets the value of the fyhrecibido property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     */
    public JAXBElement<XMLGregorianCalendar> getFyhrecibido() {
        return fyhrecibido;
    }

    /**
     * Sets the value of the fyhrecibido property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link XMLGregorianCalendar }{@code >}
     * 
     */
    public void setFyhrecibido(JAXBElement<XMLGregorianCalendar> value) {
        this.fyhrecibido = value;
    }

    /**
     * Gets the value of the numero property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getNumero() {
        return numero;
    }

    /**
     * Sets the value of the numero property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setNumero(Integer value) {
        this.numero = value;
    }

    /**
     * Gets the value of the observado property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getObservado() {
        return observado;
    }

    /**
     * Sets the value of the observado property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setObservado(Integer value) {
        this.observado = value;
    }

    /**
     * Gets the value of the referenciaerp property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getReferenciaerp() {
        return referenciaerp;
    }

    /**
     * Sets the value of the referenciaerp property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setReferenciaerp(JAXBElement<String> value) {
        this.referenciaerp = value;
    }

    /**
     * Gets the value of the serie property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getSerie() {
        return serie;
    }

    /**
     * Sets the value of the serie property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setSerie(JAXBElement<String> value) {
        this.serie = value;
    }

    /**
     * Gets the value of the sucursal property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getSucursal() {
        return sucursal;
    }

    /**
     * Sets the value of the sucursal property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setSucursal(Integer value) {
        this.sucursal = value;
    }

    /**
     * Gets the value of the tipo property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getTipo() {
        return tipo;
    }

    /**
     * Sets the value of the tipo property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setTipo(Integer value) {
        this.tipo = value;
    }

    /**
     * Gets the value of the xml property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getXml() {
        return xml;
    }

    /**
     * Sets the value of the xml property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setXml(JAXBElement<String> value) {
        this.xml = value;
    }

}
