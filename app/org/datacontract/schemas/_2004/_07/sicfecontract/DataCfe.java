
package org.datacontract.schemas._2004._07.sicfecontract;

import java.math.BigDecimal;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

/**
 * <p>
 * Java class for DataCfe complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="DataCfe">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="EstadoDGI" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="FechaEmision" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="MontoTotal" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="Numero" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="ReferenciaErp" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Serie" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="TipoCfe" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DataCfe", propOrder = { "estadoDGI", "fechaEmision", "montoTotal", "numero", "referenciaErp", "serie",
        "tipoCfe" })
public class DataCfe {

    @XmlElementRef(name = "EstadoDGI", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> estadoDGI;
    @XmlElement(name = "FechaEmision")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar fechaEmision;
    @XmlElement(name = "MontoTotal")
    protected BigDecimal montoTotal;
    @XmlElement(name = "Numero")
    protected Integer numero;
    @XmlElementRef(name = "ReferenciaErp", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> referenciaErp;
    @XmlElementRef(name = "Serie", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> serie;
    @XmlElement(name = "TipoCfe")
    protected Integer tipoCfe;

    /**
     * Gets the value of the estadoDGI property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getEstadoDGI() {
        return estadoDGI;
    }

    /**
     * Sets the value of the estadoDGI property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setEstadoDGI(JAXBElement<String> value) {
        this.estadoDGI = value;
    }

    /**
     * Gets the value of the fechaEmision property.
     * 
     * @return possible object is {@link XMLGregorianCalendar }
     * 
     */
    public XMLGregorianCalendar getFechaEmision() {
        return fechaEmision;
    }

    /**
     * Sets the value of the fechaEmision property.
     * 
     * @param value allowed object is {@link XMLGregorianCalendar }
     * 
     */
    public void setFechaEmision(XMLGregorianCalendar value) {
        this.fechaEmision = value;
    }

    /**
     * Gets the value of the montoTotal property.
     * 
     * @return possible object is {@link BigDecimal }
     * 
     */
    public BigDecimal getMontoTotal() {
        return montoTotal;
    }

    /**
     * Sets the value of the montoTotal property.
     * 
     * @param value allowed object is {@link BigDecimal }
     * 
     */
    public void setMontoTotal(BigDecimal value) {
        this.montoTotal = value;
    }

    /**
     * Gets the value of the numero property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getNumero() {
        return numero;
    }

    /**
     * Sets the value of the numero property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setNumero(Integer value) {
        this.numero = value;
    }

    /**
     * Gets the value of the referenciaErp property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getReferenciaErp() {
        return referenciaErp;
    }

    /**
     * Sets the value of the referenciaErp property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setReferenciaErp(JAXBElement<String> value) {
        this.referenciaErp = value;
    }

    /**
     * Gets the value of the serie property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getSerie() {
        return serie;
    }

    /**
     * Sets the value of the serie property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setSerie(JAXBElement<String> value) {
        this.serie = value;
    }

    /**
     * Gets the value of the tipoCfe property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getTipoCfe() {
        return tipoCfe;
    }

    /**
     * Sets the value of the tipoCfe property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setTipoCfe(Integer value) {
        this.tipoCfe = value;
    }

}
