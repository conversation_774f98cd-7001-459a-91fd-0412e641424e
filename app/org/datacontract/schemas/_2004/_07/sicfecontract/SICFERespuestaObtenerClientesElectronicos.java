
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for SICFERespuestaObtenerClientesElectronicos complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SICFERespuestaObtenerClientesElectronicos">
 *   &lt;complexContent>
 *     &lt;extension base="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta">
 *       &lt;sequence>
 *         &lt;element name="Clientes" type="{http://schemas.datacontract.org/2004/07/SICFEContract}ArrayOfClienteElectronico" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SICFERespuestaObtenerClientesElectronicos", propOrder = { "clientes" })
public class SICFERespuestaObtenerClientesElectronicos extends SICFERespuesta {

    @XmlElementRef(name = "Clientes", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<ArrayOfClienteElectronico> clientes;

    /**
     * Gets the value of the clientes property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link ArrayOfClienteElectronico }{@code >}
     * 
     */
    public JAXBElement<ArrayOfClienteElectronico> getClientes() {
        return clientes;
    }

    /**
     * Sets the value of the clientes property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link ArrayOfClienteElectronico }{@code >}
     * 
     */
    public void setClientes(JAXBElement<ArrayOfClienteElectronico> value) {
        this.clientes = value;
    }

}
