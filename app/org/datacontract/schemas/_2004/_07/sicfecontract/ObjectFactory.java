
package org.datacontract.schemas._2004._07.sicfecontract;

import java.math.BigDecimal;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.namespace.QName;
import com.microsoft.schemas._2003._10.serialization.arrays.ArrayOfstring;

/**
 * This object contains factory methods for each Java content interface and Java
 * element interface generated in the
 * org.datacontract.schemas._2004._07.sicfecontract package.
 * <p>
 * An ObjectFactory allows you to programatically construct new instances of the
 * Java representation for XML content. The Java representation of XML content
 * can consist of schema derived interfaces and classes representing the binding
 * of schema type definitions, element declarations and model groups. Factory
 * methods for each of these are provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _ClienteElectronico_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ClienteElectronico");
    private final static QName _DataNumerador_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "DataNumerador");
    private final static QName _ArrayOfDatoCAE_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfDatoCAE");
    private final static QName _RespuestaObtenerCFEsRecibidos_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "RespuestaObtenerCFEsRecibidos");
    private final static QName _ArrayOfProveedorElectronico_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfProveedorElectronico");
    private final static QName _SICFERespuestaVersion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaVersion");
    private final static QName _SicfeRespuestaConsolidar_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SicfeRespuestaConsolidar");
    private final static QName _SICFERespuesta_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuesta");
    private final static QName _ArrayOfDataCfe_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfDataCfe");
    private final static QName _DatosCAE_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "datosCAE");
    private final static QName _SICFERespuestaReimpresion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaReimpresion");
    private final static QName _ArrayOfResultadoReimpresion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfResultadoReimpresion");
    private final static QName _SICFERespuestaObtenerXML_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaObtenerXML");
    private final static QName _ReservarNro_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "ReservarNro");
    private final static QName _RespuestaObtenerCAE_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "RespuestaObtenerCAE");
    private final static QName _RespuestaObtenerCFEsRecibidosExtendido_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "RespuestaObtenerCFEsRecibidosExtendido");
    private final static QName _SICFERespuestaBuffer_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaBuffer");
    private final static QName _SICFERespuestaEmisorReceptorElectronico_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaEmisorReceptorElectronico");
    private final static QName _ProveedorElectronico_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ProveedorElectronico");
    private final static QName _ArrayOfCFERecibidoDTO_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfCFERecibidoDTO");
    private final static QName _Certificado_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "Certificado");
    private final static QName _ObtenerTemplateImpresionRespuesta_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ObtenerTemplateImpresionRespuesta");
    private final static QName _SICFERespuestaObtenerProveedoresElectronicos_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaObtenerProveedoresElectronicos");
    private final static QName _SICFERespuestaCertificados_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaCertificados");
    private final static QName _ArrayOfDataNumerador_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfDataNumerador");
    private final static QName _ArrayOfDatoTemplateImpresion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfDatoTemplateImpresion");
    private final static QName _DatoTemplateImpresion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "DatoTemplateImpresion");
    private final static QName _SICFERespuestaEstadoCFE_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaEstadoCFE");
    private final static QName _ResultadoReimpresion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ResultadoReimpresion");
    private final static QName _CFERecibidoExtendidoDTO_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "CFERecibidoExtendidoDTO");
    private final static QName _ArrayOfCertificado_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfCertificado");
    private final static QName _ArrayOfCFERecibidoExtendidoDTO_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfCFERecibidoExtendidoDTO");
    private final static QName _DataCfe_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "DataCfe");
    private final static QName _RespuestaObtenerRecursosImpresion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "RespuestaObtenerRecursosImpresion");
    private final static QName _CFERecibidoDTO_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "CFERecibidoDTO");
    private final static QName _Impresora_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "Impresora");
    private final static QName _DatoCAE_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "DatoCAE");
    private final static QName _SICFERespuestaEnvioCFE_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaEnvioCFE");
    private final static QName _SICFERespuestaObtenerCFE_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaObtenerCFE");
    private final static QName _IdCFE_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "IdCFE");
    private final static QName _ArrayOfRechazoAcuseRecibo_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfRechazoAcuseRecibo");
    private final static QName _SICFERespuestaObtenerClientesElectronicos_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaObtenerClientesElectronicos");
    private final static QName _DataReceptorNoElectronico_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "DataReceptorNoElectronico");
    private final static QName _SICFERespuestaPing_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaPing");
    private final static QName _ArrayOfClienteElectronico_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfClienteElectronico");
    private final static QName _SICFERespuestaDatosEmisorReceptor_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "SICFERespuestaDatosEmisorReceptor");
    private final static QName _RechazoAcuseRecibo_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "RechazoAcuseRecibo");
    private final static QName _ArrayOfImpresora_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ArrayOfImpresora");
    private final static QName _RechazoAcuseReciboCodigo_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Codigo");
    private final static QName _RechazoAcuseReciboDescripcion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Descripcion");
    private final static QName _SICFERespuestaDatosEmisorReceptorFechaFin_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "FechaFin");
    private final static QName _SICFERespuestaDatosEmisorReceptorRUC_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "RUC");
    private final static QName _SICFERespuestaDatosEmisorReceptorFechaInicio_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "FechaInicio");
    private final static QName _SICFERespuestaDatosEmisorReceptorFechaFinTransicion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "FechaFinTransicion");
    private final static QName _SICFERespuestaDatosEmisorReceptorEmailIntercambio_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "EmailIntercambio");
    private final static QName _SICFERespuestaDatosEmisorReceptorRazonSocial_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "RazonSocial");
    private final static QName _DatoTemplateImpresionCodTemplate_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "CodTemplate");
    private final static QName _DatoTemplateImpresionEstiloTemplate_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "EstiloTemplate");
    private final static QName _DatoTemplateImpresionTemplate_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Template");
    private final static QName _DatoTemplateImpresionTemplateNombre_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "TemplateNombre");
    private final static QName _SICFERespuestaEnvioCFEXml_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "xml");
    private final static QName _SICFERespuestaEnvioCFELinkQR_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "LinkQR");
    private final static QName _SICFERespuestaEnvioCFEHash_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "hash");
    private final static QName _SICFERespuestaEnvioCFEImagenQR_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ImagenQR");
    private final static QName _SICFERespuestaObtenerCFEDocreceptor_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "docreceptor");
    private final static QName _SICFERespuestaObtenerCFEEstadoEnvioReceptorNE_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "estadoEnvioReceptorNE");
    private final static QName _SICFERespuestaObtenerCFEFechafirma_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "fechafirma");
    private final static QName _SICFERespuestaObtenerCFEEstadoenvioDGI_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "estadoenvioDGI");
    private final static QName _SICFERespuestaObtenerCFEEstadoenvioreceptor_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "estadoenvioreceptor");
    private final static QName _SICFERespuestaObtenerCFEFyhrecibido_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "fyhrecibido");
    private final static QName _SICFERespuestaObtenerCFESerie_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "serie");
    private final static QName _SICFERespuestaObtenerCFEFemision_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "femision");
    private final static QName _SICFERespuestaObtenerCFEReferenciaerp_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "referenciaerp");
    private final static QName _DatoCAENauto_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "nauto");
    private final static QName _DatoCAEFanulacion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "fanulacion");
    private final static QName _DatoCAEUltnro_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "ultnro");
    private final static QName _DatoCAEEstado_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "estado");
    private final static QName _DatoCAEDnro_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "dnro");
    private final static QName _DatoCAESucursal_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "sucursal");
    private final static QName _DatoCAETipo_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "Tipo");
    private final static QName _DatoCAEHnro_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "hnro");
    private final static QName _DatoCAETauto_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "tauto");
    private final static QName _DatoCAEIdNumeradorClave_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "IdNumeradorClave");
    private final static QName _DatoCAETenant_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "tenant");
    private final static QName _DatoCAECantusados_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "cantusados");
    private final static QName _DatoCAEFvto_QNAME = new QName("http://schemas.datacontract.org/2004/07/SICFEContract",
            "fvto");
    private final static QName _SICFERespuestaVersionVersionSvc_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "VersionSvc");
    private final static QName _SICFERespuestaVersionVersionBD_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "VersionBD");
    private final static QName _ReservarNroSerie_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Serie");
    private final static QName _CertificadoEstado_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Estado");
    private final static QName _SICFERespuestaBufferBuffer_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Buffer");
    private final static QName _ProveedorElectronicoRuc_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Ruc");
    private final static QName _ProveedorElectronicoEmail_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Email");
    private final static QName _DataReceptorNoElectronicoNombre_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Nombre");
    private final static QName _DataReceptorNoElectronicoCodPais_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "CodPais");
    private final static QName _DataReceptorNoElectronicoDoc_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Doc");
    private final static QName _CFERecibidoDTOFechaEmision_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "FechaEmision");
    private final static QName _CFERecibidoDTOXML_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "XML");
    private final static QName _CFERecibidoDTORucEmisor_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "RucEmisor");
    private final static QName _DataCfeEstadoDGI_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "EstadoDGI");
    private final static QName _DataCfeReferenciaErp_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ReferenciaErp");
    private final static QName _RespuestaObtenerCAECAEList_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "CAEList");
    private final static QName _RespuestaObtenerCAEListaNumerador_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ListaNumerador");
    private final static QName _SICFERespuestaObtenerProveedoresElectronicosProveedores_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Proveedores");
    private final static QName _ImpresoraPapel_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Papel");
    private final static QName _ResultadoReimpresionCFE_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "CFE");
    private final static QName _DataNumeradorClave_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Clave");
    private final static QName _DataNumeradorNombreInstalacion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "NombreInstalacion");
    private final static QName _DataNumeradorTenant_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Tenant");
    private final static QName _RespuestaObtenerRecursosImpresionListRecursosImpresion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ListRecursosImpresion");
    private final static QName _SICFERespuestaCertificadosCertificados_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Certificados");
    private final static QName _RespuestaObtenerCFEsRecibidosCFEsRecibidos_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "CFEsRecibidos");
    private final static QName _SICFERespuestaEstadoCFECodRechazo_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "CodRechazo");
    private final static QName _SICFERespuestaEstadoCFEMotRechazo_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "MotRechazo");
    private final static QName _SICFERespuestaReimpresionResultadosPorCFE_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ResultadosPorCFE");
    private final static QName _ObtenerTemplateImpresionRespuestaListTemplatesImpresion_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ListTemplatesImpresion");
    private final static QName _ObtenerTemplateImpresionRespuestaListTemplatesImpresionConClave_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "ListTemplatesImpresionConClave");
    private final static QName _CFERecibidoExtendidoDTONombreComercial_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "NombreComercial");
    private final static QName _CFERecibidoExtendidoDTOMoneda_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Moneda");
    private final static QName _CFERecibidoExtendidoDTOFechaVencimientoCAE_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "FechaVencimientoCAE");
    private final static QName _CFERecibidoExtendidoDTOFechaVencimiento_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "FechaVencimiento");
    private final static QName _CFERecibidoExtendidoDTOFormaPago_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "FormaPago");
    private final static QName _SICFERespuestaObtenerClientesElectronicosClientes_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Clientes");
    private final static QName _IdCFERucemisor_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "rucemisor");
    private final static QName _SicfeRespuestaConsolidarComprobantes_QNAME = new QName(
            "http://schemas.datacontract.org/2004/07/SICFEContract", "Comprobantes");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema
     * derived classes for package: org.datacontract.schemas._2004._07.sicfecontract
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link CFERecibidoDTO }
     * 
     */
    public CFERecibidoDTO createCFERecibidoDTO() {
        return new CFERecibidoDTO();
    }

    /**
     * Create an instance of {@link Impresora }
     * 
     */
    public Impresora createImpresora() {
        return new Impresora();
    }

    /**
     * Create an instance of {@link ArrayOfResultadoReimpresion }
     * 
     */
    public ArrayOfResultadoReimpresion createArrayOfResultadoReimpresion() {
        return new ArrayOfResultadoReimpresion();
    }

    /**
     * Create an instance of {@link SICFERespuestaObtenerXML }
     * 
     */
    public SICFERespuestaObtenerXML createSICFERespuestaObtenerXML() {
        return new SICFERespuestaObtenerXML();
    }

    /**
     * Create an instance of {@link SICFERespuestaReimpresion }
     * 
     */
    public SICFERespuestaReimpresion createSICFERespuestaReimpresion() {
        return new SICFERespuestaReimpresion();
    }

    /**
     * Create an instance of {@link DataCfe }
     * 
     */
    public DataCfe createDataCfe() {
        return new DataCfe();
    }

    /**
     * Create an instance of {@link RespuestaObtenerRecursosImpresion }
     * 
     */
    public RespuestaObtenerRecursosImpresion createRespuestaObtenerRecursosImpresion() {
        return new RespuestaObtenerRecursosImpresion();
    }

    /**
     * Create an instance of {@link SICFERespuestaEnvioCFE }
     * 
     */
    public SICFERespuestaEnvioCFE createSICFERespuestaEnvioCFE() {
        return new SICFERespuestaEnvioCFE();
    }

    /**
     * Create an instance of {@link RespuestaObtenerCFEsRecibidosExtendido }
     * 
     */
    public RespuestaObtenerCFEsRecibidosExtendido createRespuestaObtenerCFEsRecibidosExtendido() {
        return new RespuestaObtenerCFEsRecibidosExtendido();
    }

    /**
     * Create an instance of {@link SICFERespuestaObtenerCFE }
     * 
     */
    public SICFERespuestaObtenerCFE createSICFERespuestaObtenerCFE() {
        return new SICFERespuestaObtenerCFE();
    }

    /**
     * Create an instance of {@link SICFERespuestaBuffer }
     * 
     */
    public SICFERespuestaBuffer createSICFERespuestaBuffer() {
        return new SICFERespuestaBuffer();
    }

    /**
     * Create an instance of {@link ReservarNro }
     * 
     */
    public ReservarNro createReservarNro() {
        return new ReservarNro();
    }

    /**
     * Create an instance of {@link DatoCAE }
     * 
     */
    public DatoCAE createDatoCAE() {
        return new DatoCAE();
    }

    /**
     * Create an instance of {@link RespuestaObtenerCAE }
     * 
     */
    public RespuestaObtenerCAE createRespuestaObtenerCAE() {
        return new RespuestaObtenerCAE();
    }

    /**
     * Create an instance of {@link DataReceptorNoElectronico }
     * 
     */
    public DataReceptorNoElectronico createDataReceptorNoElectronico() {
        return new DataReceptorNoElectronico();
    }

    /**
     * Create an instance of {@link SICFERespuestaPing }
     * 
     */
    public SICFERespuestaPing createSICFERespuestaPing() {
        return new SICFERespuestaPing();
    }

    /**
     * Create an instance of {@link SICFERespuestaEmisorReceptorElectronico }
     * 
     */
    public SICFERespuestaEmisorReceptorElectronico createSICFERespuestaEmisorReceptorElectronico() {
        return new SICFERespuestaEmisorReceptorElectronico();
    }

    /**
     * Create an instance of {@link IdCFE }
     * 
     */
    public IdCFE createIdCFE() {
        return new IdCFE();
    }

    /**
     * Create an instance of {@link ArrayOfRechazoAcuseRecibo }
     * 
     */
    public ArrayOfRechazoAcuseRecibo createArrayOfRechazoAcuseRecibo() {
        return new ArrayOfRechazoAcuseRecibo();
    }

    /**
     * Create an instance of {@link SICFERespuestaObtenerClientesElectronicos }
     * 
     */
    public SICFERespuestaObtenerClientesElectronicos createSICFERespuestaObtenerClientesElectronicos() {
        return new SICFERespuestaObtenerClientesElectronicos();
    }

    /**
     * Create an instance of {@link ArrayOfCFERecibidoDTO }
     * 
     */
    public ArrayOfCFERecibidoDTO createArrayOfCFERecibidoDTO() {
        return new ArrayOfCFERecibidoDTO();
    }

    /**
     * Create an instance of {@link Certificado }
     * 
     */
    public Certificado createCertificado() {
        return new Certificado();
    }

    /**
     * Create an instance of {@link ArrayOfImpresora }
     * 
     */
    public ArrayOfImpresora createArrayOfImpresora() {
        return new ArrayOfImpresora();
    }

    /**
     * Create an instance of {@link ObtenerTemplateImpresionRespuesta }
     * 
     */
    public ObtenerTemplateImpresionRespuesta createObtenerTemplateImpresionRespuesta() {
        return new ObtenerTemplateImpresionRespuesta();
    }

    /**
     * Create an instance of {@link SICFERespuestaObtenerProveedoresElectronicos }
     * 
     */
    public SICFERespuestaObtenerProveedoresElectronicos createSICFERespuestaObtenerProveedoresElectronicos() {
        return new SICFERespuestaObtenerProveedoresElectronicos();
    }

    /**
     * Create an instance of {@link SICFERespuestaCertificados }
     * 
     */
    public SICFERespuestaCertificados createSICFERespuestaCertificados() {
        return new SICFERespuestaCertificados();
    }

    /**
     * Create an instance of {@link ArrayOfClienteElectronico }
     * 
     */
    public ArrayOfClienteElectronico createArrayOfClienteElectronico() {
        return new ArrayOfClienteElectronico();
    }

    /**
     * Create an instance of {@link SICFERespuestaDatosEmisorReceptor }
     * 
     */
    public SICFERespuestaDatosEmisorReceptor createSICFERespuestaDatosEmisorReceptor() {
        return new SICFERespuestaDatosEmisorReceptor();
    }

    /**
     * Create an instance of {@link RechazoAcuseRecibo }
     * 
     */
    public RechazoAcuseRecibo createRechazoAcuseRecibo() {
        return new RechazoAcuseRecibo();
    }

    /**
     * Create an instance of {@link ProveedorElectronico }
     * 
     */
    public ProveedorElectronico createProveedorElectronico() {
        return new ProveedorElectronico();
    }

    /**
     * Create an instance of {@link DatoTemplateImpresion }
     * 
     */
    public DatoTemplateImpresion createDatoTemplateImpresion() {
        return new DatoTemplateImpresion();
    }

    /**
     * Create an instance of {@link ArrayOfDatoCAE }
     * 
     */
    public ArrayOfDatoCAE createArrayOfDatoCAE() {
        return new ArrayOfDatoCAE();
    }

    /**
     * Create an instance of {@link ArrayOfDataNumerador }
     * 
     */
    public ArrayOfDataNumerador createArrayOfDataNumerador() {
        return new ArrayOfDataNumerador();
    }

    /**
     * Create an instance of {@link ArrayOfDatoTemplateImpresion }
     * 
     */
    public ArrayOfDatoTemplateImpresion createArrayOfDatoTemplateImpresion() {
        return new ArrayOfDatoTemplateImpresion();
    }

    /**
     * Create an instance of {@link ClienteElectronico }
     * 
     */
    public ClienteElectronico createClienteElectronico() {
        return new ClienteElectronico();
    }

    /**
     * Create an instance of {@link DataNumerador }
     * 
     */
    public DataNumerador createDataNumerador() {
        return new DataNumerador();
    }

    /**
     * Create an instance of {@link SicfeRespuestaConsolidar }
     * 
     */
    public SicfeRespuestaConsolidar createSicfeRespuestaConsolidar() {
        return new SicfeRespuestaConsolidar();
    }

    /**
     * Create an instance of {@link RespuestaObtenerCFEsRecibidos }
     * 
     */
    public RespuestaObtenerCFEsRecibidos createRespuestaObtenerCFEsRecibidos() {
        return new RespuestaObtenerCFEsRecibidos();
    }

    /**
     * Create an instance of {@link ArrayOfProveedorElectronico }
     * 
     */
    public ArrayOfProveedorElectronico createArrayOfProveedorElectronico() {
        return new ArrayOfProveedorElectronico();
    }

    /**
     * Create an instance of {@link SICFERespuestaEstadoCFE }
     * 
     */
    public SICFERespuestaEstadoCFE createSICFERespuestaEstadoCFE() {
        return new SICFERespuestaEstadoCFE();
    }

    /**
     * Create an instance of {@link SICFERespuestaVersion }
     * 
     */
    public SICFERespuestaVersion createSICFERespuestaVersion() {
        return new SICFERespuestaVersion();
    }

    /**
     * Create an instance of {@link ResultadoReimpresion }
     * 
     */
    public ResultadoReimpresion createResultadoReimpresion() {
        return new ResultadoReimpresion();
    }

    /**
     * Create an instance of {@link CFERecibidoExtendidoDTO }
     * 
     */
    public CFERecibidoExtendidoDTO createCFERecibidoExtendidoDTO() {
        return new CFERecibidoExtendidoDTO();
    }

    /**
     * Create an instance of {@link SICFERespuesta }
     * 
     */
    public SICFERespuesta createSICFERespuesta() {
        return new SICFERespuesta();
    }

    /**
     * Create an instance of {@link DatosCAE }
     * 
     */
    public DatosCAE createDatosCAE() {
        return new DatosCAE();
    }

    /**
     * Create an instance of {@link ArrayOfCertificado }
     * 
     */
    public ArrayOfCertificado createArrayOfCertificado() {
        return new ArrayOfCertificado();
    }

    /**
     * Create an instance of {@link ArrayOfCFERecibidoExtendidoDTO }
     * 
     */
    public ArrayOfCFERecibidoExtendidoDTO createArrayOfCFERecibidoExtendidoDTO() {
        return new ArrayOfCFERecibidoExtendidoDTO();
    }

    /**
     * Create an instance of {@link ArrayOfDataCfe }
     * 
     */
    public ArrayOfDataCfe createArrayOfDataCfe() {
        return new ArrayOfDataCfe();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ClienteElectronico
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ClienteElectronico")
    public JAXBElement<ClienteElectronico> createClienteElectronico(ClienteElectronico value) {
        return new JAXBElement<ClienteElectronico>(_ClienteElectronico_QNAME, ClienteElectronico.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DataNumerador
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "DataNumerador")
    public JAXBElement<DataNumerador> createDataNumerador(DataNumerador value) {
        return new JAXBElement<DataNumerador>(_DataNumerador_QNAME, DataNumerador.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfDatoCAE
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfDatoCAE")
    public JAXBElement<ArrayOfDatoCAE> createArrayOfDatoCAE(ArrayOfDatoCAE value) {
        return new JAXBElement<ArrayOfDatoCAE>(_ArrayOfDatoCAE_QNAME, ArrayOfDatoCAE.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link RespuestaObtenerCFEsRecibidos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RespuestaObtenerCFEsRecibidos")
    public JAXBElement<RespuestaObtenerCFEsRecibidos> createRespuestaObtenerCFEsRecibidos(
            RespuestaObtenerCFEsRecibidos value) {
        return new JAXBElement<RespuestaObtenerCFEsRecibidos>(_RespuestaObtenerCFEsRecibidos_QNAME,
                RespuestaObtenerCFEsRecibidos.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfProveedorElectronico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfProveedorElectronico")
    public JAXBElement<ArrayOfProveedorElectronico> createArrayOfProveedorElectronico(
            ArrayOfProveedorElectronico value) {
        return new JAXBElement<ArrayOfProveedorElectronico>(_ArrayOfProveedorElectronico_QNAME,
                ArrayOfProveedorElectronico.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaVersion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaVersion")
    public JAXBElement<SICFERespuestaVersion> createSICFERespuestaVersion(SICFERespuestaVersion value) {
        return new JAXBElement<SICFERespuestaVersion>(_SICFERespuestaVersion_QNAME, SICFERespuestaVersion.class, null,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SicfeRespuestaConsolidar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SicfeRespuestaConsolidar")
    public JAXBElement<SicfeRespuestaConsolidar> createSicfeRespuestaConsolidar(SicfeRespuestaConsolidar value) {
        return new JAXBElement<SicfeRespuestaConsolidar>(_SicfeRespuestaConsolidar_QNAME,
                SicfeRespuestaConsolidar.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuesta
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuesta")
    public JAXBElement<SICFERespuesta> createSICFERespuesta(SICFERespuesta value) {
        return new JAXBElement<SICFERespuesta>(_SICFERespuesta_QNAME, SICFERespuesta.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfDataCfe
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfDataCfe")
    public JAXBElement<ArrayOfDataCfe> createArrayOfDataCfe(ArrayOfDataCfe value) {
        return new JAXBElement<ArrayOfDataCfe>(_ArrayOfDataCfe_QNAME, ArrayOfDataCfe.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DatosCAE
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "datosCAE")
    public JAXBElement<DatosCAE> createDatosCAE(DatosCAE value) {
        return new JAXBElement<DatosCAE>(_DatosCAE_QNAME, DatosCAE.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaReimpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaReimpresion")
    public JAXBElement<SICFERespuestaReimpresion> createSICFERespuestaReimpresion(SICFERespuestaReimpresion value) {
        return new JAXBElement<SICFERespuestaReimpresion>(_SICFERespuestaReimpresion_QNAME,
                SICFERespuestaReimpresion.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfResultadoReimpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfResultadoReimpresion")
    public JAXBElement<ArrayOfResultadoReimpresion> createArrayOfResultadoReimpresion(
            ArrayOfResultadoReimpresion value) {
        return new JAXBElement<ArrayOfResultadoReimpresion>(_ArrayOfResultadoReimpresion_QNAME,
                ArrayOfResultadoReimpresion.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaObtenerXML }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaObtenerXML")
    public JAXBElement<SICFERespuestaObtenerXML> createSICFERespuestaObtenerXML(SICFERespuestaObtenerXML value) {
        return new JAXBElement<SICFERespuestaObtenerXML>(_SICFERespuestaObtenerXML_QNAME,
                SICFERespuestaObtenerXML.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ReservarNro
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ReservarNro")
    public JAXBElement<ReservarNro> createReservarNro(ReservarNro value) {
        return new JAXBElement<ReservarNro>(_ReservarNro_QNAME, ReservarNro.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RespuestaObtenerCAE
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RespuestaObtenerCAE")
    public JAXBElement<RespuestaObtenerCAE> createRespuestaObtenerCAE(RespuestaObtenerCAE value) {
        return new JAXBElement<RespuestaObtenerCAE>(_RespuestaObtenerCAE_QNAME, RespuestaObtenerCAE.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link RespuestaObtenerCFEsRecibidosExtendido }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RespuestaObtenerCFEsRecibidosExtendido")
    public JAXBElement<RespuestaObtenerCFEsRecibidosExtendido> createRespuestaObtenerCFEsRecibidosExtendido(
            RespuestaObtenerCFEsRecibidosExtendido value) {
        return new JAXBElement<RespuestaObtenerCFEsRecibidosExtendido>(_RespuestaObtenerCFEsRecibidosExtendido_QNAME,
                RespuestaObtenerCFEsRecibidosExtendido.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaBuffer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaBuffer")
    public JAXBElement<SICFERespuestaBuffer> createSICFERespuestaBuffer(SICFERespuestaBuffer value) {
        return new JAXBElement<SICFERespuestaBuffer>(_SICFERespuestaBuffer_QNAME, SICFERespuestaBuffer.class, null,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEmisorReceptorElectronico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaEmisorReceptorElectronico")
    public JAXBElement<SICFERespuestaEmisorReceptorElectronico> createSICFERespuestaEmisorReceptorElectronico(
            SICFERespuestaEmisorReceptorElectronico value) {
        return new JAXBElement<SICFERespuestaEmisorReceptorElectronico>(_SICFERespuestaEmisorReceptorElectronico_QNAME,
                SICFERespuestaEmisorReceptorElectronico.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ProveedorElectronico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ProveedorElectronico")
    public JAXBElement<ProveedorElectronico> createProveedorElectronico(ProveedorElectronico value) {
        return new JAXBElement<ProveedorElectronico>(_ProveedorElectronico_QNAME, ProveedorElectronico.class, null,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfCFERecibidoDTO }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfCFERecibidoDTO")
    public JAXBElement<ArrayOfCFERecibidoDTO> createArrayOfCFERecibidoDTO(ArrayOfCFERecibidoDTO value) {
        return new JAXBElement<ArrayOfCFERecibidoDTO>(_ArrayOfCFERecibidoDTO_QNAME, ArrayOfCFERecibidoDTO.class, null,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Certificado
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Certificado")
    public JAXBElement<Certificado> createCertificado(Certificado value) {
        return new JAXBElement<Certificado>(_Certificado_QNAME, Certificado.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ObtenerTemplateImpresionRespuesta }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ObtenerTemplateImpresionRespuesta")
    public JAXBElement<ObtenerTemplateImpresionRespuesta> createObtenerTemplateImpresionRespuesta(
            ObtenerTemplateImpresionRespuesta value) {
        return new JAXBElement<ObtenerTemplateImpresionRespuesta>(_ObtenerTemplateImpresionRespuesta_QNAME,
                ObtenerTemplateImpresionRespuesta.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaObtenerProveedoresElectronicos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaObtenerProveedoresElectronicos")
    public JAXBElement<SICFERespuestaObtenerProveedoresElectronicos> createSICFERespuestaObtenerProveedoresElectronicos(
            SICFERespuestaObtenerProveedoresElectronicos value) {
        return new JAXBElement<SICFERespuestaObtenerProveedoresElectronicos>(
                _SICFERespuestaObtenerProveedoresElectronicos_QNAME, SICFERespuestaObtenerProveedoresElectronicos.class,
                null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaCertificados }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaCertificados")
    public JAXBElement<SICFERespuestaCertificados> createSICFERespuestaCertificados(SICFERespuestaCertificados value) {
        return new JAXBElement<SICFERespuestaCertificados>(_SICFERespuestaCertificados_QNAME,
                SICFERespuestaCertificados.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfDataNumerador }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfDataNumerador")
    public JAXBElement<ArrayOfDataNumerador> createArrayOfDataNumerador(ArrayOfDataNumerador value) {
        return new JAXBElement<ArrayOfDataNumerador>(_ArrayOfDataNumerador_QNAME, ArrayOfDataNumerador.class, null,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfDatoTemplateImpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfDatoTemplateImpresion")
    public JAXBElement<ArrayOfDatoTemplateImpresion> createArrayOfDatoTemplateImpresion(
            ArrayOfDatoTemplateImpresion value) {
        return new JAXBElement<ArrayOfDatoTemplateImpresion>(_ArrayOfDatoTemplateImpresion_QNAME,
                ArrayOfDatoTemplateImpresion.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link DatoTemplateImpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "DatoTemplateImpresion")
    public JAXBElement<DatoTemplateImpresion> createDatoTemplateImpresion(DatoTemplateImpresion value) {
        return new JAXBElement<DatoTemplateImpresion>(_DatoTemplateImpresion_QNAME, DatoTemplateImpresion.class, null,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEstadoCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaEstadoCFE")
    public JAXBElement<SICFERespuestaEstadoCFE> createSICFERespuestaEstadoCFE(SICFERespuestaEstadoCFE value) {
        return new JAXBElement<SICFERespuestaEstadoCFE>(_SICFERespuestaEstadoCFE_QNAME, SICFERespuestaEstadoCFE.class,
                null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ResultadoReimpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ResultadoReimpresion")
    public JAXBElement<ResultadoReimpresion> createResultadoReimpresion(ResultadoReimpresion value) {
        return new JAXBElement<ResultadoReimpresion>(_ResultadoReimpresion_QNAME, ResultadoReimpresion.class, null,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link CFERecibidoExtendidoDTO }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "CFERecibidoExtendidoDTO")
    public JAXBElement<CFERecibidoExtendidoDTO> createCFERecibidoExtendidoDTO(CFERecibidoExtendidoDTO value) {
        return new JAXBElement<CFERecibidoExtendidoDTO>(_CFERecibidoExtendidoDTO_QNAME, CFERecibidoExtendidoDTO.class,
                null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfCertificado
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfCertificado")
    public JAXBElement<ArrayOfCertificado> createArrayOfCertificado(ArrayOfCertificado value) {
        return new JAXBElement<ArrayOfCertificado>(_ArrayOfCertificado_QNAME, ArrayOfCertificado.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfCFERecibidoExtendidoDTO }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfCFERecibidoExtendidoDTO")
    public JAXBElement<ArrayOfCFERecibidoExtendidoDTO> createArrayOfCFERecibidoExtendidoDTO(
            ArrayOfCFERecibidoExtendidoDTO value) {
        return new JAXBElement<ArrayOfCFERecibidoExtendidoDTO>(_ArrayOfCFERecibidoExtendidoDTO_QNAME,
                ArrayOfCFERecibidoExtendidoDTO.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DataCfe }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "DataCfe")
    public JAXBElement<DataCfe> createDataCfe(DataCfe value) {
        return new JAXBElement<DataCfe>(_DataCfe_QNAME, DataCfe.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link RespuestaObtenerRecursosImpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RespuestaObtenerRecursosImpresion")
    public JAXBElement<RespuestaObtenerRecursosImpresion> createRespuestaObtenerRecursosImpresion(
            RespuestaObtenerRecursosImpresion value) {
        return new JAXBElement<RespuestaObtenerRecursosImpresion>(_RespuestaObtenerRecursosImpresion_QNAME,
                RespuestaObtenerRecursosImpresion.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link CFERecibidoDTO
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "CFERecibidoDTO")
    public JAXBElement<CFERecibidoDTO> createCFERecibidoDTO(CFERecibidoDTO value) {
        return new JAXBElement<CFERecibidoDTO>(_CFERecibidoDTO_QNAME, CFERecibidoDTO.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Impresora
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Impresora")
    public JAXBElement<Impresora> createImpresora(Impresora value) {
        return new JAXBElement<Impresora>(_Impresora_QNAME, Impresora.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DatoCAE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "DatoCAE")
    public JAXBElement<DatoCAE> createDatoCAE(DatoCAE value) {
        return new JAXBElement<DatoCAE>(_DatoCAE_QNAME, DatoCAE.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEnvioCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaEnvioCFE")
    public JAXBElement<SICFERespuestaEnvioCFE> createSICFERespuestaEnvioCFE(SICFERespuestaEnvioCFE value) {
        return new JAXBElement<SICFERespuestaEnvioCFE>(_SICFERespuestaEnvioCFE_QNAME, SICFERespuestaEnvioCFE.class,
                null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaObtenerCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaObtenerCFE")
    public JAXBElement<SICFERespuestaObtenerCFE> createSICFERespuestaObtenerCFE(SICFERespuestaObtenerCFE value) {
        return new JAXBElement<SICFERespuestaObtenerCFE>(_SICFERespuestaObtenerCFE_QNAME,
                SICFERespuestaObtenerCFE.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IdCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "IdCFE")
    public JAXBElement<IdCFE> createIdCFE(IdCFE value) {
        return new JAXBElement<IdCFE>(_IdCFE_QNAME, IdCFE.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfRechazoAcuseRecibo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfRechazoAcuseRecibo")
    public JAXBElement<ArrayOfRechazoAcuseRecibo> createArrayOfRechazoAcuseRecibo(ArrayOfRechazoAcuseRecibo value) {
        return new JAXBElement<ArrayOfRechazoAcuseRecibo>(_ArrayOfRechazoAcuseRecibo_QNAME,
                ArrayOfRechazoAcuseRecibo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaObtenerClientesElectronicos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaObtenerClientesElectronicos")
    public JAXBElement<SICFERespuestaObtenerClientesElectronicos> createSICFERespuestaObtenerClientesElectronicos(
            SICFERespuestaObtenerClientesElectronicos value) {
        return new JAXBElement<SICFERespuestaObtenerClientesElectronicos>(
                _SICFERespuestaObtenerClientesElectronicos_QNAME, SICFERespuestaObtenerClientesElectronicos.class, null,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link DataReceptorNoElectronico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "DataReceptorNoElectronico")
    public JAXBElement<DataReceptorNoElectronico> createDataReceptorNoElectronico(DataReceptorNoElectronico value) {
        return new JAXBElement<DataReceptorNoElectronico>(_DataReceptorNoElectronico_QNAME,
                DataReceptorNoElectronico.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuestaPing
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaPing")
    public JAXBElement<SICFERespuestaPing> createSICFERespuestaPing(SICFERespuestaPing value) {
        return new JAXBElement<SICFERespuestaPing>(_SICFERespuestaPing_QNAME, SICFERespuestaPing.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfClienteElectronico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfClienteElectronico")
    public JAXBElement<ArrayOfClienteElectronico> createArrayOfClienteElectronico(ArrayOfClienteElectronico value) {
        return new JAXBElement<ArrayOfClienteElectronico>(_ArrayOfClienteElectronico_QNAME,
                ArrayOfClienteElectronico.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaDatosEmisorReceptor }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "SICFERespuestaDatosEmisorReceptor")
    public JAXBElement<SICFERespuestaDatosEmisorReceptor> createSICFERespuestaDatosEmisorReceptor(
            SICFERespuestaDatosEmisorReceptor value) {
        return new JAXBElement<SICFERespuestaDatosEmisorReceptor>(_SICFERespuestaDatosEmisorReceptor_QNAME,
                SICFERespuestaDatosEmisorReceptor.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RechazoAcuseRecibo
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RechazoAcuseRecibo")
    public JAXBElement<RechazoAcuseRecibo> createRechazoAcuseRecibo(RechazoAcuseRecibo value) {
        return new JAXBElement<RechazoAcuseRecibo>(_RechazoAcuseRecibo_QNAME, RechazoAcuseRecibo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfImpresora
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ArrayOfImpresora")
    public JAXBElement<ArrayOfImpresora> createArrayOfImpresora(ArrayOfImpresora value) {
        return new JAXBElement<ArrayOfImpresora>(_ArrayOfImpresora_QNAME, ArrayOfImpresora.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Codigo", scope = RechazoAcuseRecibo.class)
    public JAXBElement<String> createRechazoAcuseReciboCodigo(String value) {
        return new JAXBElement<String>(_RechazoAcuseReciboCodigo_QNAME, String.class, RechazoAcuseRecibo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Descripcion", scope = RechazoAcuseRecibo.class)
    public JAXBElement<String> createRechazoAcuseReciboDescripcion(String value) {
        return new JAXBElement<String>(_RechazoAcuseReciboDescripcion_QNAME, String.class, RechazoAcuseRecibo.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "FechaFin", scope = SICFERespuestaDatosEmisorReceptor.class)
    public JAXBElement<String> createSICFERespuestaDatosEmisorReceptorFechaFin(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorFechaFin_QNAME, String.class,
                SICFERespuestaDatosEmisorReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RUC", scope = SICFERespuestaDatosEmisorReceptor.class)
    public JAXBElement<String> createSICFERespuestaDatosEmisorReceptorRUC(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorRUC_QNAME, String.class,
                SICFERespuestaDatosEmisorReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "FechaInicio", scope = SICFERespuestaDatosEmisorReceptor.class)
    public JAXBElement<String> createSICFERespuestaDatosEmisorReceptorFechaInicio(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorFechaInicio_QNAME, String.class,
                SICFERespuestaDatosEmisorReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "FechaFinTransicion", scope = SICFERespuestaDatosEmisorReceptor.class)
    public JAXBElement<String> createSICFERespuestaDatosEmisorReceptorFechaFinTransicion(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorFechaFinTransicion_QNAME, String.class,
                SICFERespuestaDatosEmisorReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "EmailIntercambio", scope = SICFERespuestaDatosEmisorReceptor.class)
    public JAXBElement<String> createSICFERespuestaDatosEmisorReceptorEmailIntercambio(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorEmailIntercambio_QNAME, String.class,
                SICFERespuestaDatosEmisorReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RazonSocial", scope = SICFERespuestaDatosEmisorReceptor.class)
    public JAXBElement<String> createSICFERespuestaDatosEmisorReceptorRazonSocial(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorRazonSocial_QNAME, String.class,
                SICFERespuestaDatosEmisorReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "CodTemplate", scope = DatoTemplateImpresion.class)
    public JAXBElement<String> createDatoTemplateImpresionCodTemplate(String value) {
        return new JAXBElement<String>(_DatoTemplateImpresionCodTemplate_QNAME, String.class,
                DatoTemplateImpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "EstiloTemplate", scope = DatoTemplateImpresion.class)
    public JAXBElement<Integer> createDatoTemplateImpresionEstiloTemplate(Integer value) {
        return new JAXBElement<Integer>(_DatoTemplateImpresionEstiloTemplate_QNAME, Integer.class,
                DatoTemplateImpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Template", scope = DatoTemplateImpresion.class)
    public JAXBElement<byte[]> createDatoTemplateImpresionTemplate(byte[] value) {
        return new JAXBElement<byte[]>(_DatoTemplateImpresionTemplate_QNAME, byte[].class, DatoTemplateImpresion.class,
                ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "TemplateNombre", scope = DatoTemplateImpresion.class)
    public JAXBElement<String> createDatoTemplateImpresionTemplateNombre(String value) {
        return new JAXBElement<String>(_DatoTemplateImpresionTemplateNombre_QNAME, String.class,
                DatoTemplateImpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IdCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "IdCFE", scope = SICFERespuestaEnvioCFE.class)
    public JAXBElement<IdCFE> createSICFERespuestaEnvioCFEIdCFE(IdCFE value) {
        return new JAXBElement<IdCFE>(_IdCFE_QNAME, IdCFE.class, SICFERespuestaEnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "xml", scope = SICFERespuestaEnvioCFE.class)
    public JAXBElement<String> createSICFERespuestaEnvioCFEXml(String value) {
        return new JAXBElement<String>(_SICFERespuestaEnvioCFEXml_QNAME, String.class, SICFERespuestaEnvioCFE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "LinkQR", scope = SICFERespuestaEnvioCFE.class)
    public JAXBElement<String> createSICFERespuestaEnvioCFELinkQR(String value) {
        return new JAXBElement<String>(_SICFERespuestaEnvioCFELinkQR_QNAME, String.class, SICFERespuestaEnvioCFE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "hash", scope = SICFERespuestaEnvioCFE.class)
    public JAXBElement<String> createSICFERespuestaEnvioCFEHash(String value) {
        return new JAXBElement<String>(_SICFERespuestaEnvioCFEHash_QNAME, String.class, SICFERespuestaEnvioCFE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link DatosCAE
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "datosCAE", scope = SICFERespuestaEnvioCFE.class)
    public JAXBElement<DatosCAE> createSICFERespuestaEnvioCFEDatosCAE(DatosCAE value) {
        return new JAXBElement<DatosCAE>(_DatosCAE_QNAME, DatosCAE.class, SICFERespuestaEnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ImagenQR", scope = SICFERespuestaEnvioCFE.class)
    public JAXBElement<String> createSICFERespuestaEnvioCFEImagenQR(String value) {
        return new JAXBElement<String>(_SICFERespuestaEnvioCFEImagenQR_QNAME, String.class,
                SICFERespuestaEnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "xml", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<String> createSICFERespuestaObtenerCFEXml(String value) {
        return new JAXBElement<String>(_SICFERespuestaEnvioCFEXml_QNAME, String.class, SICFERespuestaObtenerCFE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "docreceptor", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<String> createSICFERespuestaObtenerCFEDocreceptor(String value) {
        return new JAXBElement<String>(_SICFERespuestaObtenerCFEDocreceptor_QNAME, String.class,
                SICFERespuestaObtenerCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "estadoEnvioReceptorNE", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<String> createSICFERespuestaObtenerCFEEstadoEnvioReceptorNE(String value) {
        return new JAXBElement<String>(_SICFERespuestaObtenerCFEEstadoEnvioReceptorNE_QNAME, String.class,
                SICFERespuestaObtenerCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "fechafirma", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<XMLGregorianCalendar> createSICFERespuestaObtenerCFEFechafirma(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_SICFERespuestaObtenerCFEFechafirma_QNAME,
                XMLGregorianCalendar.class, SICFERespuestaObtenerCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "estadoenvioDGI", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<String> createSICFERespuestaObtenerCFEEstadoenvioDGI(String value) {
        return new JAXBElement<String>(_SICFERespuestaObtenerCFEEstadoenvioDGI_QNAME, String.class,
                SICFERespuestaObtenerCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "estadoenvioreceptor", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<String> createSICFERespuestaObtenerCFEEstadoenvioreceptor(String value) {
        return new JAXBElement<String>(_SICFERespuestaObtenerCFEEstadoenvioreceptor_QNAME, String.class,
                SICFERespuestaObtenerCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "fyhrecibido", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<XMLGregorianCalendar> createSICFERespuestaObtenerCFEFyhrecibido(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_SICFERespuestaObtenerCFEFyhrecibido_QNAME,
                XMLGregorianCalendar.class, SICFERespuestaObtenerCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "serie", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<String> createSICFERespuestaObtenerCFESerie(String value) {
        return new JAXBElement<String>(_SICFERespuestaObtenerCFESerie_QNAME, String.class,
                SICFERespuestaObtenerCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "femision", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<XMLGregorianCalendar> createSICFERespuestaObtenerCFEFemision(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_SICFERespuestaObtenerCFEFemision_QNAME,
                XMLGregorianCalendar.class, SICFERespuestaObtenerCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "referenciaerp", scope = SICFERespuestaObtenerCFE.class)
    public JAXBElement<String> createSICFERespuestaObtenerCFEReferenciaerp(String value) {
        return new JAXBElement<String>(_SICFERespuestaObtenerCFEReferenciaerp_QNAME, String.class,
                SICFERespuestaObtenerCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BigDecimal
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "nauto", scope = DatoCAE.class)
    public JAXBElement<BigDecimal> createDatoCAENauto(BigDecimal value) {
        return new JAXBElement<BigDecimal>(_DatoCAENauto_QNAME, BigDecimal.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "fanulacion", scope = DatoCAE.class)
    public JAXBElement<XMLGregorianCalendar> createDatoCAEFanulacion(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_DatoCAEFanulacion_QNAME, XMLGregorianCalendar.class,
                DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ultnro", scope = DatoCAE.class)
    public JAXBElement<Integer> createDatoCAEUltnro(Integer value) {
        return new JAXBElement<Integer>(_DatoCAEUltnro_QNAME, Integer.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "estado", scope = DatoCAE.class)
    public JAXBElement<Integer> createDatoCAEEstado(Integer value) {
        return new JAXBElement<Integer>(_DatoCAEEstado_QNAME, Integer.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "dnro", scope = DatoCAE.class)
    public JAXBElement<Integer> createDatoCAEDnro(Integer value) {
        return new JAXBElement<Integer>(_DatoCAEDnro_QNAME, Integer.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "sucursal", scope = DatoCAE.class)
    public JAXBElement<Integer> createDatoCAESucursal(Integer value) {
        return new JAXBElement<Integer>(_DatoCAESucursal_QNAME, Integer.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "xml", scope = DatoCAE.class)
    public JAXBElement<String> createDatoCAEXml(String value) {
        return new JAXBElement<String>(_SICFERespuestaEnvioCFEXml_QNAME, String.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Tipo", scope = DatoCAE.class)
    public JAXBElement<Integer> createDatoCAETipo(Integer value) {
        return new JAXBElement<Integer>(_DatoCAETipo_QNAME, Integer.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "hnro", scope = DatoCAE.class)
    public JAXBElement<Integer> createDatoCAEHnro(Integer value) {
        return new JAXBElement<Integer>(_DatoCAEHnro_QNAME, Integer.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "tauto", scope = DatoCAE.class)
    public JAXBElement<String> createDatoCAETauto(String value) {
        return new JAXBElement<String>(_DatoCAETauto_QNAME, String.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "IdNumeradorClave", scope = DatoCAE.class)
    public JAXBElement<String> createDatoCAEIdNumeradorClave(String value) {
        return new JAXBElement<String>(_DatoCAEIdNumeradorClave_QNAME, String.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "tenant", scope = DatoCAE.class)
    public JAXBElement<String> createDatoCAETenant(String value) {
        return new JAXBElement<String>(_DatoCAETenant_QNAME, String.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "serie", scope = DatoCAE.class)
    public JAXBElement<String> createDatoCAESerie(String value) {
        return new JAXBElement<String>(_SICFERespuestaObtenerCFESerie_QNAME, String.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "femision", scope = DatoCAE.class)
    public JAXBElement<XMLGregorianCalendar> createDatoCAEFemision(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_SICFERespuestaObtenerCFEFemision_QNAME,
                XMLGregorianCalendar.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "cantusados", scope = DatoCAE.class)
    public JAXBElement<Integer> createDatoCAECantusados(Integer value) {
        return new JAXBElement<Integer>(_DatoCAECantusados_QNAME, Integer.class, DatoCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "fvto", scope = DatoCAE.class)
    public JAXBElement<XMLGregorianCalendar> createDatoCAEFvto(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_DatoCAEFvto_QNAME, XMLGregorianCalendar.class, DatoCAE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "VersionSvc", scope = SICFERespuestaVersion.class)
    public JAXBElement<String> createSICFERespuestaVersionVersionSvc(String value) {
        return new JAXBElement<String>(_SICFERespuestaVersionVersionSvc_QNAME, String.class,
                SICFERespuestaVersion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "VersionBD", scope = SICFERespuestaVersion.class)
    public JAXBElement<String> createSICFERespuestaVersionVersionBD(String value) {
        return new JAXBElement<String>(_SICFERespuestaVersionVersionBD_QNAME, String.class, SICFERespuestaVersion.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BigDecimal
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "nauto", scope = ReservarNro.class)
    public JAXBElement<BigDecimal> createReservarNroNauto(BigDecimal value) {
        return new JAXBElement<BigDecimal>(_DatoCAENauto_QNAME, BigDecimal.class, ReservarNro.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Serie", scope = ReservarNro.class)
    public JAXBElement<String> createReservarNroSerie(String value) {
        return new JAXBElement<String>(_ReservarNroSerie_QNAME, String.class, ReservarNro.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Estado", scope = Certificado.class)
    public JAXBElement<String> createCertificadoEstado(String value) {
        return new JAXBElement<String>(_CertificadoEstado_QNAME, String.class, Certificado.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "EmailIntercambio", scope = SICFERespuestaEmisorReceptorElectronico.class)
    public JAXBElement<String> createSICFERespuestaEmisorReceptorElectronicoEmailIntercambio(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorEmailIntercambio_QNAME, String.class,
                SICFERespuestaEmisorReceptorElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RazonSocial", scope = SICFERespuestaEmisorReceptorElectronico.class)
    public JAXBElement<String> createSICFERespuestaEmisorReceptorElectronicoRazonSocial(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorRazonSocial_QNAME, String.class,
                SICFERespuestaEmisorReceptorElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Buffer", scope = SICFERespuestaBuffer.class)
    public JAXBElement<byte[]> createSICFERespuestaBufferBuffer(byte[] value) {
        return new JAXBElement<byte[]>(_SICFERespuestaBufferBuffer_QNAME, byte[].class, SICFERespuestaBuffer.class,
                ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Ruc", scope = ProveedorElectronico.class)
    public JAXBElement<String> createProveedorElectronicoRuc(String value) {
        return new JAXBElement<String>(_ProveedorElectronicoRuc_QNAME, String.class, ProveedorElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Email", scope = ProveedorElectronico.class)
    public JAXBElement<String> createProveedorElectronicoEmail(String value) {
        return new JAXBElement<String>(_ProveedorElectronicoEmail_QNAME, String.class, ProveedorElectronico.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RazonSocial", scope = ProveedorElectronico.class)
    public JAXBElement<String> createProveedorElectronicoRazonSocial(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorRazonSocial_QNAME, String.class,
                ProveedorElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Nombre", scope = DataReceptorNoElectronico.class)
    public JAXBElement<String> createDataReceptorNoElectronicoNombre(String value) {
        return new JAXBElement<String>(_DataReceptorNoElectronicoNombre_QNAME, String.class,
                DataReceptorNoElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "CodPais", scope = DataReceptorNoElectronico.class)
    public JAXBElement<String> createDataReceptorNoElectronicoCodPais(String value) {
        return new JAXBElement<String>(_DataReceptorNoElectronicoCodPais_QNAME, String.class,
                DataReceptorNoElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Email", scope = DataReceptorNoElectronico.class)
    public JAXBElement<String> createDataReceptorNoElectronicoEmail(String value) {
        return new JAXBElement<String>(_ProveedorElectronicoEmail_QNAME, String.class, DataReceptorNoElectronico.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Doc", scope = DataReceptorNoElectronico.class)
    public JAXBElement<String> createDataReceptorNoElectronicoDoc(String value) {
        return new JAXBElement<String>(_DataReceptorNoElectronicoDoc_QNAME, String.class,
                DataReceptorNoElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Ruc", scope = ClienteElectronico.class)
    public JAXBElement<String> createClienteElectronicoRuc(String value) {
        return new JAXBElement<String>(_ProveedorElectronicoRuc_QNAME, String.class, ClienteElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Email", scope = ClienteElectronico.class)
    public JAXBElement<String> createClienteElectronicoEmail(String value) {
        return new JAXBElement<String>(_ProveedorElectronicoEmail_QNAME, String.class, ClienteElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RazonSocial", scope = ClienteElectronico.class)
    public JAXBElement<String> createClienteElectronicoRazonSocial(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorRazonSocial_QNAME, String.class,
                ClienteElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Descripcion", scope = SICFERespuesta.class)
    public JAXBElement<String> createSICFERespuestaDescripcion(String value) {
        return new JAXBElement<String>(_RechazoAcuseReciboDescripcion_QNAME, String.class, SICFERespuesta.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "FechaEmision", scope = CFERecibidoDTO.class)
    public JAXBElement<String> createCFERecibidoDTOFechaEmision(String value) {
        return new JAXBElement<String>(_CFERecibidoDTOFechaEmision_QNAME, String.class, CFERecibidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "XML", scope = CFERecibidoDTO.class)
    public JAXBElement<String> createCFERecibidoDTOXML(String value) {
        return new JAXBElement<String>(_CFERecibidoDTOXML_QNAME, String.class, CFERecibidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Serie", scope = CFERecibidoDTO.class)
    public JAXBElement<String> createCFERecibidoDTOSerie(String value) {
        return new JAXBElement<String>(_ReservarNroSerie_QNAME, String.class, CFERecibidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Estado", scope = CFERecibidoDTO.class)
    public JAXBElement<String> createCFERecibidoDTOEstado(String value) {
        return new JAXBElement<String>(_CertificadoEstado_QNAME, String.class, CFERecibidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RucEmisor", scope = CFERecibidoDTO.class)
    public JAXBElement<String> createCFERecibidoDTORucEmisor(String value) {
        return new JAXBElement<String>(_CFERecibidoDTORucEmisor_QNAME, String.class, CFERecibidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "EstadoDGI", scope = DataCfe.class)
    public JAXBElement<String> createDataCfeEstadoDGI(String value) {
        return new JAXBElement<String>(_DataCfeEstadoDGI_QNAME, String.class, DataCfe.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Serie", scope = DataCfe.class)
    public JAXBElement<String> createDataCfeSerie(String value) {
        return new JAXBElement<String>(_ReservarNroSerie_QNAME, String.class, DataCfe.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ReferenciaErp", scope = DataCfe.class)
    public JAXBElement<String> createDataCfeReferenciaErp(String value) {
        return new JAXBElement<String>(_DataCfeReferenciaErp_QNAME, String.class, DataCfe.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfDatoCAE
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "CAEList", scope = RespuestaObtenerCAE.class)
    public JAXBElement<ArrayOfDatoCAE> createRespuestaObtenerCAECAEList(ArrayOfDatoCAE value) {
        return new JAXBElement<ArrayOfDatoCAE>(_RespuestaObtenerCAECAEList_QNAME, ArrayOfDatoCAE.class,
                RespuestaObtenerCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfDataNumerador }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ListaNumerador", scope = RespuestaObtenerCAE.class)
    public JAXBElement<ArrayOfDataNumerador> createRespuestaObtenerCAEListaNumerador(ArrayOfDataNumerador value) {
        return new JAXBElement<ArrayOfDataNumerador>(_RespuestaObtenerCAEListaNumerador_QNAME,
                ArrayOfDataNumerador.class, RespuestaObtenerCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfProveedorElectronico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Proveedores", scope = SICFERespuestaObtenerProveedoresElectronicos.class)
    public JAXBElement<ArrayOfProveedorElectronico> createSICFERespuestaObtenerProveedoresElectronicosProveedores(
            ArrayOfProveedorElectronico value) {
        return new JAXBElement<ArrayOfProveedorElectronico>(
                _SICFERespuestaObtenerProveedoresElectronicosProveedores_QNAME, ArrayOfProveedorElectronico.class,
                SICFERespuestaObtenerProveedoresElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Nombre", scope = Impresora.class)
    public JAXBElement<String> createImpresoraNombre(String value) {
        return new JAXBElement<String>(_DataReceptorNoElectronicoNombre_QNAME, String.class, Impresora.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Papel", scope = Impresora.class)
    public JAXBElement<String> createImpresoraPapel(String value) {
        return new JAXBElement<String>(_ImpresoraPapel_QNAME, String.class, Impresora.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IdCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "CFE", scope = ResultadoReimpresion.class)
    public JAXBElement<IdCFE> createResultadoReimpresionCFE(IdCFE value) {
        return new JAXBElement<IdCFE>(_ResultadoReimpresionCFE_QNAME, IdCFE.class, ResultadoReimpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Clave", scope = DataNumerador.class)
    public JAXBElement<String> createDataNumeradorClave(String value) {
        return new JAXBElement<String>(_DataNumeradorClave_QNAME, String.class, DataNumerador.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "NombreInstalacion", scope = DataNumerador.class)
    public JAXBElement<String> createDataNumeradorNombreInstalacion(String value) {
        return new JAXBElement<String>(_DataNumeradorNombreInstalacion_QNAME, String.class, DataNumerador.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Tenant", scope = DataNumerador.class)
    public JAXBElement<String> createDataNumeradorTenant(String value) {
        return new JAXBElement<String>(_DataNumeradorTenant_QNAME, String.class, DataNumerador.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfImpresora
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ListRecursosImpresion", scope = RespuestaObtenerRecursosImpresion.class)
    public JAXBElement<ArrayOfImpresora> createRespuestaObtenerRecursosImpresionListRecursosImpresion(
            ArrayOfImpresora value) {
        return new JAXBElement<ArrayOfImpresora>(_RespuestaObtenerRecursosImpresionListRecursosImpresion_QNAME,
                ArrayOfImpresora.class, RespuestaObtenerRecursosImpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfCertificado
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Certificados", scope = SICFERespuestaCertificados.class)
    public JAXBElement<ArrayOfCertificado> createSICFERespuestaCertificadosCertificados(ArrayOfCertificado value) {
        return new JAXBElement<ArrayOfCertificado>(_SICFERespuestaCertificadosCertificados_QNAME,
                ArrayOfCertificado.class, SICFERespuestaCertificados.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfCFERecibidoDTO }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "CFEsRecibidos", scope = RespuestaObtenerCFEsRecibidos.class)
    public JAXBElement<ArrayOfCFERecibidoDTO> createRespuestaObtenerCFEsRecibidosCFEsRecibidos(
            ArrayOfCFERecibidoDTO value) {
        return new JAXBElement<ArrayOfCFERecibidoDTO>(_RespuestaObtenerCFEsRecibidosCFEsRecibidos_QNAME,
                ArrayOfCFERecibidoDTO.class, RespuestaObtenerCFEsRecibidos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BigDecimal
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "nauto", scope = DatosCAE.class)
    public JAXBElement<BigDecimal> createDatosCAENauto(BigDecimal value) {
        return new JAXBElement<BigDecimal>(_DatoCAENauto_QNAME, BigDecimal.class, DatosCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "hnro", scope = DatosCAE.class)
    public JAXBElement<Integer> createDatosCAEHnro(Integer value) {
        return new JAXBElement<Integer>(_DatoCAEHnro_QNAME, Integer.class, DatosCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "dnro", scope = DatosCAE.class)
    public JAXBElement<Integer> createDatosCAEDnro(Integer value) {
        return new JAXBElement<Integer>(_DatoCAEDnro_QNAME, Integer.class, DatosCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "fvto", scope = DatosCAE.class)
    public JAXBElement<XMLGregorianCalendar> createDatosCAEFvto(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_DatoCAEFvto_QNAME, XMLGregorianCalendar.class, DatosCAE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "xml", scope = SICFERespuestaObtenerXML.class)
    public JAXBElement<String> createSICFERespuestaObtenerXMLXml(String value) {
        return new JAXBElement<String>(_SICFERespuestaEnvioCFEXml_QNAME, String.class, SICFERespuestaObtenerXML.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "CodRechazo", scope = SICFERespuestaEstadoCFE.class)
    public JAXBElement<String> createSICFERespuestaEstadoCFECodRechazo(String value) {
        return new JAXBElement<String>(_SICFERespuestaEstadoCFECodRechazo_QNAME, String.class,
                SICFERespuestaEstadoCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Estado", scope = SICFERespuestaEstadoCFE.class)
    public JAXBElement<String> createSICFERespuestaEstadoCFEEstado(String value) {
        return new JAXBElement<String>(_CertificadoEstado_QNAME, String.class, SICFERespuestaEstadoCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "MotRechazo", scope = SICFERespuestaEstadoCFE.class)
    public JAXBElement<String> createSICFERespuestaEstadoCFEMotRechazo(String value) {
        return new JAXBElement<String>(_SICFERespuestaEstadoCFEMotRechazo_QNAME, String.class,
                SICFERespuestaEstadoCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfResultadoReimpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ResultadosPorCFE", scope = SICFERespuestaReimpresion.class)
    public JAXBElement<ArrayOfResultadoReimpresion> createSICFERespuestaReimpresionResultadosPorCFE(
            ArrayOfResultadoReimpresion value) {
        return new JAXBElement<ArrayOfResultadoReimpresion>(_SICFERespuestaReimpresionResultadosPorCFE_QNAME,
                ArrayOfResultadoReimpresion.class, SICFERespuestaReimpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfDatoTemplateImpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ListTemplatesImpresion", scope = ObtenerTemplateImpresionRespuesta.class)
    public JAXBElement<ArrayOfDatoTemplateImpresion> createObtenerTemplateImpresionRespuestaListTemplatesImpresion(
            ArrayOfDatoTemplateImpresion value) {
        return new JAXBElement<ArrayOfDatoTemplateImpresion>(
                _ObtenerTemplateImpresionRespuestaListTemplatesImpresion_QNAME, ArrayOfDatoTemplateImpresion.class,
                ObtenerTemplateImpresionRespuesta.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfstring
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "ListTemplatesImpresionConClave", scope = ObtenerTemplateImpresionRespuesta.class)
    public JAXBElement<ArrayOfstring> createObtenerTemplateImpresionRespuestaListTemplatesImpresionConClave(
            ArrayOfstring value) {
        return new JAXBElement<ArrayOfstring>(_ObtenerTemplateImpresionRespuestaListTemplatesImpresionConClave_QNAME,
                ArrayOfstring.class, ObtenerTemplateImpresionRespuesta.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "NombreComercial", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTONombreComercial(String value) {
        return new JAXBElement<String>(_CFERecibidoExtendidoDTONombreComercial_QNAME, String.class,
                CFERecibidoExtendidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "FechaEmision", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTOFechaEmision(String value) {
        return new JAXBElement<String>(_CFERecibidoDTOFechaEmision_QNAME, String.class, CFERecibidoExtendidoDTO.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "XML", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTOXML(String value) {
        return new JAXBElement<String>(_CFERecibidoDTOXML_QNAME, String.class, CFERecibidoExtendidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Moneda", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTOMoneda(String value) {
        return new JAXBElement<String>(_CFERecibidoExtendidoDTOMoneda_QNAME, String.class,
                CFERecibidoExtendidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "FechaVencimientoCAE", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTOFechaVencimientoCAE(String value) {
        return new JAXBElement<String>(_CFERecibidoExtendidoDTOFechaVencimientoCAE_QNAME, String.class,
                CFERecibidoExtendidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Serie", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTOSerie(String value) {
        return new JAXBElement<String>(_ReservarNroSerie_QNAME, String.class, CFERecibidoExtendidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Estado", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTOEstado(String value) {
        return new JAXBElement<String>(_CertificadoEstado_QNAME, String.class, CFERecibidoExtendidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RucEmisor", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTORucEmisor(String value) {
        return new JAXBElement<String>(_CFERecibidoDTORucEmisor_QNAME, String.class, CFERecibidoExtendidoDTO.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "RazonSocial", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTORazonSocial(String value) {
        return new JAXBElement<String>(_SICFERespuestaDatosEmisorReceptorRazonSocial_QNAME, String.class,
                CFERecibidoExtendidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "FechaVencimiento", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<String> createCFERecibidoExtendidoDTOFechaVencimiento(String value) {
        return new JAXBElement<String>(_CFERecibidoExtendidoDTOFechaVencimiento_QNAME, String.class,
                CFERecibidoExtendidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Short }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "FormaPago", scope = CFERecibidoExtendidoDTO.class)
    public JAXBElement<Short> createCFERecibidoExtendidoDTOFormaPago(Short value) {
        return new JAXBElement<Short>(_CFERecibidoExtendidoDTOFormaPago_QNAME, Short.class,
                CFERecibidoExtendidoDTO.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfClienteElectronico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Clientes", scope = SICFERespuestaObtenerClientesElectronicos.class)
    public JAXBElement<ArrayOfClienteElectronico> createSICFERespuestaObtenerClientesElectronicosClientes(
            ArrayOfClienteElectronico value) {
        return new JAXBElement<ArrayOfClienteElectronico>(_SICFERespuestaObtenerClientesElectronicosClientes_QNAME,
                ArrayOfClienteElectronico.class, SICFERespuestaObtenerClientesElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfCFERecibidoExtendidoDTO }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "CFEsRecibidos", scope = RespuestaObtenerCFEsRecibidosExtendido.class)
    public JAXBElement<ArrayOfCFERecibidoExtendidoDTO> createRespuestaObtenerCFEsRecibidosExtendidoCFEsRecibidos(
            ArrayOfCFERecibidoExtendidoDTO value) {
        return new JAXBElement<ArrayOfCFERecibidoExtendidoDTO>(_RespuestaObtenerCFEsRecibidosCFEsRecibidos_QNAME,
                ArrayOfCFERecibidoExtendidoDTO.class, RespuestaObtenerCFEsRecibidosExtendido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "rucemisor", scope = IdCFE.class)
    public JAXBElement<String> createIdCFERucemisor(String value) {
        return new JAXBElement<String>(_IdCFERucemisor_QNAME, String.class, IdCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Serie", scope = IdCFE.class)
    public JAXBElement<String> createIdCFESerie(String value) {
        return new JAXBElement<String>(_ReservarNroSerie_QNAME, String.class, IdCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfDataCfe
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", name = "Comprobantes", scope = SicfeRespuestaConsolidar.class)
    public JAXBElement<ArrayOfDataCfe> createSicfeRespuestaConsolidarComprobantes(ArrayOfDataCfe value) {
        return new JAXBElement<ArrayOfDataCfe>(_SicfeRespuestaConsolidarComprobantes_QNAME, ArrayOfDataCfe.class,
                SicfeRespuestaConsolidar.class, value);
    }

}
