
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for Impresora complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="Impresora">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Color" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="Nombre" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Papel" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PorDefecto" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Impresora", propOrder = { "color", "nombre", "papel", "porDefecto" })
public class Impresora {

    @XmlElement(name = "Color")
    protected Boolean color;
    @XmlElementRef(name = "Nombre", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> nombre;
    @XmlElementRef(name = "Papel", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> papel;
    @XmlElement(name = "PorDefecto")
    protected Boolean porDefecto;

    /**
     * Gets the value of the color property.
     * 
     * @return possible object is {@link Boolean }
     * 
     */
    public Boolean isColor() {
        return color;
    }

    /**
     * Sets the value of the color property.
     * 
     * @param value allowed object is {@link Boolean }
     * 
     */
    public void setColor(Boolean value) {
        this.color = value;
    }

    /**
     * Gets the value of the nombre property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getNombre() {
        return nombre;
    }

    /**
     * Sets the value of the nombre property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setNombre(JAXBElement<String> value) {
        this.nombre = value;
    }

    /**
     * Gets the value of the papel property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getPapel() {
        return papel;
    }

    /**
     * Sets the value of the papel property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setPapel(JAXBElement<String> value) {
        this.papel = value;
    }

    /**
     * Gets the value of the porDefecto property.
     * 
     * @return possible object is {@link Boolean }
     * 
     */
    public Boolean isPorDefecto() {
        return porDefecto;
    }

    /**
     * Sets the value of the porDefecto property.
     * 
     * @param value allowed object is {@link Boolean }
     * 
     */
    public void setPorDefecto(Boolean value) {
        this.porDefecto = value;
    }

}
