
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nomusuario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="clave" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tenant" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cliente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cfexml" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="version" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "nomusuario", "clave", "tenant", "cliente", "cfexml", "version" })
@XmlRootElement(name = "ValidarEnvioCFE")
public class ValidarEnvioCFE {

    @XmlElementRef(name = "nomusuario", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> nomusuario;
    @XmlElementRef(name = "clave", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clave;
    @XmlElementRef(name = "tenant", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> tenant;
    @XmlElementRef(name = "cliente", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> cliente;
    @XmlElementRef(name = "cfexml", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> cfexml;
    @XmlElementRef(name = "version", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> version;

    /**
     * Gets the value of the nomusuario property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getNomusuario() {
        return nomusuario;
    }

    /**
     * Sets the value of the nomusuario property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setNomusuario(JAXBElement<String> value) {
        this.nomusuario = value;
    }

    /**
     * Gets the value of the clave property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getClave() {
        return clave;
    }

    /**
     * Sets the value of the clave property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setClave(JAXBElement<String> value) {
        this.clave = value;
    }

    /**
     * Gets the value of the tenant property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getTenant() {
        return tenant;
    }

    /**
     * Sets the value of the tenant property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setTenant(JAXBElement<String> value) {
        this.tenant = value;
    }

    /**
     * Gets the value of the cliente property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getCliente() {
        return cliente;
    }

    /**
     * Sets the value of the cliente property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setCliente(JAXBElement<String> value) {
        this.cliente = value;
    }

    /**
     * Gets the value of the cfexml property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getCfexml() {
        return cfexml;
    }

    /**
     * Sets the value of the cfexml property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setCfexml(JAXBElement<String> value) {
        this.cfexml = value;
    }

    /**
     * Gets the value of the version property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getVersion() {
        return version;
    }

    /**
     * Sets the value of the version property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setVersion(JAXBElement<String> value) {
        this.version = value;
    }

}
