
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.IdCFE;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nomusuario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="clave" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="p_idCFE" type="{http://schemas.datacontract.org/2004/07/SICFEContract}IdCFE" minOccurs="0"/>
 *         &lt;element name="p_tenant" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="template" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "nomusuario", "clave", "pIdCFE", "pTenant", "template" })
@XmlRootElement(name = "ObtenerPDF")
public class ObtenerPDF {

    @XmlElementRef(name = "nomusuario", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> nomusuario;
    @XmlElementRef(name = "clave", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clave;
    @XmlElementRef(name = "p_idCFE", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<IdCFE> pIdCFE;
    @XmlElementRef(name = "p_tenant", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pTenant;
    @XmlElementRef(name = "template", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> template;

    /**
     * Gets the value of the nomusuario property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getNomusuario() {
        return nomusuario;
    }

    /**
     * Sets the value of the nomusuario property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setNomusuario(JAXBElement<String> value) {
        this.nomusuario = value;
    }

    /**
     * Gets the value of the clave property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getClave() {
        return clave;
    }

    /**
     * Sets the value of the clave property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setClave(JAXBElement<String> value) {
        this.clave = value;
    }

    /**
     * Gets the value of the pIdCFE property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link IdCFE
     *         }{@code >}
     * 
     */
    public JAXBElement<IdCFE> getPIdCFE() {
        return pIdCFE;
    }

    /**
     * Sets the value of the pIdCFE property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link IdCFE
     *              }{@code >}
     * 
     */
    public void setPIdCFE(JAXBElement<IdCFE> value) {
        this.pIdCFE = value;
    }

    /**
     * Gets the value of the pTenant property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getPTenant() {
        return pTenant;
    }

    /**
     * Sets the value of the pTenant property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setPTenant(JAXBElement<String> value) {
        this.pTenant = value;
    }

    /**
     * Gets the value of the template property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getTemplate() {
        return template;
    }

    /**
     * Sets the value of the template property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setTemplate(JAXBElement<String> value) {
        this.template = value;
    }

}
