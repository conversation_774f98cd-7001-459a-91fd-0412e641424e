
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.namespace.QName;
import org.datacontract.schemas._2004._07.sicfecontract.ArrayOfRechazoAcuseRecibo;
import org.datacontract.schemas._2004._07.sicfecontract.DataReceptorNoElectronico;
import org.datacontract.schemas._2004._07.sicfecontract.IdCFE;
import org.datacontract.schemas._2004._07.sicfecontract.ObtenerTemplateImpresionRespuesta;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCAE;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCFEsRecibidos;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCFEsRecibidosExtendido;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerRecursosImpresion;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaBuffer;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaCertificados;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaDatosEmisorReceptor;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEmisorReceptorElectronico;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEnvioCFE;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEstadoCFE;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerCFE;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerClientesElectronicos;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerProveedoresElectronicos;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerXML;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaPing;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaReimpresion;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaVersion;
import org.datacontract.schemas._2004._07.sicfecontract.SicfeRespuestaConsolidar;

/**
 * This object contains factory methods for each Java content interface and Java
 * element interface generated in the org.tempuri package.
 * <p>
 * An ObjectFactory allows you to programatically construct new instances of the
 * Java representation for XML content. The Java representation of XML content
 * can consist of schema derived interfaces and classes representing the binding
 * of schema type definitions, element declarations and model groups. Factory
 * methods for each of these are provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _EnvioCFEResponseEnvioCFEResult_QNAME = new QName("http://tempuri.org/",
            "EnvioCFEResult");
    private final static QName _ObtenerCFEsRecibidosExtendidoResponseObtenerCFEsRecibidosExtendidoResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerCFEsRecibidosExtendidoResult");
    private final static QName _ObtenerClientesElectronicosPass_QNAME = new QName("http://tempuri.org/", "pass");
    private final static QName _ObtenerClientesElectronicosTenant_QNAME = new QName("http://tempuri.org/", "tenant");
    private final static QName _ObtenerClientesElectronicosNomusuario_QNAME = new QName("http://tempuri.org/",
            "nomusuario");
    private final static QName _AceptarCFERecibidoResponseAceptarCFERecibidoResult_QNAME = new QName(
            "http://tempuri.org/", "AceptarCFERecibidoResult");
    private final static QName _ObtenerXMLDGIClave_QNAME = new QName("http://tempuri.org/", "clave");
    private final static QName _ObtenerXMLDGIPTenant_QNAME = new QName("http://tempuri.org/", "p_tenant");
    private final static QName _ObtenerXMLDGIPIdCFE_QNAME = new QName("http://tempuri.org/", "p_idCFE");
    private final static QName _EsEmisorReceptorElectronicoResponseEsEmisorReceptorElectronicoResult_QNAME = new QName(
            "http://tempuri.org/", "EsEmisorReceptorElectronicoResult");
    private final static QName _PingSICFEEmisorResponsePingSICFEEmisorResult_QNAME = new QName("http://tempuri.org/",
            "PingSICFEEmisorResult");
    private final static QName _ObtenerXMLDGIResponseObtenerXMLDGIResult_QNAME = new QName("http://tempuri.org/",
            "ObtenerXML_DGIResult");
    private final static QName _ObtenerEstadoCFEResponseObtenerEstadoCFEResult_QNAME = new QName("http://tempuri.org/",
            "ObtenerEstadoCFEResult");
    private final static QName _ConfirmarCFEResponseConfirmarCFEResult_QNAME = new QName("http://tempuri.org/",
            "ConfirmarCFEResult");
    private final static QName _ObtenerCAEParamTenant_QNAME = new QName("http://tempuri.org/", "param_tenant");
    private final static QName _ConfirmarCFERecibidoResponseConfirmarCFERecibidoResult_QNAME = new QName(
            "http://tempuri.org/", "ConfirmarCFERecibidoResult");
    private final static QName _ObtenerPDFSinProcesarResponseObtenerPDFSinProcesarResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerPDFSinProcesarResult");
    private final static QName _ConsolidarComprobantesResponseConsolidarComprobantesResult_QNAME = new QName(
            "http://tempuri.org/", "ConsolidarComprobantesResult");
    private final static QName _RechazarCFERecibidoPMotivosRechazo_QNAME = new QName("http://tempuri.org/",
            "p_motivosRechazo");
    private final static QName _ObtenerProveedoresElectronicosResponseObtenerProveedoresElectronicosResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerProveedoresElectronicosResult");
    private final static QName _ObtenerInfoCertificadosResponseObtenerInfoCertificadosResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerInfoCertificadosResult");
    private final static QName _EnvioCFESinFirmarResponseEnvioCFESinFirmarResult_QNAME = new QName(
            "http://tempuri.org/", "EnvioCFESinFirmarResult");
    private final static QName _EnvioCFEConReceptorImpresora_QNAME = new QName("http://tempuri.org/", "impresora");
    private final static QName _EnvioCFEConReceptorCliente_QNAME = new QName("http://tempuri.org/", "cliente");
    private final static QName _EnvioCFEConReceptorTemplate_QNAME = new QName("http://tempuri.org/", "template");
    private final static QName _EnvioCFEConReceptorCorreo_QNAME = new QName("http://tempuri.org/", "correo");
    private final static QName _EnvioCFEConReceptorReferenciaERP_QNAME = new QName("http://tempuri.org/",
            "referenciaERP");
    private final static QName _EnvioCFEConReceptorCfexml_QNAME = new QName("http://tempuri.org/", "cfexml");
    private final static QName _EnvioCFEConReceptorVersionXSD_QNAME = new QName("http://tempuri.org/", "versionXSD");
    private final static QName _EnvioCFEConReceptorFechaEnvioCorreo_QNAME = new QName("http://tempuri.org/",
            "fechaEnvioCorreo");
    private final static QName _ObtenerClientesElectronicosResponseObtenerClientesElectronicosResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerClientesElectronicosResult");
    private final static QName _ObtenerTemplatesImpresionResponseObtenerTemplatesImpresionResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerTemplatesImpresionResult");
    private final static QName _ObtenerEstadoCFEPClave_QNAME = new QName("http://tempuri.org/", "pClave");
    private final static QName _ObtenerEstadoCFEPIDCFE_QNAME = new QName("http://tempuri.org/", "pIDCFE");
    private final static QName _ObtenerEstadoCFEPTenant_QNAME = new QName("http://tempuri.org/", "pTenant");
    private final static QName _ObtenerEstadoCFEPUsuario_QNAME = new QName("http://tempuri.org/", "pUsuario");
    private final static QName _ObtenerCFEsRecibidosFechaDesde_QNAME = new QName("http://tempuri.org/", "fecha_desde");
    private final static QName _ObtenerCFEsRecibidosEstado_QNAME = new QName("http://tempuri.org/", "estado");
    private final static QName _ObtenerCFEsRecibidosFechaHasta_QNAME = new QName("http://tempuri.org/", "fecha_hasta");
    private final static QName _ObtenerCFEsRecibidosRucEmisor_QNAME = new QName("http://tempuri.org/", "rucEmisor");
    private final static QName _ConfirmarCFEReferenciaERP2_QNAME = new QName("http://tempuri.org/", "referenciaERP2");
    private final static QName _ConfirmarCFEVersion_QNAME = new QName("http://tempuri.org/", "version");
    private final static QName _ObtenerCFEPorIDResponseObtenerCFEPorIDResult_QNAME = new QName("http://tempuri.org/",
            "ObtenerCFEPorIDResult");
    private final static QName _ObtenerRecursosImpresionResponseObtenerRecursosImpresionResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerRecursosImpresionResult");
    private final static QName _EnvioCFEConReceptorResponseEnvioCFEConReceptorResult_QNAME = new QName(
            "http://tempuri.org/", "EnvioCFEConReceptorResult");
    private final static QName _ObtenerVersionResponseObtenerVersionResult_QNAME = new QName("http://tempuri.org/",
            "ObtenerVersionResult");
    private final static QName _ReservarNroResponseReservarNroResult_QNAME = new QName("http://tempuri.org/",
            "ReservarNroResult");
    private final static QName _ObtenerPDFSinProcesarXml_QNAME = new QName("http://tempuri.org/", "xml");
    private final static QName _EsEmisorReceptorElectronicoRuc_QNAME = new QName("http://tempuri.org/", "ruc");
    private final static QName _DesconfirmarCFERecibidoResponseDesconfirmarCFERecibidoResult_QNAME = new QName(
            "http://tempuri.org/", "DesconfirmarCFERecibidoResult");
    private final static QName _ObtenerCFEPorIDUsuario_QNAME = new QName("http://tempuri.org/", "usuario");
    private final static QName _ObtenerCFEPorIDSerie_QNAME = new QName("http://tempuri.org/", "serie");
    private final static QName _AnularRangoPSerie_QNAME = new QName("http://tempuri.org/", "p_serie");
    private final static QName _ObtenerCFEPorReferenciaResponseObtenerCFEPorReferenciaResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerCFEPorReferenciaResult");
    private final static QName _ObtenerCFEsRecibidosResponseObtenerCFEsRecibidosResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerCFEsRecibidosResult");
    private final static QName _ActualizarReceptoresNoElectronicosPDatoReceptor_QNAME = new QName("http://tempuri.org/",
            "p_datoReceptor");
    private final static QName _ActualizarReceptoresNoElectronicosPNomusuario_QNAME = new QName("http://tempuri.org/",
            "p_nomusuario");
    private final static QName _ActualizarReceptoresNoElectronicosPPass_QNAME = new QName("http://tempuri.org/",
            "p_pass");
    private final static QName _ValidarEnvioCFEResponseValidarEnvioCFEResult_QNAME = new QName("http://tempuri.org/",
            "ValidarEnvioCFEResult");
    private final static QName _ObtenerPDFResponseObtenerPDFResult_QNAME = new QName("http://tempuri.org/",
            "ObtenerPDFResult");
    private final static QName _ObtenerCAEResponseObtenerCAEResult_QNAME = new QName("http://tempuri.org/",
            "ObtenerCAEResult");
    private final static QName _RechazarCFERecibidoResponseRechazarCFERecibidoResult_QNAME = new QName(
            "http://tempuri.org/", "RechazarCFERecibidoResult");
    private final static QName _ReimprimirResponseReimprimirResult_QNAME = new QName("http://tempuri.org/",
            "ReimprimirResult");
    private final static QName _ConfirmarCFERecibidoRucemisor_QNAME = new QName("http://tempuri.org/", "rucemisor");
    private final static QName _ObtenerDatosDeEmisorReceptorResponseObtenerDatosDeEmisorReceptorResult_QNAME = new QName(
            "http://tempuri.org/", "ObtenerDatosDeEmisorReceptorResult");
    private final static QName _AnularNumeracionResponseAnularNumeracionResult_QNAME = new QName("http://tempuri.org/",
            "AnularNumeracionResult");
    private final static QName _AnularRangoResponseAnularRangoResult_QNAME = new QName("http://tempuri.org/",
            "AnularRangoResult");
    private final static QName _ReimprimirPID_QNAME = new QName("http://tempuri.org/", "p_ID");
    private final static QName _EnvioCFERecurso_QNAME = new QName("http://tempuri.org/", "recurso");
    private final static QName _ActualizarReceptoresNoElectronicosResponseActualizarReceptoresNoElectronicosResult_QNAME = new QName(
            "http://tempuri.org/", "ActualizarReceptoresNoElectronicosResult");
    private final static QName _EnvioCFESinFirmarRefErp_QNAME = new QName("http://tempuri.org/", "refErp");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema
     * derived classes for package: org.tempuri
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ObtenerCFEPorID }
     * 
     */
    public ObtenerCFEPorID createObtenerCFEPorID() {
        return new ObtenerCFEPorID();
    }

    /**
     * Create an instance of {@link AnularRangoResponse }
     * 
     */
    public AnularRangoResponse createAnularRangoResponse() {
        return new AnularRangoResponse();
    }

    /**
     * Create an instance of {@link ObtenerDatosDeEmisorReceptorResponse }
     * 
     */
    public ObtenerDatosDeEmisorReceptorResponse createObtenerDatosDeEmisorReceptorResponse() {
        return new ObtenerDatosDeEmisorReceptorResponse();
    }

    /**
     * Create an instance of {@link ObtenerXMLDGI }
     * 
     */
    public ObtenerXMLDGI createObtenerXMLDGI() {
        return new ObtenerXMLDGI();
    }

    /**
     * Create an instance of {@link RechazarCFERecibido }
     * 
     */
    public RechazarCFERecibido createRechazarCFERecibido() {
        return new RechazarCFERecibido();
    }

    /**
     * Create an instance of {@link AceptarCFERecibidoResponse }
     * 
     */
    public AceptarCFERecibidoResponse createAceptarCFERecibidoResponse() {
        return new AceptarCFERecibidoResponse();
    }

    /**
     * Create an instance of {@link AnularNumeracion }
     * 
     */
    public AnularNumeracion createAnularNumeracion() {
        return new AnularNumeracion();
    }

    /**
     * Create an instance of {@link ObtenerCFEsRecibidosExtendido }
     * 
     */
    public ObtenerCFEsRecibidosExtendido createObtenerCFEsRecibidosExtendido() {
        return new ObtenerCFEsRecibidosExtendido();
    }

    /**
     * Create an instance of {@link ObtenerCFEPorReferenciaResponse }
     * 
     */
    public ObtenerCFEPorReferenciaResponse createObtenerCFEPorReferenciaResponse() {
        return new ObtenerCFEPorReferenciaResponse();
    }

    /**
     * Create an instance of {@link RechazarCFERecibidoResponse }
     * 
     */
    public RechazarCFERecibidoResponse createRechazarCFERecibidoResponse() {
        return new RechazarCFERecibidoResponse();
    }

    /**
     * Create an instance of {@link ActualizarReceptoresNoElectronicos }
     * 
     */
    public ActualizarReceptoresNoElectronicos createActualizarReceptoresNoElectronicos() {
        return new ActualizarReceptoresNoElectronicos();
    }

    /**
     * Create an instance of {@link ObtenerTemplatesImpresionResponse }
     * 
     */
    public ObtenerTemplatesImpresionResponse createObtenerTemplatesImpresionResponse() {
        return new ObtenerTemplatesImpresionResponse();
    }

    /**
     * Create an instance of {@link EnvioCFEResponse }
     * 
     */
    public EnvioCFEResponse createEnvioCFEResponse() {
        return new EnvioCFEResponse();
    }

    /**
     * Create an instance of {@link ActualizarReceptoresNoElectronicosResponse }
     * 
     */
    public ActualizarReceptoresNoElectronicosResponse createActualizarReceptoresNoElectronicosResponse() {
        return new ActualizarReceptoresNoElectronicosResponse();
    }

    /**
     * Create an instance of {@link ReservarNroResponse }
     * 
     */
    public ReservarNroResponse createReservarNroResponse() {
        return new ReservarNroResponse();
    }

    /**
     * Create an instance of {@link ObtenerDatosDeEmisorReceptor }
     * 
     */
    public ObtenerDatosDeEmisorReceptor createObtenerDatosDeEmisorReceptor() {
        return new ObtenerDatosDeEmisorReceptor();
    }

    /**
     * Create an instance of {@link PingSICFEEmisorResponse }
     * 
     */
    public PingSICFEEmisorResponse createPingSICFEEmisorResponse() {
        return new PingSICFEEmisorResponse();
    }

    /**
     * Create an instance of {@link ObtenerCFEsRecibidosExtendidoResponse }
     * 
     */
    public ObtenerCFEsRecibidosExtendidoResponse createObtenerCFEsRecibidosExtendidoResponse() {
        return new ObtenerCFEsRecibidosExtendidoResponse();
    }

    /**
     * Create an instance of {@link ConsolidarComprobantes }
     * 
     */
    public ConsolidarComprobantes createConsolidarComprobantes() {
        return new ConsolidarComprobantes();
    }

    /**
     * Create an instance of {@link ObtenerCFEsRecibidosResponse }
     * 
     */
    public ObtenerCFEsRecibidosResponse createObtenerCFEsRecibidosResponse() {
        return new ObtenerCFEsRecibidosResponse();
    }

    /**
     * Create an instance of {@link ObtenerClientesElectronicos }
     * 
     */
    public ObtenerClientesElectronicos createObtenerClientesElectronicos() {
        return new ObtenerClientesElectronicos();
    }

    /**
     * Create an instance of {@link AnularRango }
     * 
     */
    public AnularRango createAnularRango() {
        return new AnularRango();
    }

    /**
     * Create an instance of {@link EnvioCFESinFirmarResponse }
     * 
     */
    public EnvioCFESinFirmarResponse createEnvioCFESinFirmarResponse() {
        return new EnvioCFESinFirmarResponse();
    }

    /**
     * Create an instance of {@link ObtenerVersion }
     * 
     */
    public ObtenerVersion createObtenerVersion() {
        return new ObtenerVersion();
    }

    /**
     * Create an instance of {@link ObtenerInfoCertificados }
     * 
     */
    public ObtenerInfoCertificados createObtenerInfoCertificados() {
        return new ObtenerInfoCertificados();
    }

    /**
     * Create an instance of {@link ObtenerProveedoresElectronicos }
     * 
     */
    public ObtenerProveedoresElectronicos createObtenerProveedoresElectronicos() {
        return new ObtenerProveedoresElectronicos();
    }

    /**
     * Create an instance of {@link Reimprimir }
     * 
     */
    public Reimprimir createReimprimir() {
        return new Reimprimir();
    }

    /**
     * Create an instance of {@link ObtenerCFEsRecibidos }
     * 
     */
    public ObtenerCFEsRecibidos createObtenerCFEsRecibidos() {
        return new ObtenerCFEsRecibidos();
    }

    /**
     * Create an instance of {@link ConfirmarCFE }
     * 
     */
    public ConfirmarCFE createConfirmarCFE() {
        return new ConfirmarCFE();
    }

    /**
     * Create an instance of {@link ObtenerPDFSinProcesarResponse }
     * 
     */
    public ObtenerPDFSinProcesarResponse createObtenerPDFSinProcesarResponse() {
        return new ObtenerPDFSinProcesarResponse();
    }

    /**
     * Create an instance of {@link ConsolidarComprobantesResponse }
     * 
     */
    public ConsolidarComprobantesResponse createConsolidarComprobantesResponse() {
        return new ConsolidarComprobantesResponse();
    }

    /**
     * Create an instance of {@link ObtenerVersionResponse }
     * 
     */
    public ObtenerVersionResponse createObtenerVersionResponse() {
        return new ObtenerVersionResponse();
    }

    /**
     * Create an instance of {@link DesconfirmarCFERecibido }
     * 
     */
    public DesconfirmarCFERecibido createDesconfirmarCFERecibido() {
        return new DesconfirmarCFERecibido();
    }

    /**
     * Create an instance of {@link ConfirmarCFERecibidoResponse }
     * 
     */
    public ConfirmarCFERecibidoResponse createConfirmarCFERecibidoResponse() {
        return new ConfirmarCFERecibidoResponse();
    }

    /**
     * Create an instance of {@link ObtenerPDFSinProcesar }
     * 
     */
    public ObtenerPDFSinProcesar createObtenerPDFSinProcesar() {
        return new ObtenerPDFSinProcesar();
    }

    /**
     * Create an instance of {@link ValidarEnvioCFE }
     * 
     */
    public ValidarEnvioCFE createValidarEnvioCFE() {
        return new ValidarEnvioCFE();
    }

    /**
     * Create an instance of {@link ObtenerPDF }
     * 
     */
    public ObtenerPDF createObtenerPDF() {
        return new ObtenerPDF();
    }

    /**
     * Create an instance of {@link ObtenerEstadoCFEResponse }
     * 
     */
    public ObtenerEstadoCFEResponse createObtenerEstadoCFEResponse() {
        return new ObtenerEstadoCFEResponse();
    }

    /**
     * Create an instance of {@link ObtenerCFEPorReferencia }
     * 
     */
    public ObtenerCFEPorReferencia createObtenerCFEPorReferencia() {
        return new ObtenerCFEPorReferencia();
    }

    /**
     * Create an instance of {@link EnvioCFEConReceptor }
     * 
     */
    public EnvioCFEConReceptor createEnvioCFEConReceptor() {
        return new EnvioCFEConReceptor();
    }

    /**
     * Create an instance of {@link ObtenerPDFResponse }
     * 
     */
    public ObtenerPDFResponse createObtenerPDFResponse() {
        return new ObtenerPDFResponse();
    }

    /**
     * Create an instance of {@link org.tempuri.ReservarNro }
     * 
     */
    public org.tempuri.ReservarNro createReservarNro() {
        return new org.tempuri.ReservarNro();
    }

    /**
     * Create an instance of {@link ConfirmarCFERecibido }
     * 
     */
    public ConfirmarCFERecibido createConfirmarCFERecibido() {
        return new ConfirmarCFERecibido();
    }

    /**
     * Create an instance of {@link EsEmisorReceptorElectronicoResponse }
     * 
     */
    public EsEmisorReceptorElectronicoResponse createEsEmisorReceptorElectronicoResponse() {
        return new EsEmisorReceptorElectronicoResponse();
    }

    /**
     * Create an instance of {@link ObtenerTemplatesImpresion }
     * 
     */
    public ObtenerTemplatesImpresion createObtenerTemplatesImpresion() {
        return new ObtenerTemplatesImpresion();
    }

    /**
     * Create an instance of {@link ObtenerXMLDGIResponse }
     * 
     */
    public ObtenerXMLDGIResponse createObtenerXMLDGIResponse() {
        return new ObtenerXMLDGIResponse();
    }

    /**
     * Create an instance of {@link EnvioCFE }
     * 
     */
    public EnvioCFE createEnvioCFE() {
        return new EnvioCFE();
    }

    /**
     * Create an instance of {@link ObtenerRecursosImpresion }
     * 
     */
    public ObtenerRecursosImpresion createObtenerRecursosImpresion() {
        return new ObtenerRecursosImpresion();
    }

    /**
     * Create an instance of {@link ValidarEnvioCFEResponse }
     * 
     */
    public ValidarEnvioCFEResponse createValidarEnvioCFEResponse() {
        return new ValidarEnvioCFEResponse();
    }

    /**
     * Create an instance of {@link ReimprimirResponse }
     * 
     */
    public ReimprimirResponse createReimprimirResponse() {
        return new ReimprimirResponse();
    }

    /**
     * Create an instance of {@link AnularNumeracionResponse }
     * 
     */
    public AnularNumeracionResponse createAnularNumeracionResponse() {
        return new AnularNumeracionResponse();
    }

    /**
     * Create an instance of {@link ObtenerCFEPorIDResponse }
     * 
     */
    public ObtenerCFEPorIDResponse createObtenerCFEPorIDResponse() {
        return new ObtenerCFEPorIDResponse();
    }

    /**
     * Create an instance of {@link ObtenerCAE }
     * 
     */
    public ObtenerCAE createObtenerCAE() {
        return new ObtenerCAE();
    }

    /**
     * Create an instance of {@link ObtenerCAEResponse }
     * 
     */
    public ObtenerCAEResponse createObtenerCAEResponse() {
        return new ObtenerCAEResponse();
    }

    /**
     * Create an instance of {@link EnvioCFESinFirmar }
     * 
     */
    public EnvioCFESinFirmar createEnvioCFESinFirmar() {
        return new EnvioCFESinFirmar();
    }

    /**
     * Create an instance of {@link ObtenerProveedoresElectronicosResponse }
     * 
     */
    public ObtenerProveedoresElectronicosResponse createObtenerProveedoresElectronicosResponse() {
        return new ObtenerProveedoresElectronicosResponse();
    }

    /**
     * Create an instance of {@link ObtenerInfoCertificadosResponse }
     * 
     */
    public ObtenerInfoCertificadosResponse createObtenerInfoCertificadosResponse() {
        return new ObtenerInfoCertificadosResponse();
    }

    /**
     * Create an instance of {@link ObtenerRecursosImpresionResponse }
     * 
     */
    public ObtenerRecursosImpresionResponse createObtenerRecursosImpresionResponse() {
        return new ObtenerRecursosImpresionResponse();
    }

    /**
     * Create an instance of {@link EsEmisorReceptorElectronico }
     * 
     */
    public EsEmisorReceptorElectronico createEsEmisorReceptorElectronico() {
        return new EsEmisorReceptorElectronico();
    }

    /**
     * Create an instance of {@link PingSICFEEmisor }
     * 
     */
    public PingSICFEEmisor createPingSICFEEmisor() {
        return new PingSICFEEmisor();
    }

    /**
     * Create an instance of {@link ObtenerClientesElectronicosResponse }
     * 
     */
    public ObtenerClientesElectronicosResponse createObtenerClientesElectronicosResponse() {
        return new ObtenerClientesElectronicosResponse();
    }

    /**
     * Create an instance of {@link AceptarCFERecibido }
     * 
     */
    public AceptarCFERecibido createAceptarCFERecibido() {
        return new AceptarCFERecibido();
    }

    /**
     * Create an instance of {@link ConfirmarCFEResponse }
     * 
     */
    public ConfirmarCFEResponse createConfirmarCFEResponse() {
        return new ConfirmarCFEResponse();
    }

    /**
     * Create an instance of {@link EnvioCFEConReceptorResponse }
     * 
     */
    public EnvioCFEConReceptorResponse createEnvioCFEConReceptorResponse() {
        return new EnvioCFEConReceptorResponse();
    }

    /**
     * Create an instance of {@link ObtenerEstadoCFE }
     * 
     */
    public ObtenerEstadoCFE createObtenerEstadoCFE() {
        return new ObtenerEstadoCFE();
    }

    /**
     * Create an instance of {@link DesconfirmarCFERecibidoResponse }
     * 
     */
    public DesconfirmarCFERecibidoResponse createDesconfirmarCFERecibidoResponse() {
        return new DesconfirmarCFERecibidoResponse();
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEnvioCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "EnvioCFEResult", scope = EnvioCFEResponse.class)
    public JAXBElement<SICFERespuestaEnvioCFE> createEnvioCFEResponseEnvioCFEResult(SICFERespuestaEnvioCFE value) {
        return new JAXBElement<SICFERespuestaEnvioCFE>(_EnvioCFEResponseEnvioCFEResult_QNAME,
                SICFERespuestaEnvioCFE.class, EnvioCFEResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link RespuestaObtenerCFEsRecibidosExtendido }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerCFEsRecibidosExtendidoResult", scope = ObtenerCFEsRecibidosExtendidoResponse.class)
    public JAXBElement<RespuestaObtenerCFEsRecibidosExtendido> createObtenerCFEsRecibidosExtendidoResponseObtenerCFEsRecibidosExtendidoResult(
            RespuestaObtenerCFEsRecibidosExtendido value) {
        return new JAXBElement<RespuestaObtenerCFEsRecibidosExtendido>(
                _ObtenerCFEsRecibidosExtendidoResponseObtenerCFEsRecibidosExtendidoResult_QNAME,
                RespuestaObtenerCFEsRecibidosExtendido.class, ObtenerCFEsRecibidosExtendidoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pass", scope = ObtenerClientesElectronicos.class)
    public JAXBElement<String> createObtenerClientesElectronicosPass(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosPass_QNAME, String.class,
                ObtenerClientesElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = ObtenerClientesElectronicos.class)
    public JAXBElement<String> createObtenerClientesElectronicosTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class,
                ObtenerClientesElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerClientesElectronicos.class)
    public JAXBElement<String> createObtenerClientesElectronicosNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ObtenerClientesElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuesta
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "AceptarCFERecibidoResult", scope = AceptarCFERecibidoResponse.class)
    public JAXBElement<SICFERespuesta> createAceptarCFERecibidoResponseAceptarCFERecibidoResult(SICFERespuesta value) {
        return new JAXBElement<SICFERespuesta>(_AceptarCFERecibidoResponseAceptarCFERecibidoResult_QNAME,
                SICFERespuesta.class, AceptarCFERecibidoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ObtenerXMLDGI.class)
    public JAXBElement<String> createObtenerXMLDGIClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ObtenerXMLDGI.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_tenant", scope = ObtenerXMLDGI.class)
    public JAXBElement<String> createObtenerXMLDGIPTenant(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIPTenant_QNAME, String.class, ObtenerXMLDGI.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IdCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_idCFE", scope = ObtenerXMLDGI.class)
    public JAXBElement<IdCFE> createObtenerXMLDGIPIdCFE(IdCFE value) {
        return new JAXBElement<IdCFE>(_ObtenerXMLDGIPIdCFE_QNAME, IdCFE.class, ObtenerXMLDGI.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerXMLDGI.class)
    public JAXBElement<String> createObtenerXMLDGINomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class, ObtenerXMLDGI.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEmisorReceptorElectronico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "EsEmisorReceptorElectronicoResult", scope = EsEmisorReceptorElectronicoResponse.class)
    public JAXBElement<SICFERespuestaEmisorReceptorElectronico> createEsEmisorReceptorElectronicoResponseEsEmisorReceptorElectronicoResult(
            SICFERespuestaEmisorReceptorElectronico value) {
        return new JAXBElement<SICFERespuestaEmisorReceptorElectronico>(
                _EsEmisorReceptorElectronicoResponseEsEmisorReceptorElectronicoResult_QNAME,
                SICFERespuestaEmisorReceptorElectronico.class, EsEmisorReceptorElectronicoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuestaPing
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "PingSICFEEmisorResult", scope = PingSICFEEmisorResponse.class)
    public JAXBElement<SICFERespuestaPing> createPingSICFEEmisorResponsePingSICFEEmisorResult(
            SICFERespuestaPing value) {
        return new JAXBElement<SICFERespuestaPing>(_PingSICFEEmisorResponsePingSICFEEmisorResult_QNAME,
                SICFERespuestaPing.class, PingSICFEEmisorResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ObtenerRecursosImpresion.class)
    public JAXBElement<String> createObtenerRecursosImpresionClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ObtenerRecursosImpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = ObtenerRecursosImpresion.class)
    public JAXBElement<String> createObtenerRecursosImpresionTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class,
                ObtenerRecursosImpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerRecursosImpresion.class)
    public JAXBElement<String> createObtenerRecursosImpresionNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ObtenerRecursosImpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaObtenerXML }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerXML_DGIResult", scope = ObtenerXMLDGIResponse.class)
    public JAXBElement<SICFERespuestaObtenerXML> createObtenerXMLDGIResponseObtenerXMLDGIResult(
            SICFERespuestaObtenerXML value) {
        return new JAXBElement<SICFERespuestaObtenerXML>(_ObtenerXMLDGIResponseObtenerXMLDGIResult_QNAME,
                SICFERespuestaObtenerXML.class, ObtenerXMLDGIResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEstadoCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerEstadoCFEResult", scope = ObtenerEstadoCFEResponse.class)
    public JAXBElement<SICFERespuestaEstadoCFE> createObtenerEstadoCFEResponseObtenerEstadoCFEResult(
            SICFERespuestaEstadoCFE value) {
        return new JAXBElement<SICFERespuestaEstadoCFE>(_ObtenerEstadoCFEResponseObtenerEstadoCFEResult_QNAME,
                SICFERespuestaEstadoCFE.class, ObtenerEstadoCFEResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEnvioCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ConfirmarCFEResult", scope = ConfirmarCFEResponse.class)
    public JAXBElement<SICFERespuestaEnvioCFE> createConfirmarCFEResponseConfirmarCFEResult(
            SICFERespuestaEnvioCFE value) {
        return new JAXBElement<SICFERespuestaEnvioCFE>(_ConfirmarCFEResponseConfirmarCFEResult_QNAME,
                SICFERespuestaEnvioCFE.class, ConfirmarCFEResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pass", scope = ObtenerCAE.class)
    public JAXBElement<String> createObtenerCAEPass(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosPass_QNAME, String.class, ObtenerCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = ObtenerCAE.class)
    public JAXBElement<String> createObtenerCAEParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, ObtenerCAE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerCAE.class)
    public JAXBElement<String> createObtenerCAENomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class, ObtenerCAE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = ObtenerInfoCertificados.class)
    public JAXBElement<String> createObtenerInfoCertificadosParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, ObtenerInfoCertificados.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuesta
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ConfirmarCFERecibidoResult", scope = ConfirmarCFERecibidoResponse.class)
    public JAXBElement<SICFERespuesta> createConfirmarCFERecibidoResponseConfirmarCFERecibidoResult(
            SICFERespuesta value) {
        return new JAXBElement<SICFERespuesta>(_ConfirmarCFERecibidoResponseConfirmarCFERecibidoResult_QNAME,
                SICFERespuesta.class, ConfirmarCFERecibidoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaBuffer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerPDFSinProcesarResult", scope = ObtenerPDFSinProcesarResponse.class)
    public JAXBElement<SICFERespuestaBuffer> createObtenerPDFSinProcesarResponseObtenerPDFSinProcesarResult(
            SICFERespuestaBuffer value) {
        return new JAXBElement<SICFERespuestaBuffer>(_ObtenerPDFSinProcesarResponseObtenerPDFSinProcesarResult_QNAME,
                SICFERespuestaBuffer.class, ObtenerPDFSinProcesarResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SicfeRespuestaConsolidar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ConsolidarComprobantesResult", scope = ConsolidarComprobantesResponse.class)
    public JAXBElement<SicfeRespuestaConsolidar> createConsolidarComprobantesResponseConsolidarComprobantesResult(
            SicfeRespuestaConsolidar value) {
        return new JAXBElement<SicfeRespuestaConsolidar>(
                _ConsolidarComprobantesResponseConsolidarComprobantesResult_QNAME, SicfeRespuestaConsolidar.class,
                ConsolidarComprobantesResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = RechazarCFERecibido.class)
    public JAXBElement<String> createRechazarCFERecibidoClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, RechazarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IdCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_idCFE", scope = RechazarCFERecibido.class)
    public JAXBElement<IdCFE> createRechazarCFERecibidoPIdCFE(IdCFE value) {
        return new JAXBElement<IdCFE>(_ObtenerXMLDGIPIdCFE_QNAME, IdCFE.class, RechazarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = RechazarCFERecibido.class)
    public JAXBElement<String> createRechazarCFERecibidoParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, RechazarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ArrayOfRechazoAcuseRecibo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_motivosRechazo", scope = RechazarCFERecibido.class)
    public JAXBElement<ArrayOfRechazoAcuseRecibo> createRechazarCFERecibidoPMotivosRechazo(
            ArrayOfRechazoAcuseRecibo value) {
        return new JAXBElement<ArrayOfRechazoAcuseRecibo>(_RechazarCFERecibidoPMotivosRechazo_QNAME,
                ArrayOfRechazoAcuseRecibo.class, RechazarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = RechazarCFERecibido.class)
    public JAXBElement<String> createRechazarCFERecibidoNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                RechazarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pass", scope = ObtenerProveedoresElectronicos.class)
    public JAXBElement<String> createObtenerProveedoresElectronicosPass(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosPass_QNAME, String.class,
                ObtenerProveedoresElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = ObtenerProveedoresElectronicos.class)
    public JAXBElement<String> createObtenerProveedoresElectronicosTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class,
                ObtenerProveedoresElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerProveedoresElectronicos.class)
    public JAXBElement<String> createObtenerProveedoresElectronicosNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ObtenerProveedoresElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaObtenerProveedoresElectronicos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerProveedoresElectronicosResult", scope = ObtenerProveedoresElectronicosResponse.class)
    public JAXBElement<SICFERespuestaObtenerProveedoresElectronicos> createObtenerProveedoresElectronicosResponseObtenerProveedoresElectronicosResult(
            SICFERespuestaObtenerProveedoresElectronicos value) {
        return new JAXBElement<SICFERespuestaObtenerProveedoresElectronicos>(
                _ObtenerProveedoresElectronicosResponseObtenerProveedoresElectronicosResult_QNAME,
                SICFERespuestaObtenerProveedoresElectronicos.class, ObtenerProveedoresElectronicosResponse.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaCertificados }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerInfoCertificadosResult", scope = ObtenerInfoCertificadosResponse.class)
    public JAXBElement<SICFERespuestaCertificados> createObtenerInfoCertificadosResponseObtenerInfoCertificadosResult(
            SICFERespuestaCertificados value) {
        return new JAXBElement<SICFERespuestaCertificados>(
                _ObtenerInfoCertificadosResponseObtenerInfoCertificadosResult_QNAME, SICFERespuestaCertificados.class,
                ObtenerInfoCertificadosResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEnvioCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "EnvioCFESinFirmarResult", scope = EnvioCFESinFirmarResponse.class)
    public JAXBElement<SICFERespuestaEnvioCFE> createEnvioCFESinFirmarResponseEnvioCFESinFirmarResult(
            SICFERespuestaEnvioCFE value) {
        return new JAXBElement<SICFERespuestaEnvioCFE>(_EnvioCFESinFirmarResponseEnvioCFESinFirmarResult_QNAME,
                SICFERespuestaEnvioCFE.class, EnvioCFESinFirmarResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "impresora", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorImpresora(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorImpresora_QNAME, String.class, EnvioCFEConReceptor.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, EnvioCFEConReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cliente", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorCliente(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCliente_QNAME, String.class, EnvioCFEConReceptor.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "template", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorTemplate(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorTemplate_QNAME, String.class, EnvioCFEConReceptor.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "correo", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorCorreo(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCorreo_QNAME, String.class, EnvioCFEConReceptor.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "referenciaERP", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorReferenciaERP(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorReferenciaERP_QNAME, String.class, EnvioCFEConReceptor.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cfexml", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorCfexml(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCfexml_QNAME, String.class, EnvioCFEConReceptor.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "versionXSD", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorVersionXSD(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorVersionXSD_QNAME, String.class, EnvioCFEConReceptor.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class,
                EnvioCFEConReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "fechaEnvioCorreo", scope = EnvioCFEConReceptor.class)
    public JAXBElement<XMLGregorianCalendar> createEnvioCFEConReceptorFechaEnvioCorreo(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_EnvioCFEConReceptorFechaEnvioCorreo_QNAME,
                XMLGregorianCalendar.class, EnvioCFEConReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = EnvioCFEConReceptor.class)
    public JAXBElement<String> createEnvioCFEConReceptorNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                EnvioCFEConReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = AceptarCFERecibido.class)
    public JAXBElement<String> createAceptarCFERecibidoClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, AceptarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IdCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_idCFE", scope = AceptarCFERecibido.class)
    public JAXBElement<IdCFE> createAceptarCFERecibidoPIdCFE(IdCFE value) {
        return new JAXBElement<IdCFE>(_ObtenerXMLDGIPIdCFE_QNAME, IdCFE.class, AceptarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = AceptarCFERecibido.class)
    public JAXBElement<String> createAceptarCFERecibidoParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, AceptarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = AceptarCFERecibido.class)
    public JAXBElement<String> createAceptarCFERecibidoNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                AceptarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaObtenerClientesElectronicos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerClientesElectronicosResult", scope = ObtenerClientesElectronicosResponse.class)
    public JAXBElement<SICFERespuestaObtenerClientesElectronicos> createObtenerClientesElectronicosResponseObtenerClientesElectronicosResult(
            SICFERespuestaObtenerClientesElectronicos value) {
        return new JAXBElement<SICFERespuestaObtenerClientesElectronicos>(
                _ObtenerClientesElectronicosResponseObtenerClientesElectronicosResult_QNAME,
                SICFERespuestaObtenerClientesElectronicos.class, ObtenerClientesElectronicosResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link ObtenerTemplateImpresionRespuesta }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerTemplatesImpresionResult", scope = ObtenerTemplatesImpresionResponse.class)
    public JAXBElement<ObtenerTemplateImpresionRespuesta> createObtenerTemplatesImpresionResponseObtenerTemplatesImpresionResult(
            ObtenerTemplateImpresionRespuesta value) {
        return new JAXBElement<ObtenerTemplateImpresionRespuesta>(
                _ObtenerTemplatesImpresionResponseObtenerTemplatesImpresionResult_QNAME,
                ObtenerTemplateImpresionRespuesta.class, ObtenerTemplatesImpresionResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pClave", scope = ObtenerEstadoCFE.class)
    public JAXBElement<String> createObtenerEstadoCFEPClave(String value) {
        return new JAXBElement<String>(_ObtenerEstadoCFEPClave_QNAME, String.class, ObtenerEstadoCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IdCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pIDCFE", scope = ObtenerEstadoCFE.class)
    public JAXBElement<IdCFE> createObtenerEstadoCFEPIDCFE(IdCFE value) {
        return new JAXBElement<IdCFE>(_ObtenerEstadoCFEPIDCFE_QNAME, IdCFE.class, ObtenerEstadoCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pTenant", scope = ObtenerEstadoCFE.class)
    public JAXBElement<String> createObtenerEstadoCFEPTenant(String value) {
        return new JAXBElement<String>(_ObtenerEstadoCFEPTenant_QNAME, String.class, ObtenerEstadoCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pUsuario", scope = ObtenerEstadoCFE.class)
    public JAXBElement<String> createObtenerEstadoCFEPUsuario(String value) {
        return new JAXBElement<String>(_ObtenerEstadoCFEPUsuario_QNAME, String.class, ObtenerEstadoCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ObtenerCFEsRecibidos.class)
    public JAXBElement<String> createObtenerCFEsRecibidosClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ObtenerCFEsRecibidos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "fecha_desde", scope = ObtenerCFEsRecibidos.class)
    public JAXBElement<String> createObtenerCFEsRecibidosFechaDesde(String value) {
        return new JAXBElement<String>(_ObtenerCFEsRecibidosFechaDesde_QNAME, String.class, ObtenerCFEsRecibidos.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "estado", scope = ObtenerCFEsRecibidos.class)
    public JAXBElement<String> createObtenerCFEsRecibidosEstado(String value) {
        return new JAXBElement<String>(_ObtenerCFEsRecibidosEstado_QNAME, String.class, ObtenerCFEsRecibidos.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = ObtenerCFEsRecibidos.class)
    public JAXBElement<String> createObtenerCFEsRecibidosParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, ObtenerCFEsRecibidos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "fecha_hasta", scope = ObtenerCFEsRecibidos.class)
    public JAXBElement<String> createObtenerCFEsRecibidosFechaHasta(String value) {
        return new JAXBElement<String>(_ObtenerCFEsRecibidosFechaHasta_QNAME, String.class, ObtenerCFEsRecibidos.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "rucEmisor", scope = ObtenerCFEsRecibidos.class)
    public JAXBElement<String> createObtenerCFEsRecibidosRucEmisor(String value) {
        return new JAXBElement<String>(_ObtenerCFEsRecibidosRucEmisor_QNAME, String.class, ObtenerCFEsRecibidos.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerCFEsRecibidos.class)
    public JAXBElement<String> createObtenerCFEsRecibidosNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ObtenerCFEsRecibidos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pass", scope = ConsolidarComprobantes.class)
    public JAXBElement<String> createConsolidarComprobantesPass(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosPass_QNAME, String.class,
                ConsolidarComprobantes.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = ConsolidarComprobantes.class)
    public JAXBElement<String> createConsolidarComprobantesTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class,
                ConsolidarComprobantes.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ConsolidarComprobantes.class)
    public JAXBElement<String> createConsolidarComprobantesNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ConsolidarComprobantes.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ConfirmarCFE.class)
    public JAXBElement<String> createConfirmarCFEClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ConfirmarCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cliente", scope = ConfirmarCFE.class)
    public JAXBElement<String> createConfirmarCFECliente(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCliente_QNAME, String.class, ConfirmarCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "referenciaERP", scope = ConfirmarCFE.class)
    public JAXBElement<String> createConfirmarCFEReferenciaERP(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorReferenciaERP_QNAME, String.class, ConfirmarCFE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "referenciaERP2", scope = ConfirmarCFE.class)
    public JAXBElement<String> createConfirmarCFEReferenciaERP2(String value) {
        return new JAXBElement<String>(_ConfirmarCFEReferenciaERP2_QNAME, String.class, ConfirmarCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cfexml", scope = ConfirmarCFE.class)
    public JAXBElement<String> createConfirmarCFECfexml(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCfexml_QNAME, String.class, ConfirmarCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = ConfirmarCFE.class)
    public JAXBElement<String> createConfirmarCFETenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class, ConfirmarCFE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ConfirmarCFE.class)
    public JAXBElement<String> createConfirmarCFENomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class, ConfirmarCFE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "version", scope = ConfirmarCFE.class)
    public JAXBElement<String> createConfirmarCFEVersion(String value) {
        return new JAXBElement<String>(_ConfirmarCFEVersion_QNAME, String.class, ConfirmarCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaObtenerCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerCFEPorIDResult", scope = ObtenerCFEPorIDResponse.class)
    public JAXBElement<SICFERespuestaObtenerCFE> createObtenerCFEPorIDResponseObtenerCFEPorIDResult(
            SICFERespuestaObtenerCFE value) {
        return new JAXBElement<SICFERespuestaObtenerCFE>(_ObtenerCFEPorIDResponseObtenerCFEPorIDResult_QNAME,
                SICFERespuestaObtenerCFE.class, ObtenerCFEPorIDResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = org.tempuri.ReservarNro.class)
    public JAXBElement<String> createReservarNroClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, org.tempuri.ReservarNro.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cliente", scope = org.tempuri.ReservarNro.class)
    public JAXBElement<String> createReservarNroCliente(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCliente_QNAME, String.class, org.tempuri.ReservarNro.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cfexml", scope = org.tempuri.ReservarNro.class)
    public JAXBElement<String> createReservarNroCfexml(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCfexml_QNAME, String.class, org.tempuri.ReservarNro.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = org.tempuri.ReservarNro.class)
    public JAXBElement<String> createReservarNroTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class,
                org.tempuri.ReservarNro.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = org.tempuri.ReservarNro.class)
    public JAXBElement<String> createReservarNroNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                org.tempuri.ReservarNro.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link RespuestaObtenerRecursosImpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerRecursosImpresionResult", scope = ObtenerRecursosImpresionResponse.class)
    public JAXBElement<RespuestaObtenerRecursosImpresion> createObtenerRecursosImpresionResponseObtenerRecursosImpresionResult(
            RespuestaObtenerRecursosImpresion value) {
        return new JAXBElement<RespuestaObtenerRecursosImpresion>(
                _ObtenerRecursosImpresionResponseObtenerRecursosImpresionResult_QNAME,
                RespuestaObtenerRecursosImpresion.class, ObtenerRecursosImpresionResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEnvioCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "EnvioCFEConReceptorResult", scope = EnvioCFEConReceptorResponse.class)
    public JAXBElement<SICFERespuestaEnvioCFE> createEnvioCFEConReceptorResponseEnvioCFEConReceptorResult(
            SICFERespuestaEnvioCFE value) {
        return new JAXBElement<SICFERespuestaEnvioCFE>(_EnvioCFEConReceptorResponseEnvioCFEConReceptorResult_QNAME,
                SICFERespuestaEnvioCFE.class, EnvioCFEConReceptorResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ObtenerCFEsRecibidosExtendido.class)
    public JAXBElement<String> createObtenerCFEsRecibidosExtendidoClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ObtenerCFEsRecibidosExtendido.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "fecha_desde", scope = ObtenerCFEsRecibidosExtendido.class)
    public JAXBElement<String> createObtenerCFEsRecibidosExtendidoFechaDesde(String value) {
        return new JAXBElement<String>(_ObtenerCFEsRecibidosFechaDesde_QNAME, String.class,
                ObtenerCFEsRecibidosExtendido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "estado", scope = ObtenerCFEsRecibidosExtendido.class)
    public JAXBElement<String> createObtenerCFEsRecibidosExtendidoEstado(String value) {
        return new JAXBElement<String>(_ObtenerCFEsRecibidosEstado_QNAME, String.class,
                ObtenerCFEsRecibidosExtendido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = ObtenerCFEsRecibidosExtendido.class)
    public JAXBElement<String> createObtenerCFEsRecibidosExtendidoParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, ObtenerCFEsRecibidosExtendido.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "fecha_hasta", scope = ObtenerCFEsRecibidosExtendido.class)
    public JAXBElement<String> createObtenerCFEsRecibidosExtendidoFechaHasta(String value) {
        return new JAXBElement<String>(_ObtenerCFEsRecibidosFechaHasta_QNAME, String.class,
                ObtenerCFEsRecibidosExtendido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "rucEmisor", scope = ObtenerCFEsRecibidosExtendido.class)
    public JAXBElement<String> createObtenerCFEsRecibidosExtendidoRucEmisor(String value) {
        return new JAXBElement<String>(_ObtenerCFEsRecibidosRucEmisor_QNAME, String.class,
                ObtenerCFEsRecibidosExtendido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerCFEsRecibidosExtendido.class)
    public JAXBElement<String> createObtenerCFEsRecibidosExtendidoNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ObtenerCFEsRecibidosExtendido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaVersion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerVersionResult", scope = ObtenerVersionResponse.class)
    public JAXBElement<SICFERespuestaVersion> createObtenerVersionResponseObtenerVersionResult(
            SICFERespuestaVersion value) {
        return new JAXBElement<SICFERespuestaVersion>(_ObtenerVersionResponseObtenerVersionResult_QNAME,
                SICFERespuestaVersion.class, ObtenerVersionResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link org.datacontract.schemas._2004._07.sicfecontract.ReservarNro }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ReservarNroResult", scope = ReservarNroResponse.class)
    public JAXBElement<org.datacontract.schemas._2004._07.sicfecontract.ReservarNro> createReservarNroResponseReservarNroResult(
            org.datacontract.schemas._2004._07.sicfecontract.ReservarNro value) {
        return new JAXBElement<org.datacontract.schemas._2004._07.sicfecontract.ReservarNro>(
                _ReservarNroResponseReservarNroResult_QNAME,
                org.datacontract.schemas._2004._07.sicfecontract.ReservarNro.class, ReservarNroResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ObtenerPDFSinProcesar.class)
    public JAXBElement<String> createObtenerPDFSinProcesarClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ObtenerPDFSinProcesar.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_tenant", scope = ObtenerPDFSinProcesar.class)
    public JAXBElement<String> createObtenerPDFSinProcesarPTenant(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIPTenant_QNAME, String.class, ObtenerPDFSinProcesar.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "template", scope = ObtenerPDFSinProcesar.class)
    public JAXBElement<String> createObtenerPDFSinProcesarTemplate(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorTemplate_QNAME, String.class, ObtenerPDFSinProcesar.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "xml", scope = ObtenerPDFSinProcesar.class)
    public JAXBElement<String> createObtenerPDFSinProcesarXml(String value) {
        return new JAXBElement<String>(_ObtenerPDFSinProcesarXml_QNAME, String.class, ObtenerPDFSinProcesar.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerPDFSinProcesar.class)
    public JAXBElement<String> createObtenerPDFSinProcesarNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ObtenerPDFSinProcesar.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ruc", scope = EsEmisorReceptorElectronico.class)
    public JAXBElement<String> createEsEmisorReceptorElectronicoRuc(String value) {
        return new JAXBElement<String>(_EsEmisorReceptorElectronicoRuc_QNAME, String.class,
                EsEmisorReceptorElectronico.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuesta
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "DesconfirmarCFERecibidoResult", scope = DesconfirmarCFERecibidoResponse.class)
    public JAXBElement<SICFERespuesta> createDesconfirmarCFERecibidoResponseDesconfirmarCFERecibidoResult(
            SICFERespuesta value) {
        return new JAXBElement<SICFERespuesta>(_DesconfirmarCFERecibidoResponseDesconfirmarCFERecibidoResult_QNAME,
                SICFERespuesta.class, DesconfirmarCFERecibidoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ObtenerCFEPorID.class)
    public JAXBElement<String> createObtenerCFEPorIDClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ObtenerCFEPorID.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "usuario", scope = ObtenerCFEPorID.class)
    public JAXBElement<String> createObtenerCFEPorIDUsuario(String value) {
        return new JAXBElement<String>(_ObtenerCFEPorIDUsuario_QNAME, String.class, ObtenerCFEPorID.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "serie", scope = ObtenerCFEPorID.class)
    public JAXBElement<String> createObtenerCFEPorIDSerie(String value) {
        return new JAXBElement<String>(_ObtenerCFEPorIDSerie_QNAME, String.class, ObtenerCFEPorID.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = ObtenerCFEPorID.class)
    public JAXBElement<String> createObtenerCFEPorIDTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class, ObtenerCFEPorID.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_serie", scope = AnularRango.class)
    public JAXBElement<String> createAnularRangoPSerie(String value) {
        return new JAXBElement<String>(_AnularRangoPSerie_QNAME, String.class, AnularRango.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pass", scope = AnularRango.class)
    public JAXBElement<String> createAnularRangoPass(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosPass_QNAME, String.class, AnularRango.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = AnularRango.class)
    public JAXBElement<String> createAnularRangoParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, AnularRango.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = AnularRango.class)
    public JAXBElement<String> createAnularRangoNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class, AnularRango.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaObtenerCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerCFEPorReferenciaResult", scope = ObtenerCFEPorReferenciaResponse.class)
    public JAXBElement<SICFERespuestaObtenerCFE> createObtenerCFEPorReferenciaResponseObtenerCFEPorReferenciaResult(
            SICFERespuestaObtenerCFE value) {
        return new JAXBElement<SICFERespuestaObtenerCFE>(
                _ObtenerCFEPorReferenciaResponseObtenerCFEPorReferenciaResult_QNAME, SICFERespuestaObtenerCFE.class,
                ObtenerCFEPorReferenciaResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ruc", scope = ObtenerDatosDeEmisorReceptor.class)
    public JAXBElement<String> createObtenerDatosDeEmisorReceptorRuc(String value) {
        return new JAXBElement<String>(_EsEmisorReceptorElectronicoRuc_QNAME, String.class,
                ObtenerDatosDeEmisorReceptor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link RespuestaObtenerCFEsRecibidos }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerCFEsRecibidosResult", scope = ObtenerCFEsRecibidosResponse.class)
    public JAXBElement<RespuestaObtenerCFEsRecibidos> createObtenerCFEsRecibidosResponseObtenerCFEsRecibidosResult(
            RespuestaObtenerCFEsRecibidos value) {
        return new JAXBElement<RespuestaObtenerCFEsRecibidos>(
                _ObtenerCFEsRecibidosResponseObtenerCFEsRecibidosResult_QNAME, RespuestaObtenerCFEsRecibidos.class,
                ObtenerCFEsRecibidosResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link DataReceptorNoElectronico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_datoReceptor", scope = ActualizarReceptoresNoElectronicos.class)
    public JAXBElement<DataReceptorNoElectronico> createActualizarReceptoresNoElectronicosPDatoReceptor(
            DataReceptorNoElectronico value) {
        return new JAXBElement<DataReceptorNoElectronico>(_ActualizarReceptoresNoElectronicosPDatoReceptor_QNAME,
                DataReceptorNoElectronico.class, ActualizarReceptoresNoElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_tenant", scope = ActualizarReceptoresNoElectronicos.class)
    public JAXBElement<String> createActualizarReceptoresNoElectronicosPTenant(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIPTenant_QNAME, String.class,
                ActualizarReceptoresNoElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_nomusuario", scope = ActualizarReceptoresNoElectronicos.class)
    public JAXBElement<String> createActualizarReceptoresNoElectronicosPNomusuario(String value) {
        return new JAXBElement<String>(_ActualizarReceptoresNoElectronicosPNomusuario_QNAME, String.class,
                ActualizarReceptoresNoElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_pass", scope = ActualizarReceptoresNoElectronicos.class)
    public JAXBElement<String> createActualizarReceptoresNoElectronicosPPass(String value) {
        return new JAXBElement<String>(_ActualizarReceptoresNoElectronicosPPass_QNAME, String.class,
                ActualizarReceptoresNoElectronicos.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pass", scope = ObtenerTemplatesImpresion.class)
    public JAXBElement<String> createObtenerTemplatesImpresionPass(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosPass_QNAME, String.class,
                ObtenerTemplatesImpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = ObtenerTemplatesImpresion.class)
    public JAXBElement<String> createObtenerTemplatesImpresionParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, ObtenerTemplatesImpresion.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerTemplatesImpresion.class)
    public JAXBElement<String> createObtenerTemplatesImpresionNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ObtenerTemplatesImpresion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaEnvioCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ValidarEnvioCFEResult", scope = ValidarEnvioCFEResponse.class)
    public JAXBElement<SICFERespuestaEnvioCFE> createValidarEnvioCFEResponseValidarEnvioCFEResult(
            SICFERespuestaEnvioCFE value) {
        return new JAXBElement<SICFERespuestaEnvioCFE>(_ValidarEnvioCFEResponseValidarEnvioCFEResult_QNAME,
                SICFERespuestaEnvioCFE.class, ValidarEnvioCFEResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaBuffer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerPDFResult", scope = ObtenerPDFResponse.class)
    public JAXBElement<SICFERespuestaBuffer> createObtenerPDFResponseObtenerPDFResult(SICFERespuestaBuffer value) {
        return new JAXBElement<SICFERespuestaBuffer>(_ObtenerPDFResponseObtenerPDFResult_QNAME,
                SICFERespuestaBuffer.class, ObtenerPDFResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ObtenerPDF.class)
    public JAXBElement<String> createObtenerPDFClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ObtenerPDF.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_tenant", scope = ObtenerPDF.class)
    public JAXBElement<String> createObtenerPDFPTenant(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIPTenant_QNAME, String.class, ObtenerPDF.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "template", scope = ObtenerPDF.class)
    public JAXBElement<String> createObtenerPDFTemplate(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorTemplate_QNAME, String.class, ObtenerPDF.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IdCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_idCFE", scope = ObtenerPDF.class)
    public JAXBElement<IdCFE> createObtenerPDFPIdCFE(IdCFE value) {
        return new JAXBElement<IdCFE>(_ObtenerXMLDGIPIdCFE_QNAME, IdCFE.class, ObtenerPDF.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ObtenerPDF.class)
    public JAXBElement<String> createObtenerPDFNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class, ObtenerPDF.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_serie", scope = AnularNumeracion.class)
    public JAXBElement<String> createAnularNumeracionPSerie(String value) {
        return new JAXBElement<String>(_AnularRangoPSerie_QNAME, String.class, AnularNumeracion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "pass", scope = AnularNumeracion.class)
    public JAXBElement<String> createAnularNumeracionPass(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosPass_QNAME, String.class, AnularNumeracion.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = AnularNumeracion.class)
    public JAXBElement<String> createAnularNumeracionParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, AnularNumeracion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = AnularNumeracion.class)
    public JAXBElement<String> createAnularNumeracionNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                AnularNumeracion.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link RespuestaObtenerCAE
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerCAEResult", scope = ObtenerCAEResponse.class)
    public JAXBElement<RespuestaObtenerCAE> createObtenerCAEResponseObtenerCAEResult(RespuestaObtenerCAE value) {
        return new JAXBElement<RespuestaObtenerCAE>(_ObtenerCAEResponseObtenerCAEResult_QNAME,
                RespuestaObtenerCAE.class, ObtenerCAEResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuesta
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "RechazarCFERecibidoResult", scope = RechazarCFERecibidoResponse.class)
    public JAXBElement<SICFERespuesta> createRechazarCFERecibidoResponseRechazarCFERecibidoResult(
            SICFERespuesta value) {
        return new JAXBElement<SICFERespuesta>(_RechazarCFERecibidoResponseRechazarCFERecibidoResult_QNAME,
                SICFERespuesta.class, RechazarCFERecibidoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = PingSICFEEmisor.class)
    public JAXBElement<String> createPingSICFEEmisorClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, PingSICFEEmisor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = PingSICFEEmisor.class)
    public JAXBElement<String> createPingSICFEEmisorTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class, PingSICFEEmisor.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = PingSICFEEmisor.class)
    public JAXBElement<String> createPingSICFEEmisorNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                PingSICFEEmisor.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaReimpresion }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ReimprimirResult", scope = ReimprimirResponse.class)
    public JAXBElement<SICFERespuestaReimpresion> createReimprimirResponseReimprimirResult(
            SICFERespuestaReimpresion value) {
        return new JAXBElement<SICFERespuestaReimpresion>(_ReimprimirResponseReimprimirResult_QNAME,
                SICFERespuestaReimpresion.class, ReimprimirResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "rucemisor", scope = ConfirmarCFERecibido.class)
    public JAXBElement<String> createConfirmarCFERecibidoRucemisor(String value) {
        return new JAXBElement<String>(_ConfirmarCFERecibidoRucemisor_QNAME, String.class, ConfirmarCFERecibido.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ConfirmarCFERecibido.class)
    public JAXBElement<String> createConfirmarCFERecibidoClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ConfirmarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = ConfirmarCFERecibido.class)
    public JAXBElement<String> createConfirmarCFERecibidoParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, ConfirmarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "serie", scope = ConfirmarCFERecibido.class)
    public JAXBElement<String> createConfirmarCFERecibidoSerie(String value) {
        return new JAXBElement<String>(_ObtenerCFEPorIDSerie_QNAME, String.class, ConfirmarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ConfirmarCFERecibido.class)
    public JAXBElement<String> createConfirmarCFERecibidoNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ConfirmarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement
     * }{@code <}{@link SICFERespuestaDatosEmisorReceptor }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ObtenerDatosDeEmisorReceptorResult", scope = ObtenerDatosDeEmisorReceptorResponse.class)
    public JAXBElement<SICFERespuestaDatosEmisorReceptor> createObtenerDatosDeEmisorReceptorResponseObtenerDatosDeEmisorReceptorResult(
            SICFERespuestaDatosEmisorReceptor value) {
        return new JAXBElement<SICFERespuestaDatosEmisorReceptor>(
                _ObtenerDatosDeEmisorReceptorResponseObtenerDatosDeEmisorReceptorResult_QNAME,
                SICFERespuestaDatosEmisorReceptor.class, ObtenerDatosDeEmisorReceptorResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuesta
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "AnularNumeracionResult", scope = AnularNumeracionResponse.class)
    public JAXBElement<SICFERespuesta> createAnularNumeracionResponseAnularNumeracionResult(SICFERespuesta value) {
        return new JAXBElement<SICFERespuesta>(_AnularNumeracionResponseAnularNumeracionResult_QNAME,
                SICFERespuesta.class, AnularNumeracionResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ObtenerCFEPorReferencia.class)
    public JAXBElement<String> createObtenerCFEPorReferenciaClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ObtenerCFEPorReferencia.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "referenciaERP", scope = ObtenerCFEPorReferencia.class)
    public JAXBElement<String> createObtenerCFEPorReferenciaReferenciaERP(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorReferenciaERP_QNAME, String.class,
                ObtenerCFEPorReferencia.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "usuario", scope = ObtenerCFEPorReferencia.class)
    public JAXBElement<String> createObtenerCFEPorReferenciaUsuario(String value) {
        return new JAXBElement<String>(_ObtenerCFEPorIDUsuario_QNAME, String.class, ObtenerCFEPorReferencia.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = ObtenerCFEPorReferencia.class)
    public JAXBElement<String> createObtenerCFEPorReferenciaTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class,
                ObtenerCFEPorReferencia.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuesta
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "AnularRangoResult", scope = AnularRangoResponse.class)
    public JAXBElement<SICFERespuesta> createAnularRangoResponseAnularRangoResult(SICFERespuesta value) {
        return new JAXBElement<SICFERespuesta>(_AnularRangoResponseAnularRangoResult_QNAME, SICFERespuesta.class,
                AnularRangoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "impresora", scope = Reimprimir.class)
    public JAXBElement<String> createReimprimirImpresora(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorImpresora_QNAME, String.class, Reimprimir.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = Reimprimir.class)
    public JAXBElement<String> createReimprimirClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, Reimprimir.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "template", scope = Reimprimir.class)
    public JAXBElement<String> createReimprimirTemplate(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorTemplate_QNAME, String.class, Reimprimir.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = Reimprimir.class)
    public JAXBElement<String> createReimprimirParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, Reimprimir.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link IdCFE }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "p_ID", scope = Reimprimir.class)
    public JAXBElement<IdCFE> createReimprimirPID(IdCFE value) {
        return new JAXBElement<IdCFE>(_ReimprimirPID_QNAME, IdCFE.class, Reimprimir.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = Reimprimir.class)
    public JAXBElement<String> createReimprimirNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class, Reimprimir.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFEClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, EnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cliente", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFECliente(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCliente_QNAME, String.class, EnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "recurso", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFERecurso(String value) {
        return new JAXBElement<String>(_EnvioCFERecurso_QNAME, String.class, EnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "template", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFETemplate(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorTemplate_QNAME, String.class, EnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "referenciaERP", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFEReferenciaERP(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorReferenciaERP_QNAME, String.class, EnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "referenciaERP2", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFEReferenciaERP2(String value) {
        return new JAXBElement<String>(_ConfirmarCFEReferenciaERP2_QNAME, String.class, EnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cfexml", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFECfexml(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCfexml_QNAME, String.class, EnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFETenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class, EnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFENomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class, EnvioCFE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "version", scope = EnvioCFE.class)
    public JAXBElement<String> createEnvioCFEVersion(String value) {
        return new JAXBElement<String>(_ConfirmarCFEVersion_QNAME, String.class, EnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SICFERespuesta
     * }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "ActualizarReceptoresNoElectronicosResult", scope = ActualizarReceptoresNoElectronicosResponse.class)
    public JAXBElement<SICFERespuesta> createActualizarReceptoresNoElectronicosResponseActualizarReceptoresNoElectronicosResult(
            SICFERespuesta value) {
        return new JAXBElement<SICFERespuesta>(
                _ActualizarReceptoresNoElectronicosResponseActualizarReceptoresNoElectronicosResult_QNAME,
                SICFERespuesta.class, ActualizarReceptoresNoElectronicosResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = ValidarEnvioCFE.class)
    public JAXBElement<String> createValidarEnvioCFEClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, ValidarEnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cliente", scope = ValidarEnvioCFE.class)
    public JAXBElement<String> createValidarEnvioCFECliente(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCliente_QNAME, String.class, ValidarEnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cfexml", scope = ValidarEnvioCFE.class)
    public JAXBElement<String> createValidarEnvioCFECfexml(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCfexml_QNAME, String.class, ValidarEnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = ValidarEnvioCFE.class)
    public JAXBElement<String> createValidarEnvioCFETenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class, ValidarEnvioCFE.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = ValidarEnvioCFE.class)
    public JAXBElement<String> createValidarEnvioCFENomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                ValidarEnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "version", scope = ValidarEnvioCFE.class)
    public JAXBElement<String> createValidarEnvioCFEVersion(String value) {
        return new JAXBElement<String>(_ConfirmarCFEVersion_QNAME, String.class, ValidarEnvioCFE.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "rucemisor", scope = DesconfirmarCFERecibido.class)
    public JAXBElement<String> createDesconfirmarCFERecibidoRucemisor(String value) {
        return new JAXBElement<String>(_ConfirmarCFERecibidoRucemisor_QNAME, String.class,
                DesconfirmarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = DesconfirmarCFERecibido.class)
    public JAXBElement<String> createDesconfirmarCFERecibidoClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, DesconfirmarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "param_tenant", scope = DesconfirmarCFERecibido.class)
    public JAXBElement<String> createDesconfirmarCFERecibidoParamTenant(String value) {
        return new JAXBElement<String>(_ObtenerCAEParamTenant_QNAME, String.class, DesconfirmarCFERecibido.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "serie", scope = DesconfirmarCFERecibido.class)
    public JAXBElement<String> createDesconfirmarCFERecibidoSerie(String value) {
        return new JAXBElement<String>(_ObtenerCFEPorIDSerie_QNAME, String.class, DesconfirmarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = DesconfirmarCFERecibido.class)
    public JAXBElement<String> createDesconfirmarCFERecibidoNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                DesconfirmarCFERecibido.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "clave", scope = EnvioCFESinFirmar.class)
    public JAXBElement<String> createEnvioCFESinFirmarClave(String value) {
        return new JAXBElement<String>(_ObtenerXMLDGIClave_QNAME, String.class, EnvioCFESinFirmar.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cliente", scope = EnvioCFESinFirmar.class)
    public JAXBElement<String> createEnvioCFESinFirmarCliente(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCliente_QNAME, String.class, EnvioCFESinFirmar.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "refErp", scope = EnvioCFESinFirmar.class)
    public JAXBElement<String> createEnvioCFESinFirmarRefErp(String value) {
        return new JAXBElement<String>(_EnvioCFESinFirmarRefErp_QNAME, String.class, EnvioCFESinFirmar.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "cfexml", scope = EnvioCFESinFirmar.class)
    public JAXBElement<String> createEnvioCFESinFirmarCfexml(String value) {
        return new JAXBElement<String>(_EnvioCFEConReceptorCfexml_QNAME, String.class, EnvioCFESinFirmar.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "tenant", scope = EnvioCFESinFirmar.class)
    public JAXBElement<String> createEnvioCFESinFirmarTenant(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosTenant_QNAME, String.class, EnvioCFESinFirmar.class,
                value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "nomusuario", scope = EnvioCFESinFirmar.class)
    public JAXBElement<String> createEnvioCFESinFirmarNomusuario(String value) {
        return new JAXBElement<String>(_ObtenerClientesElectronicosNomusuario_QNAME, String.class,
                EnvioCFESinFirmar.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "version", scope = EnvioCFESinFirmar.class)
    public JAXBElement<String> createEnvioCFESinFirmarVersion(String value) {
        return new JAXBElement<String>(_ConfirmarCFEVersion_QNAME, String.class, EnvioCFESinFirmar.class, value);
    }

}
