
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCFEsRecibidosExtendido;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ObtenerCFEsRecibidosExtendidoResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}RespuestaObtenerCFEsRecibidosExtendido" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "obtenerCFEsRecibidosExtendidoResult" })
@XmlRootElement(name = "ObtenerCFEsRecibidosExtendidoResponse")
public class ObtenerCFEsRecibidosExtendidoResponse {

    @XmlElementRef(name = "ObtenerCFEsRecibidosExtendidoResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<RespuestaObtenerCFEsRecibidosExtendido> obtenerCFEsRecibidosExtendidoResult;

    /**
     * Gets the value of the obtenerCFEsRecibidosExtendidoResult property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link RespuestaObtenerCFEsRecibidosExtendido }{@code >}
     * 
     */
    public JAXBElement<RespuestaObtenerCFEsRecibidosExtendido> getObtenerCFEsRecibidosExtendidoResult() {
        return obtenerCFEsRecibidosExtendidoResult;
    }

    /**
     * Sets the value of the obtenerCFEsRecibidosExtendidoResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link RespuestaObtenerCFEsRecibidosExtendido
     *              }{@code >}
     * 
     */
    public void setObtenerCFEsRecibidosExtendidoResult(JAXBElement<RespuestaObtenerCFEsRecibidosExtendido> value) {
        this.obtenerCFEsRecibidosExtendidoResult = value;
    }

}
