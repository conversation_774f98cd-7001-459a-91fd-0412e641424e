
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="AnularRangoResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "anularRangoResult" })
@XmlRootElement(name = "AnularRangoResponse")
public class AnularRangoResponse {

    @XmlElementRef(name = "AnularRangoResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<SICFERespuesta> anularRangoResult;

    /**
     * Gets the value of the anularRangoResult property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link SICFERespuesta
     *         }{@code >}
     * 
     */
    public JAXBElement<SICFERespuesta> getAnularRangoResult() {
        return anularRangoResult;
    }

    /**
     * Sets the value of the anularRangoResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link SICFERespuesta }{@code >}
     * 
     */
    public void setAnularRangoResult(JAXBElement<SICFERespuesta> value) {
        this.anularRangoResult = value;
    }

}
