
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nomusuario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pass" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="param_tenant" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="p_tipo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="p_serie" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="p_desde" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="p_hasta" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="p_sucursal" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="es_pre_numerado" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "nomusuario", "pass", "paramTenant", "pTipo", "pSerie", "pDesde", "pHasta",
        "pSucursal", "esPreNumerado" })
@XmlRootElement(name = "AnularNumeracion")
public class AnularNumeracion {

    @XmlElementRef(name = "nomusuario", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> nomusuario;
    @XmlElementRef(name = "pass", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pass;
    @XmlElementRef(name = "param_tenant", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> paramTenant;
    @XmlElement(name = "p_tipo")
    protected Integer pTipo;
    @XmlElementRef(name = "p_serie", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pSerie;
    @XmlElement(name = "p_desde")
    protected Integer pDesde;
    @XmlElement(name = "p_hasta")
    protected Integer pHasta;
    @XmlElement(name = "p_sucursal")
    protected Integer pSucursal;
    @XmlElement(name = "es_pre_numerado")
    protected Boolean esPreNumerado;

    /**
     * Gets the value of the nomusuario property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getNomusuario() {
        return nomusuario;
    }

    /**
     * Sets the value of the nomusuario property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setNomusuario(JAXBElement<String> value) {
        this.nomusuario = value;
    }

    /**
     * Gets the value of the pass property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getPass() {
        return pass;
    }

    /**
     * Sets the value of the pass property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setPass(JAXBElement<String> value) {
        this.pass = value;
    }

    /**
     * Gets the value of the paramTenant property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getParamTenant() {
        return paramTenant;
    }

    /**
     * Sets the value of the paramTenant property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setParamTenant(JAXBElement<String> value) {
        this.paramTenant = value;
    }

    /**
     * Gets the value of the pTipo property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getPTipo() {
        return pTipo;
    }

    /**
     * Sets the value of the pTipo property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setPTipo(Integer value) {
        this.pTipo = value;
    }

    /**
     * Gets the value of the pSerie property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getPSerie() {
        return pSerie;
    }

    /**
     * Sets the value of the pSerie property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setPSerie(JAXBElement<String> value) {
        this.pSerie = value;
    }

    /**
     * Gets the value of the pDesde property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getPDesde() {
        return pDesde;
    }

    /**
     * Sets the value of the pDesde property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setPDesde(Integer value) {
        this.pDesde = value;
    }

    /**
     * Gets the value of the pHasta property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getPHasta() {
        return pHasta;
    }

    /**
     * Sets the value of the pHasta property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setPHasta(Integer value) {
        this.pHasta = value;
    }

    /**
     * Gets the value of the pSucursal property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getPSucursal() {
        return pSucursal;
    }

    /**
     * Sets the value of the pSucursal property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setPSucursal(Integer value) {
        this.pSucursal = value;
    }

    /**
     * Gets the value of the esPreNumerado property.
     * 
     * @return possible object is {@link Boolean }
     * 
     */
    public Boolean isEsPreNumerado() {
        return esPreNumerado;
    }

    /**
     * Sets the value of the esPreNumerado property.
     * 
     * @param value allowed object is {@link Boolean }
     * 
     */
    public void setEsPreNumerado(Boolean value) {
        this.esPreNumerado = value;
    }

}
