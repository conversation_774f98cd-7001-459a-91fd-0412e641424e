
package org.tempuri;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;
import org.datacontract.schemas._2004._07.sicfecontract.ArrayOfRechazoAcuseRecibo;
import org.datacontract.schemas._2004._07.sicfecontract.DataReceptorNoElectronico;
import org.datacontract.schemas._2004._07.sicfecontract.IdCFE;
import org.datacontract.schemas._2004._07.sicfecontract.ObtenerTemplateImpresionRespuesta;
import org.datacontract.schemas._2004._07.sicfecontract.ReservarNro;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCAE;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCFEsRecibidos;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCFEsRecibidosExtendido;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerRecursosImpresion;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaBuffer;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaCertificados;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaDatosEmisorReceptor;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEmisorReceptorElectronico;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEnvioCFE;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEstadoCFE;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerCFE;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerClientesElectronicos;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerProveedoresElectronicos;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerXML;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaPing;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaReimpresion;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaVersion;
import org.datacontract.schemas._2004._07.sicfecontract.SicfeRespuestaConsolidar;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "ISICFEEmisor", targetNamespace = "http://tempuri.org/")
@XmlSeeAlso({ com.microsoft.schemas._2003._10.serialization.ObjectFactory.class,
        com.microsoft.schemas._2003._10.serialization.arrays.ObjectFactory.class,
        org.datacontract.schemas._2004._07.sicfecontract.ObjectFactory.class, org.tempuri.ObjectFactory.class })
public interface ISICFEEmisor {

    /**
     * 
     * @param recurso
     * @param template
     * @param clave
     * @param cfexml
     * @param nomusuario
     * @param version
     * @param cliente
     * @param erpPideValidacion
     * @param imprime
     * @param devolverXML
     * @param devolverQR
     * @param referenciaERP
     * @param referenciaERP2
     * @param tenant
     * @param sizeQR
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEnvioCFE
     */
    @WebMethod(operationName = "EnvioCFE", action = "http://tempuri.org/ISICFEEmisor/EnvioCFE")
    @WebResult(name = "EnvioCFEResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "EnvioCFE", targetNamespace = "http://tempuri.org/", className = "org.tempuri.EnvioCFE")
    @ResponseWrapper(localName = "EnvioCFEResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.EnvioCFEResponse")
    public SICFERespuestaEnvioCFE envioCFE(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant,
            @WebParam(name = "cliente", targetNamespace = "http://tempuri.org/") String cliente,
            @WebParam(name = "cfexml", targetNamespace = "http://tempuri.org/") String cfexml,
            @WebParam(name = "referenciaERP", targetNamespace = "http://tempuri.org/") String referenciaERP,
            @WebParam(name = "referenciaERP2", targetNamespace = "http://tempuri.org/") String referenciaERP2,
            @WebParam(name = "devolverQR", targetNamespace = "http://tempuri.org/") Boolean devolverQR,
            @WebParam(name = "sizeQR", targetNamespace = "http://tempuri.org/") Integer sizeQR,
            @WebParam(name = "imprime", targetNamespace = "http://tempuri.org/") Integer imprime,
            @WebParam(name = "recurso", targetNamespace = "http://tempuri.org/") String recurso,
            @WebParam(name = "template", targetNamespace = "http://tempuri.org/") String template,
            @WebParam(name = "devolverXML", targetNamespace = "http://tempuri.org/") Boolean devolverXML,
            @WebParam(name = "erpPideValidacion", targetNamespace = "http://tempuri.org/") Boolean erpPideValidacion,
            @WebParam(name = "version", targetNamespace = "http://tempuri.org/") String version);

    /**
     * 
     * @param template
     * @param clave
     * @param cfexml
     * @param nomusuario
     * @param cliente
     * @param impresora
     * @param imprime
     * @param correo
     * @param versionXSD
     * @param devolverQR
     * @param referenciaERP
     * @param tenant
     * @param fechaEnvioCorreo
     * @param sizeQR
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEnvioCFE
     */
    @WebMethod(operationName = "EnvioCFEConReceptor", action = "http://tempuri.org/ISICFEEmisor/EnvioCFEConReceptor")
    @WebResult(name = "EnvioCFEConReceptorResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "EnvioCFEConReceptor", targetNamespace = "http://tempuri.org/", className = "org.tempuri.EnvioCFEConReceptor")
    @ResponseWrapper(localName = "EnvioCFEConReceptorResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.EnvioCFEConReceptorResponse")
    public SICFERespuestaEnvioCFE envioCFEConReceptor(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant,
            @WebParam(name = "cliente", targetNamespace = "http://tempuri.org/") String cliente,
            @WebParam(name = "cfexml", targetNamespace = "http://tempuri.org/") String cfexml,
            @WebParam(name = "referenciaERP", targetNamespace = "http://tempuri.org/") String referenciaERP,
            @WebParam(name = "devolverQR", targetNamespace = "http://tempuri.org/") Boolean devolverQR,
            @WebParam(name = "sizeQR", targetNamespace = "http://tempuri.org/") Integer sizeQR,
            @WebParam(name = "imprime", targetNamespace = "http://tempuri.org/") Integer imprime,
            @WebParam(name = "impresora", targetNamespace = "http://tempuri.org/") String impresora,
            @WebParam(name = "template", targetNamespace = "http://tempuri.org/") String template,
            @WebParam(name = "versionXSD", targetNamespace = "http://tempuri.org/") String versionXSD,
            @WebParam(name = "correo", targetNamespace = "http://tempuri.org/") String correo,
            @WebParam(name = "fechaEnvioCorreo", targetNamespace = "http://tempuri.org/") XMLGregorianCalendar fechaEnvioCorreo);

    /**
     * 
     * @param cliente
     * @param clave
     * @param cfexml
     * @param nomusuario
     * @param version
     * @param tenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEnvioCFE
     */
    @WebMethod(operationName = "ValidarEnvioCFE", action = "http://tempuri.org/ISICFEEmisor/ValidarEnvioCFE")
    @WebResult(name = "ValidarEnvioCFEResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ValidarEnvioCFE", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ValidarEnvioCFE")
    @ResponseWrapper(localName = "ValidarEnvioCFEResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ValidarEnvioCFEResponse")
    public SICFERespuestaEnvioCFE validarEnvioCFE(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant,
            @WebParam(name = "cliente", targetNamespace = "http://tempuri.org/") String cliente,
            @WebParam(name = "cfexml", targetNamespace = "http://tempuri.org/") String cfexml,
            @WebParam(name = "version", targetNamespace = "http://tempuri.org/") String version);

    /**
     * 
     * @param clave
     * @param pMotivosRechazo
     * @param pIdCFE
     * @param paramTenant
     * @param pRucEmisor
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta
     */
    @WebMethod(operationName = "RechazarCFERecibido", action = "http://tempuri.org/ISICFEEmisor/RechazarCFERecibido")
    @WebResult(name = "RechazarCFERecibidoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "RechazarCFERecibido", targetNamespace = "http://tempuri.org/", className = "org.tempuri.RechazarCFERecibido")
    @ResponseWrapper(localName = "RechazarCFERecibidoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.RechazarCFERecibidoResponse")
    public SICFERespuesta rechazarCFERecibido(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant,
            @WebParam(name = "p_rucEmisor", targetNamespace = "http://tempuri.org/") Long pRucEmisor,
            @WebParam(name = "p_idCFE", targetNamespace = "http://tempuri.org/") IdCFE pIdCFE,
            @WebParam(name = "p_motivosRechazo", targetNamespace = "http://tempuri.org/") ArrayOfRechazoAcuseRecibo pMotivosRechazo);

    /**
     * 
     * @param clave
     * @param pIdCFE
     * @param paramTenant
     * @param pRucEmisor
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta
     */
    @WebMethod(operationName = "AceptarCFERecibido", action = "http://tempuri.org/ISICFEEmisor/AceptarCFERecibido")
    @WebResult(name = "AceptarCFERecibidoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "AceptarCFERecibido", targetNamespace = "http://tempuri.org/", className = "org.tempuri.AceptarCFERecibido")
    @ResponseWrapper(localName = "AceptarCFERecibidoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.AceptarCFERecibidoResponse")
    public SICFERespuesta aceptarCFERecibido(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant,
            @WebParam(name = "p_rucEmisor", targetNamespace = "http://tempuri.org/") Long pRucEmisor,
            @WebParam(name = "p_idCFE", targetNamespace = "http://tempuri.org/") IdCFE pIdCFE);

    /**
     * 
     * @param template
     * @param clave
     * @param pIdCFE
     * @param pTenant
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaBuffer
     */
    @WebMethod(operationName = "ObtenerPDF", action = "http://tempuri.org/ISICFEEmisor/ObtenerPDF")
    @WebResult(name = "ObtenerPDFResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerPDF", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerPDF")
    @ResponseWrapper(localName = "ObtenerPDFResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerPDFResponse")
    public SICFERespuestaBuffer obtenerPDF(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "p_idCFE", targetNamespace = "http://tempuri.org/") IdCFE pIdCFE,
            @WebParam(name = "p_tenant", targetNamespace = "http://tempuri.org/") String pTenant,
            @WebParam(name = "template", targetNamespace = "http://tempuri.org/") String template);

    /**
     * 
     * @param template
     * @param clave
     * @param tipoCFE
     * @param xml
     * @param pTenant
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaBuffer
     */
    @WebMethod(operationName = "ObtenerPDFSinProcesar", action = "http://tempuri.org/ISICFEEmisor/ObtenerPDFSinProcesar")
    @WebResult(name = "ObtenerPDFSinProcesarResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerPDFSinProcesar", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerPDFSinProcesar")
    @ResponseWrapper(localName = "ObtenerPDFSinProcesarResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerPDFSinProcesarResponse")
    public SICFERespuestaBuffer obtenerPDFSinProcesar(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "xml", targetNamespace = "http://tempuri.org/") String xml,
            @WebParam(name = "p_tenant", targetNamespace = "http://tempuri.org/") String pTenant,
            @WebParam(name = "tipoCFE", targetNamespace = "http://tempuri.org/") Integer tipoCFE,
            @WebParam(name = "template", targetNamespace = "http://tempuri.org/") String template);

    /**
     * 
     * @param clave
     * @param pIdCFE
     * @param pTenant
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerXML
     */
    @WebMethod(operationName = "ObtenerXML_DGI", action = "http://tempuri.org/ISICFEEmisor/ObtenerXML_DGI")
    @WebResult(name = "ObtenerXML_DGIResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerXML_DGI", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerXMLDGI")
    @ResponseWrapper(localName = "ObtenerXML_DGIResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerXMLDGIResponse")
    public SICFERespuestaObtenerXML obtenerXMLDGI(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "p_idCFE", targetNamespace = "http://tempuri.org/") IdCFE pIdCFE,
            @WebParam(name = "p_tenant", targetNamespace = "http://tempuri.org/") String pTenant);

    /**
     * 
     * @param clave
     * @param nomusuario
     * @param tenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerRecursosImpresion
     */
    @WebMethod(operationName = "ObtenerRecursosImpresion", action = "http://tempuri.org/ISICFEEmisor/ObtenerRecursosImpresion")
    @WebResult(name = "ObtenerRecursosImpresionResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerRecursosImpresion", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerRecursosImpresion")
    @ResponseWrapper(localName = "ObtenerRecursosImpresionResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerRecursosImpresionResponse")
    public RespuestaObtenerRecursosImpresion obtenerRecursosImpresion(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant);

    /**
     * 
     * @param template
     * @param clave
     * @param impresora
     * @param paramTenant
     * @param pID
     * @param nroImpresiones
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaReimpresion
     */
    @WebMethod(operationName = "Reimprimir", action = "http://tempuri.org/ISICFEEmisor/Reimprimir")
    @WebResult(name = "ReimprimirResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "Reimprimir", targetNamespace = "http://tempuri.org/", className = "org.tempuri.Reimprimir")
    @ResponseWrapper(localName = "ReimprimirResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ReimprimirResponse")
    public SICFERespuestaReimpresion reimprimir(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant,
            @WebParam(name = "p_ID", targetNamespace = "http://tempuri.org/") IdCFE pID,
            @WebParam(name = "impresora", targetNamespace = "http://tempuri.org/") String impresora,
            @WebParam(name = "nroImpresiones", targetNamespace = "http://tempuri.org/") Integer nroImpresiones,
            @WebParam(name = "template", targetNamespace = "http://tempuri.org/") String template);

    /**
     * 
     * @param clave
     * @param estado
     * @param fechaHasta
     * @param fechaDesde
     * @param paramTenant
     * @param nomusuario
     * @param rucEmisor
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCFEsRecibidos
     */
    @WebMethod(operationName = "ObtenerCFEsRecibidos", action = "http://tempuri.org/ISICFEEmisor/ObtenerCFEsRecibidos")
    @WebResult(name = "ObtenerCFEsRecibidosResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerCFEsRecibidos", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCFEsRecibidos")
    @ResponseWrapper(localName = "ObtenerCFEsRecibidosResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCFEsRecibidosResponse")
    public RespuestaObtenerCFEsRecibidos obtenerCFEsRecibidos(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant,
            @WebParam(name = "fecha_desde", targetNamespace = "http://tempuri.org/") String fechaDesde,
            @WebParam(name = "fecha_hasta", targetNamespace = "http://tempuri.org/") String fechaHasta,
            @WebParam(name = "estado", targetNamespace = "http://tempuri.org/") String estado,
            @WebParam(name = "rucEmisor", targetNamespace = "http://tempuri.org/") String rucEmisor);

    /**
     * 
     * @param pPass
     * @param pDatoReceptor
     * @param pTenant
     * @param pNomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta
     */
    @WebMethod(operationName = "ActualizarReceptoresNoElectronicos", action = "http://tempuri.org/ISICFEEmisor/ActualizarReceptoresNoElectronicos")
    @WebResult(name = "ActualizarReceptoresNoElectronicosResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ActualizarReceptoresNoElectronicos", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ActualizarReceptoresNoElectronicos")
    @ResponseWrapper(localName = "ActualizarReceptoresNoElectronicosResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ActualizarReceptoresNoElectronicosResponse")
    public SICFERespuesta actualizarReceptoresNoElectronicos(
            @WebParam(name = "p_nomusuario", targetNamespace = "http://tempuri.org/") String pNomusuario,
            @WebParam(name = "p_pass", targetNamespace = "http://tempuri.org/") String pPass,
            @WebParam(name = "p_tenant", targetNamespace = "http://tempuri.org/") String pTenant,
            @WebParam(name = "p_datoReceptor", targetNamespace = "http://tempuri.org/") DataReceptorNoElectronico pDatoReceptor);

    /**
     * 
     * @param clave
     * @param estado
     * @param fechaHasta
     * @param fechaDesde
     * @param devolverXML
     * @param paramTenant
     * @param nomusuario
     * @param rucEmisor
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCFEsRecibidosExtendido
     */
    @WebMethod(operationName = "ObtenerCFEsRecibidosExtendido", action = "http://tempuri.org/ISICFEEmisor/ObtenerCFEsRecibidosExtendido")
    @WebResult(name = "ObtenerCFEsRecibidosExtendidoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerCFEsRecibidosExtendido", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCFEsRecibidosExtendido")
    @ResponseWrapper(localName = "ObtenerCFEsRecibidosExtendidoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCFEsRecibidosExtendidoResponse")
    public RespuestaObtenerCFEsRecibidosExtendido obtenerCFEsRecibidosExtendido(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant,
            @WebParam(name = "fecha_desde", targetNamespace = "http://tempuri.org/") String fechaDesde,
            @WebParam(name = "fecha_hasta", targetNamespace = "http://tempuri.org/") String fechaHasta,
            @WebParam(name = "estado", targetNamespace = "http://tempuri.org/") String estado,
            @WebParam(name = "devolverXML", targetNamespace = "http://tempuri.org/") Boolean devolverXML,
            @WebParam(name = "rucEmisor", targetNamespace = "http://tempuri.org/") String rucEmisor);

    /**
     * 
     * @param cliente
     * @param clave
     * @param erpPideValidacion
     * @param cfexml
     * @param referenciaERP
     * @param referenciaERP2
     * @param nomusuario
     * @param version
     * @param tenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEnvioCFE
     */
    @WebMethod(operationName = "ConfirmarCFE", action = "http://tempuri.org/ISICFEEmisor/ConfirmarCFE")
    @WebResult(name = "ConfirmarCFEResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ConfirmarCFE", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ConfirmarCFE")
    @ResponseWrapper(localName = "ConfirmarCFEResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ConfirmarCFEResponse")
    public SICFERespuestaEnvioCFE confirmarCFE(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant,
            @WebParam(name = "cliente", targetNamespace = "http://tempuri.org/") String cliente,
            @WebParam(name = "cfexml", targetNamespace = "http://tempuri.org/") String cfexml,
            @WebParam(name = "referenciaERP", targetNamespace = "http://tempuri.org/") String referenciaERP,
            @WebParam(name = "referenciaERP2", targetNamespace = "http://tempuri.org/") String referenciaERP2,
            @WebParam(name = "erpPideValidacion", targetNamespace = "http://tempuri.org/") Boolean erpPideValidacion,
            @WebParam(name = "version", targetNamespace = "http://tempuri.org/") String version);

    /**
     * 
     * @param cliente
     * @param clave
     * @param cfexml
     * @param nomusuario
     * @param tenant
     * @return returns org.datacontract.schemas._2004._07.sicfecontract.ReservarNro
     */
    @WebMethod(operationName = "ReservarNro", action = "http://tempuri.org/ISICFEEmisor/ReservarNro")
    @WebResult(name = "ReservarNroResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ReservarNro", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ReservarNro")
    @ResponseWrapper(localName = "ReservarNroResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ReservarNroResponse")
    public ReservarNro reservarNro(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant,
            @WebParam(name = "cliente", targetNamespace = "http://tempuri.org/") String cliente,
            @WebParam(name = "cfexml", targetNamespace = "http://tempuri.org/") String cfexml);

    /**
     * 
     * @param clave
     * @param nomusuario
     * @param tenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaPing
     */
    @WebMethod(operationName = "PingSICFEEmisor", action = "http://tempuri.org/ISICFEEmisor/PingSICFEEmisor")
    @WebResult(name = "PingSICFEEmisorResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "PingSICFEEmisor", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PingSICFEEmisor")
    @ResponseWrapper(localName = "PingSICFEEmisorResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.PingSICFEEmisorResponse")
    public SICFERespuestaPing pingSICFEEmisor(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant);

    /**
     * 
     * @param pSucursal
     * @param pHasta
     * @param pass
     * @param pTipo
     * @param paramTenant
     * @param pSerie
     * @param nomusuario
     * @param pDesde
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta
     */
    @WebMethod(operationName = "AnularRango", action = "http://tempuri.org/ISICFEEmisor/AnularRango")
    @WebResult(name = "AnularRangoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "AnularRango", targetNamespace = "http://tempuri.org/", className = "org.tempuri.AnularRango")
    @ResponseWrapper(localName = "AnularRangoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.AnularRangoResponse")
    public SICFERespuesta anularRango(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "pass", targetNamespace = "http://tempuri.org/") String pass,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant,
            @WebParam(name = "p_tipo", targetNamespace = "http://tempuri.org/") Integer pTipo,
            @WebParam(name = "p_serie", targetNamespace = "http://tempuri.org/") String pSerie,
            @WebParam(name = "p_desde", targetNamespace = "http://tempuri.org/") Integer pDesde,
            @WebParam(name = "p_hasta", targetNamespace = "http://tempuri.org/") Integer pHasta,
            @WebParam(name = "p_sucursal", targetNamespace = "http://tempuri.org/") Integer pSucursal);

    /**
     * 
     * @param pSucursal
     * @param pHasta
     * @param pass
     * @param pTipo
     * @param paramTenant
     * @param esPreNumerado
     * @param pSerie
     * @param nomusuario
     * @param pDesde
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta
     */
    @WebMethod(operationName = "AnularNumeracion", action = "http://tempuri.org/ISICFEEmisor/AnularNumeracion")
    @WebResult(name = "AnularNumeracionResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "AnularNumeracion", targetNamespace = "http://tempuri.org/", className = "org.tempuri.AnularNumeracion")
    @ResponseWrapper(localName = "AnularNumeracionResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.AnularNumeracionResponse")
    public SICFERespuesta anularNumeracion(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "pass", targetNamespace = "http://tempuri.org/") String pass,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant,
            @WebParam(name = "p_tipo", targetNamespace = "http://tempuri.org/") Integer pTipo,
            @WebParam(name = "p_serie", targetNamespace = "http://tempuri.org/") String pSerie,
            @WebParam(name = "p_desde", targetNamespace = "http://tempuri.org/") Integer pDesde,
            @WebParam(name = "p_hasta", targetNamespace = "http://tempuri.org/") Integer pHasta,
            @WebParam(name = "p_sucursal", targetNamespace = "http://tempuri.org/") Integer pSucursal,
            @WebParam(name = "es_pre_numerado", targetNamespace = "http://tempuri.org/") Boolean esPreNumerado);

    /**
     * 
     * @param pIDCFE
     * @param pClave
     * @param pTenant
     * @param pUsuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEstadoCFE
     */
    @WebMethod(operationName = "ObtenerEstadoCFE", action = "http://tempuri.org/ISICFEEmisor/ObtenerEstadoCFE")
    @WebResult(name = "ObtenerEstadoCFEResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerEstadoCFE", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerEstadoCFE")
    @ResponseWrapper(localName = "ObtenerEstadoCFEResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerEstadoCFEResponse")
    public SICFERespuestaEstadoCFE obtenerEstadoCFE(
            @WebParam(name = "pUsuario", targetNamespace = "http://tempuri.org/") String pUsuario,
            @WebParam(name = "pClave", targetNamespace = "http://tempuri.org/") String pClave,
            @WebParam(name = "pTenant", targetNamespace = "http://tempuri.org/") String pTenant,
            @WebParam(name = "pIDCFE", targetNamespace = "http://tempuri.org/") IdCFE pIDCFE);

    /**
     * 
     * @param pass
     * @param paramTenant
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCAE
     */
    @WebMethod(operationName = "ObtenerCAE", action = "http://tempuri.org/ISICFEEmisor/ObtenerCAE")
    @WebResult(name = "ObtenerCAEResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerCAE", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCAE")
    @ResponseWrapper(localName = "ObtenerCAEResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCAEResponse")
    public RespuestaObtenerCAE obtenerCAE(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "pass", targetNamespace = "http://tempuri.org/") String pass,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant);

    /**
     * 
     * @param pass
     * @param paramTenant
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.ObtenerTemplateImpresionRespuesta
     */
    @WebMethod(operationName = "ObtenerTemplatesImpresion", action = "http://tempuri.org/ISICFEEmisor/ObtenerTemplatesImpresion")
    @WebResult(name = "ObtenerTemplatesImpresionResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerTemplatesImpresion", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerTemplatesImpresion")
    @ResponseWrapper(localName = "ObtenerTemplatesImpresionResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerTemplatesImpresionResponse")
    public ObtenerTemplateImpresionRespuesta obtenerTemplatesImpresion(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "pass", targetNamespace = "http://tempuri.org/") String pass,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant);

    /**
     * 
     * @param pass
     * @param fechaInicio
     * @param nomusuario
     * @param fechaFin
     * @param tenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SicfeRespuestaConsolidar
     */
    @WebMethod(operationName = "ConsolidarComprobantes", action = "http://tempuri.org/ISICFEEmisor/ConsolidarComprobantes")
    @WebResult(name = "ConsolidarComprobantesResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ConsolidarComprobantes", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ConsolidarComprobantes")
    @ResponseWrapper(localName = "ConsolidarComprobantesResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ConsolidarComprobantesResponse")
    public SicfeRespuestaConsolidar consolidarComprobantes(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "pass", targetNamespace = "http://tempuri.org/") String pass,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant,
            @WebParam(name = "fechaInicio", targetNamespace = "http://tempuri.org/") XMLGregorianCalendar fechaInicio,
            @WebParam(name = "fechaFin", targetNamespace = "http://tempuri.org/") XMLGregorianCalendar fechaFin);

    /**
     * 
     * @param pass
     * @param nomusuario
     * @param tenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerProveedoresElectronicos
     */
    @WebMethod(operationName = "ObtenerProveedoresElectronicos", action = "http://tempuri.org/ISICFEEmisor/ObtenerProveedoresElectronicos")
    @WebResult(name = "ObtenerProveedoresElectronicosResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerProveedoresElectronicos", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerProveedoresElectronicos")
    @ResponseWrapper(localName = "ObtenerProveedoresElectronicosResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerProveedoresElectronicosResponse")
    public SICFERespuestaObtenerProveedoresElectronicos obtenerProveedoresElectronicos(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "pass", targetNamespace = "http://tempuri.org/") String pass,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant);

    /**
     * 
     * @param pass
     * @param nomusuario
     * @param tenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerClientesElectronicos
     */
    @WebMethod(operationName = "ObtenerClientesElectronicos", action = "http://tempuri.org/ISICFEEmisor/ObtenerClientesElectronicos")
    @WebResult(name = "ObtenerClientesElectronicosResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerClientesElectronicos", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerClientesElectronicos")
    @ResponseWrapper(localName = "ObtenerClientesElectronicosResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerClientesElectronicosResponse")
    public SICFERespuestaObtenerClientesElectronicos obtenerClientesElectronicos(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "pass", targetNamespace = "http://tempuri.org/") String pass,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant);

    /**
     * 
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaVersion
     */
    @WebMethod(operationName = "ObtenerVersion", action = "http://tempuri.org/ISICFEEmisor/ObtenerVersion")
    @WebResult(name = "ObtenerVersionResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerVersion", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerVersion")
    @ResponseWrapper(localName = "ObtenerVersionResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerVersionResponse")
    public SICFERespuestaVersion obtenerVersion();

    /**
     * 
     * @param clave
     * @param tipo
     * @param rucemisor
     * @param numero
     * @param serie
     * @param paramTenant
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta
     */
    @WebMethod(operationName = "ConfirmarCFERecibido", action = "http://tempuri.org/ISICFEEmisor/ConfirmarCFERecibido")
    @WebResult(name = "ConfirmarCFERecibidoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ConfirmarCFERecibido", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ConfirmarCFERecibido")
    @ResponseWrapper(localName = "ConfirmarCFERecibidoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ConfirmarCFERecibidoResponse")
    public SICFERespuesta confirmarCFERecibido(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant,
            @WebParam(name = "tipo", targetNamespace = "http://tempuri.org/") Short tipo,
            @WebParam(name = "serie", targetNamespace = "http://tempuri.org/") String serie,
            @WebParam(name = "numero", targetNamespace = "http://tempuri.org/") Long numero,
            @WebParam(name = "rucemisor", targetNamespace = "http://tempuri.org/") String rucemisor);

    /**
     * 
     * @param clave
     * @param tipo
     * @param rucemisor
     * @param numero
     * @param serie
     * @param paramTenant
     * @param nomusuario
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta
     */
    @WebMethod(operationName = "DesconfirmarCFERecibido", action = "http://tempuri.org/ISICFEEmisor/DesconfirmarCFERecibido")
    @WebResult(name = "DesconfirmarCFERecibidoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "DesconfirmarCFERecibido", targetNamespace = "http://tempuri.org/", className = "org.tempuri.DesconfirmarCFERecibido")
    @ResponseWrapper(localName = "DesconfirmarCFERecibidoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.DesconfirmarCFERecibidoResponse")
    public SICFERespuesta desconfirmarCFERecibido(
            @WebParam(name = "nomusuario", targetNamespace = "http://tempuri.org/") String nomusuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant,
            @WebParam(name = "tipo", targetNamespace = "http://tempuri.org/") Short tipo,
            @WebParam(name = "serie", targetNamespace = "http://tempuri.org/") String serie,
            @WebParam(name = "numero", targetNamespace = "http://tempuri.org/") Long numero,
            @WebParam(name = "rucemisor", targetNamespace = "http://tempuri.org/") String rucemisor);

    /**
     * 
     * @param paramTenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaCertificados
     */
    @WebMethod(operationName = "ObtenerInfoCertificados", action = "http://tempuri.org/ISICFEEmisor/ObtenerInfoCertificados")
    @WebResult(name = "ObtenerInfoCertificadosResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerInfoCertificados", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerInfoCertificados")
    @ResponseWrapper(localName = "ObtenerInfoCertificadosResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerInfoCertificadosResponse")
    public SICFERespuestaCertificados obtenerInfoCertificados(
            @WebParam(name = "param_tenant", targetNamespace = "http://tempuri.org/") String paramTenant);

    /**
     * 
     * @param ruc
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEmisorReceptorElectronico
     */
    @WebMethod(operationName = "EsEmisorReceptorElectronico", action = "http://tempuri.org/ISICFEEmisor/EsEmisorReceptorElectronico")
    @WebResult(name = "EsEmisorReceptorElectronicoResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "EsEmisorReceptorElectronico", targetNamespace = "http://tempuri.org/", className = "org.tempuri.EsEmisorReceptorElectronico")
    @ResponseWrapper(localName = "EsEmisorReceptorElectronicoResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.EsEmisorReceptorElectronicoResponse")
    public SICFERespuestaEmisorReceptorElectronico esEmisorReceptorElectronico(
            @WebParam(name = "ruc", targetNamespace = "http://tempuri.org/") String ruc);

    /**
     * 
     * @param ruc
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaDatosEmisorReceptor
     */
    @WebMethod(operationName = "ObtenerDatosDeEmisorReceptor", action = "http://tempuri.org/ISICFEEmisor/ObtenerDatosDeEmisorReceptor")
    @WebResult(name = "ObtenerDatosDeEmisorReceptorResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerDatosDeEmisorReceptor", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerDatosDeEmisorReceptor")
    @ResponseWrapper(localName = "ObtenerDatosDeEmisorReceptorResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerDatosDeEmisorReceptorResponse")
    public SICFERespuestaDatosEmisorReceptor obtenerDatosDeEmisorReceptor(
            @WebParam(name = "ruc", targetNamespace = "http://tempuri.org/") String ruc);

    /**
     * 
     * @param clave
     * @param devolverXML
     * @param usuario
     * @param referenciaERP
     * @param tenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerCFE
     */
    @WebMethod(operationName = "ObtenerCFEPorReferencia", action = "http://tempuri.org/ISICFEEmisor/ObtenerCFEPorReferencia")
    @WebResult(name = "ObtenerCFEPorReferenciaResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerCFEPorReferencia", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCFEPorReferencia")
    @ResponseWrapper(localName = "ObtenerCFEPorReferenciaResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCFEPorReferenciaResponse")
    public SICFERespuestaObtenerCFE obtenerCFEPorReferencia(
            @WebParam(name = "usuario", targetNamespace = "http://tempuri.org/") String usuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant,
            @WebParam(name = "referenciaERP", targetNamespace = "http://tempuri.org/") String referenciaERP,
            @WebParam(name = "devolverXML", targetNamespace = "http://tempuri.org/") Boolean devolverXML);

    /**
     * 
     * @param clave
     * @param tipo
     * @param numero
     * @param serie
     * @param devolverXML
     * @param usuario
     * @param tenant
     * @return returns
     *         org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerCFE
     */
    @WebMethod(operationName = "ObtenerCFEPorID", action = "http://tempuri.org/ISICFEEmisor/ObtenerCFEPorID")
    @WebResult(name = "ObtenerCFEPorIDResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ObtenerCFEPorID", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCFEPorID")
    @ResponseWrapper(localName = "ObtenerCFEPorIDResponse", targetNamespace = "http://tempuri.org/", className = "org.tempuri.ObtenerCFEPorIDResponse")
    public SICFERespuestaObtenerCFE obtenerCFEPorID(
            @WebParam(name = "usuario", targetNamespace = "http://tempuri.org/") String usuario,
            @WebParam(name = "clave", targetNamespace = "http://tempuri.org/") String clave,
            @WebParam(name = "tenant", targetNamespace = "http://tempuri.org/") String tenant,
            @WebParam(name = "tipo", targetNamespace = "http://tempuri.org/") Short tipo,
            @WebParam(name = "serie", targetNamespace = "http://tempuri.org/") String serie,
            @WebParam(name = "numero", targetNamespace = "http://tempuri.org/") Long numero,
            @WebParam(name = "devolverXML", targetNamespace = "http://tempuri.org/") Boolean devolverXML);

}
