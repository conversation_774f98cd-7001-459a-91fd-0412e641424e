
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nomusuario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="clave" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="param_tenant" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tipo" type="{http://www.w3.org/2001/XMLSchema}short" minOccurs="0"/>
 *         &lt;element name="serie" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numero" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="rucemisor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "nomusuario", "clave", "paramTenant", "tipo", "serie", "numero", "rucemisor" })
@XmlRootElement(name = "ConfirmarCFERecibido")
public class ConfirmarCFERecibido {

    @XmlElementRef(name = "nomusuario", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> nomusuario;
    @XmlElementRef(name = "clave", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> clave;
    @XmlElementRef(name = "param_tenant", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> paramTenant;
    protected Short tipo;
    @XmlElementRef(name = "serie", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> serie;
    protected Long numero;
    @XmlElementRef(name = "rucemisor", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<String> rucemisor;

    /**
     * Gets the value of the nomusuario property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getNomusuario() {
        return nomusuario;
    }

    /**
     * Sets the value of the nomusuario property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setNomusuario(JAXBElement<String> value) {
        this.nomusuario = value;
    }

    /**
     * Gets the value of the clave property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getClave() {
        return clave;
    }

    /**
     * Sets the value of the clave property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setClave(JAXBElement<String> value) {
        this.clave = value;
    }

    /**
     * Gets the value of the paramTenant property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getParamTenant() {
        return paramTenant;
    }

    /**
     * Sets the value of the paramTenant property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setParamTenant(JAXBElement<String> value) {
        this.paramTenant = value;
    }

    /**
     * Gets the value of the tipo property.
     * 
     * @return possible object is {@link Short }
     * 
     */
    public Short getTipo() {
        return tipo;
    }

    /**
     * Sets the value of the tipo property.
     * 
     * @param value allowed object is {@link Short }
     * 
     */
    public void setTipo(Short value) {
        this.tipo = value;
    }

    /**
     * Gets the value of the serie property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getSerie() {
        return serie;
    }

    /**
     * Sets the value of the serie property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setSerie(JAXBElement<String> value) {
        this.serie = value;
    }

    /**
     * Gets the value of the numero property.
     * 
     * @return possible object is {@link Long }
     * 
     */
    public Long getNumero() {
        return numero;
    }

    /**
     * Sets the value of the numero property.
     * 
     * @param value allowed object is {@link Long }
     * 
     */
    public void setNumero(Long value) {
        this.numero = value;
    }

    /**
     * Gets the value of the rucemisor property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getRucemisor() {
        return rucemisor;
    }

    /**
     * Sets the value of the rucemisor property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setRucemisor(JAXBElement<String> value) {
        this.rucemisor = value;
    }

}
