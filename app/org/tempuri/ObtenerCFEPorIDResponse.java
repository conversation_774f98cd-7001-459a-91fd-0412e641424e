
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerCFE;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ObtenerCFEPorIDResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuestaObtenerCFE" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "obtenerCFEPorIDResult" })
@XmlRootElement(name = "ObtenerCFEPorIDResponse")
public class ObtenerCFEPorIDResponse {

    @XmlElementRef(name = "ObtenerCFEPorIDResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<SICFERespuestaObtenerCFE> obtenerCFEPorIDResult;

    /**
     * Gets the value of the obtenerCFEPorIDResult property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link SICFERespuestaObtenerCFE }{@code >}
     * 
     */
    public JAXBElement<SICFERespuestaObtenerCFE> getObtenerCFEPorIDResult() {
        return obtenerCFEPorIDResult;
    }

    /**
     * Sets the value of the obtenerCFEPorIDResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link SICFERespuestaObtenerCFE }{@code >}
     * 
     */
    public void setObtenerCFEPorIDResult(JAXBElement<SICFERespuestaObtenerCFE> value) {
        this.obtenerCFEPorIDResult = value;
    }

}
