
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaBuffer;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ObtenerPDFSinProcesarResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuestaBuffer" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "obtenerPDFSinProcesarResult" })
@XmlRootElement(name = "ObtenerPDFSinProcesarResponse")
public class ObtenerPDFSinProcesarResponse {

    @XmlElementRef(name = "ObtenerPDFSinProcesarResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<SICFERespuestaBuffer> obtenerPDFSinProcesarResult;

    /**
     * Gets the value of the obtenerPDFSinProcesarResult property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link SICFERespuestaBuffer }{@code >}
     * 
     */
    public JAXBElement<SICFERespuestaBuffer> getObtenerPDFSinProcesarResult() {
        return obtenerPDFSinProcesarResult;
    }

    /**
     * Sets the value of the obtenerPDFSinProcesarResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link SICFERespuestaBuffer }{@code >}
     * 
     */
    public void setObtenerPDFSinProcesarResult(JAXBElement<SICFERespuestaBuffer> value) {
        this.obtenerPDFSinProcesarResult = value;
    }

}
