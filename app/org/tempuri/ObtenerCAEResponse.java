
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.RespuestaObtenerCAE;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ObtenerCAEResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}RespuestaObtenerCAE" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "obtenerCAEResult" })
@XmlRootElement(name = "ObtenerCAEResponse")
public class ObtenerCAEResponse {

    @XmlElementRef(name = "ObtenerCAEResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<RespuestaObtenerCAE> obtenerCAEResult;

    /**
     * Gets the value of the obtenerCAEResult property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link RespuestaObtenerCAE }{@code >}
     * 
     */
    public JAXBElement<RespuestaObtenerCAE> getObtenerCAEResult() {
        return obtenerCAEResult;
    }

    /**
     * Sets the value of the obtenerCAEResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link RespuestaObtenerCAE }{@code >}
     * 
     */
    public void setObtenerCAEResult(JAXBElement<RespuestaObtenerCAE> value) {
        this.obtenerCAEResult = value;
    }

}
