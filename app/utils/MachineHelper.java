package utils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import models.Building;
import models.Card;
import models.Machine;
import models.MachineUse;
import models.Part.PartState;
import models.Unit;

public class MachineHelper {

    public static Date getLastMachineUpdateByCard(Card card) {
        if (card == null) {
            return null;
        }

        Unit unit = card.getUnit();
        if (unit == null) {
            return null;
        }

        Building building = unit.getBuilding();
        if (building == null) {
            return null;
        }

        List<Machine> machines = building
            .getMachines()
            .stream()
            .filter(x ->
                (x.getState() == PartState.ACTIVE || x.getState() == PartState.NEW) &&
                x.getSortIndex() != 0
            )
            .collect(Collectors.toList());

        Date lastAlive = null;

        for (Machine machine : machines) {
            if (
                machine.getLastAlive() != null &&
                (lastAlive == null || lastAlive.after(machine.getLastAlive()))
            ) {
                lastAlive = machine.getLastAlive();
            }

            if (machine.getPendingUses() > 0) {
                MachineUse lastMachineUse = MachineUse.getLastMachineUseByMachine(machine);
                if (
                    lastMachineUse != null &&
                    (lastAlive == null || lastAlive.after(lastMachineUse.getTimestamp()))
                ) {
                    lastAlive = lastMachineUse.getTimestamp();
                }
            }
        }

        return lastAlive;
    }
}
