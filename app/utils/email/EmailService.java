package utils.email;

import java.io.IOException;
import java.util.List;
import models.Attachment;
import org.apache.commons.mail.*;
import utils.ApplicationConfiguration;
import utils.StringHelper;

public class EmailService {

    private static final String RECIPIENTS_DELIMITER = ",";

    public enum AttachExtension {
        pdf,
        xls,
        xlsx,
        noOne,
    }

    public static void send(String to, String subject, String body)
        throws EmailException, IOException {
        sendInternal(to, subject, body, null, "", AttachExtension.noOne, null);
    }

    public static void sendAsync(String to, String subject, String body)
        throws EmailException, IOException {
        sendInternalAsync(to, subject, body, null, "", AttachExtension.noOne, null);
    }

    public static void send(
        String to,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension
    ) throws EmailException, IOException {
        sendInternal(to, subject, body, attach, attachName, extension, null);
    }

    public static void sendAsync(
        String to,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension
    ) throws EmailException, IOException {
        sendInternalAsync(to, subject, body, attach, attachName, extension, null);
    }

    public static void send(String to, String subject, String body, List<Attachment> attachments)
        throws EmailException, IOException {
        sendInternal(to, subject, body, null, "", AttachExtension.noOne, attachments);
    }

    public static void send(
        String to,
        FromProps from,
        String subject,
        String body,
        List<Attachment> attachments
    ) throws EmailException, IOException {
        sendInternal(to, from, subject, body, null, "", AttachExtension.noOne, attachments);
    }

    public static void sendAsync(
        String to,
        String subject,
        String body,
        List<Attachment> attachments
    ) throws EmailException, IOException {
        sendInternalAsync(to, subject, body, null, "", AttachExtension.noOne, attachments);
    }

    public static String doReplaces(String body) {
        return body
            .replace("plusIcon ", " .plusIcon")
            .replace("lessIcon ", " .lessIcon")
            .replace("isexpanded ", "#isexpanded")
            .replace(" checked ", ":checked~* ")
            .replace(" expandable ", ".expandable")
            .replace(" }}", " }")
            .replace(" ${{", " {");
    }

    /**
     * Get lis of recipients delimited by internal delimiter
     */
    public static String generateRecipientsList(List<String> recipients) {
        return String.join(RECIPIENTS_DELIMITER, recipients);
    }

    public static String generateRecipientsList(String... recipients) {
        return String.join(RECIPIENTS_DELIMITER, recipients);
    }

    private static void sendInternalAsync(
        String to,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension,
        List<Attachment> attachments
    ) {
        new Thread(
            (
                new Runnable() {
                    String _to;
                    String _subject;
                    String _body;
                    byte[] _attach;
                    String _attachName;
                    AttachExtension _extension;
                    List<Attachment> _attachments;

                    public Runnable init(
                        String to,
                        String subject,
                        String body,
                        byte[] attach,
                        String attachName,
                        AttachExtension extension,
                        List<Attachment> attachments
                    ) {
                        this._to = to;
                        this._subject = subject;
                        this._body = body;
                        this._attach = attach;
                        this._attachName = attachName;
                        this._extension = extension;
                        this._attachments = attachments;

                        return this;
                    }

                    public void run() {
                        try {
                            sendInternal(
                                this._to,
                                this._subject,
                                this._body,
                                this._attach,
                                this._attachName,
                                this._extension,
                                this._attachments
                            );
                        } catch (EmailException | IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            ).init(to, subject, body, attach, attachName, extension, attachments)
        )
            .start();
    }

    private static void sendInternal(
        String to,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension,
        List<Attachment> attachments
    ) throws EmailException, IOException {
        sendInternal(
            to,
            new LavomatFromProps(),
            subject,
            body,
            attach,
            attachName,
            extension,
            attachments
        );
    }

    private static void sendInternal(
        String to,
        FromProps fromProps,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension,
        List<Attachment> attachments
    ) throws EmailException, IOException {
        body = doReplaces(body);

        Boolean isProduction = ApplicationConfiguration.isProd();
        HtmlEmail email = new HtmlEmail();

        if (isProduction) {
            String[] tos = to.split(RECIPIENTS_DELIMITER);
            for (String recipient : tos) {
                email.addTo(recipient);
            }
        } else {
            String debugEmail = ApplicationConfiguration.getDebugEmail();
            if (!StringHelper.isBlank(debugEmail)) {
                email.addTo(debugEmail);
            } else {
                return;
            }
        }

        email.setSmtpPort(ApplicationConfiguration.getSMTPPort());
        email.setAuthenticator(
            new DefaultAuthenticator(
                ApplicationConfiguration.getSMTPUser(),
                ApplicationConfiguration.getSMTPPassword()
            )
        );
        // email.setDebug(!isProduction && ApplicationConfiguration.isDebugEmailEnabled());
        email.setDebug(true);
        email.setHostName(ApplicationConfiguration.getEmailHostname());
        email.setTLS(true);
        email.setSSL(true);
        email.setFrom(fromProps.EMAIL, fromProps.NAME);
        email.setHtmlMsg(body);

        String tag = isProduction ? "" : "[PRUEBA] ";
        email.setSubject(tag + subject);

        if (attach != null) {
            email.attach(
                new ByteArrayDataSource(attach, "application/" + extension),
                attachName + "." + extension,
                attachName,
                EmailAttachment.ATTACHMENT
            );
        }

        if (attachments != null) {
            for (Attachment attachment : attachments) {
                if (attachment.getPayload() != null) {
                    email.attach(
                        new ByteArrayDataSource(attachment.getPayload(), attachment.getMIMEType()),
                        attachment.getName() + "." + attachment.getExtension(),
                        attachment.getName(),
                        EmailAttachment.ATTACHMENT
                    );
                }
            }
        }

        email.send();
    }
}
