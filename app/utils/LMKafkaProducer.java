package utils;

import java.util.List;
import java.util.Properties;
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.serialization.LongSerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.json.JSONException;

public class LMKafkaProducer {

    private static final String TOPIC = "TutorialTopic";
    private static final String BOOTSTRAP_SERVERS = "18.236.68.114:9092";

    private static Producer<Long, String> createProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        props.put(ProducerConfig.CLIENT_ID_CONFIG, "KafkaExampleProducer");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, LongSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        return new KafkaProducer<>(props);
    }

    public static void runProducer(final int sendMessageCount) throws Exception {
        final Producer<Long, String> producer = createProducer();
        long time = System.currentTimeMillis();
        try {
            for (long index = time; index < time + sendMessageCount; index++) {
                final ProducerRecord<Long, String> record = new ProducerRecord<>(
                    TOPIC,
                    index,
                    "Hello Mom " + index
                );
                RecordMetadata metadata = producer.send(record).get();
                long elapsedTime = System.currentTimeMillis() - time;
                System.out.printf(
                    "sent record(key=%s value=%s) " + "meta(partition=%d, offset=%d) time=%d\n",
                    record.key(),
                    record.value(),
                    metadata.partition(),
                    metadata.offset(),
                    elapsedTime
                );
            }
        } catch (Exception e) {
            System.out.printf(e.getMessage());
        } finally {
            producer.flush();
            producer.close();
        }
    }
}
