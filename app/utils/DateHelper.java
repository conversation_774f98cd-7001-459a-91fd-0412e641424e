package utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

public abstract class DateHelper {

    private DateHelper() {}

    private static final int[] MERGED_DAYS = { 29, 30, 31 };

    private static final String[] MONTHS_NAMES = {
        "<PERSON>ero",
        "Febrero",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "Agosto",
        "Septiembre",
        "Octubre",
        "Noviem<PERSON>",
        "Diciembre",
    };

    public static final SimpleDateFormat utcDateFormat = new SimpleDateFormat(
        "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
    );

    public static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

    public static final DateTimeFormatter simpleMonthFormat = DateTimeFormat.forPattern("MM/yyyy");

    private static final DateTimeFormatter dateAndTimeSlashFormatter = DateTimeFormat.forPattern(
        "dd/MM/YYYY HH:mm"
    );

    public static final DateTimeFormatter dateAndTimeDashFormatter = DateTimeFormat.forPattern(
        "yyyy-MM-dd HH:mm"
    );

    public static final DateTimeFormatter dateAndTimeWithSecondsDashFormatter = DateTimeFormat.forPattern(
        "yyyy-MM-dd HH:mm:ss.S"
    );

    private static final DateTimeFormatter dateFormatter = DateTimeFormat.forPattern("dd/MM/YYYY");

    public static String printDateAndTime(DateTime dateTime) {
        return dateAndTimeSlashFormatter.print(dateTime);
    }

    public static String printDateAndTime(Date date) {
        return dateAndTimeSlashFormatter.print(new DateTime(date));
    }

    public static String printDate(DateTime dateTime) {
        return dateFormatter.print(dateTime);
    }

    public static String printMonth(DateTime dateTime) {
        return simpleMonthFormat.print(dateTime);
    }

    public static Date parseUTCDate(String dateString) throws ParseException {
        return utcDateFormat.parse(dateString);
    }

    public static boolean isBefore(Date date, Date other) {
        return date.toInstant().isBefore(other.toInstant());
    }

    /**
     * Compare if the {@code date} is between {@code startDate} and {@code endDate}
     * only taking into account the time.
     * It only considers the date if the {@code startDate} and {@code endDate} are
     * from
     * different years or months.
     */
    public static boolean isTimeBetween(Date date, Date startDate, Date endDate) {
        Calendar dateCalendar = Calendar.getInstance();
        dateCalendar.setTime(date);
        int dateHour = dateCalendar.get(Calendar.HOUR_OF_DAY);
        int dateMinute = dateCalendar.get(Calendar.MINUTE);

        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(startDate);
        int startHour = startCalendar.get(Calendar.HOUR_OF_DAY);
        int startMinute = startCalendar.get(Calendar.MINUTE);

        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);
        int endHour = endCalendar.get(Calendar.HOUR_OF_DAY);
        int endMinute = endCalendar.get(Calendar.MINUTE);

        // == Special time configuration CASE ==
        if (startHour == endHour && startMinute == endMinute) {
            return false;
        }

        // Si la hora de "endDate" es anterior a la de "startDate", consideramos que
        // "endDate"
        // corresponde al día siguiente al de "startDate"
        if (
            endHour < startHour ||
            (endHour == startHour && endMinute < startMinute) ||
            (endHour == startHour && endMinute == startMinute)
        ) {
            endHour += 24;
        }

        // Comparamos la hora de "date" con las de "startDate" y "endDate"
        int dateTotalSeconds = dateHour * 3600 + dateMinute * 60;
        int startTotalSeconds = startHour * 3600 + startMinute * 60;
        int endTotalSeconds = endHour * 3600 + endMinute * 60;

        return dateTotalSeconds >= startTotalSeconds && dateTotalSeconds <= endTotalSeconds;
    }

    public static Date setStartOfTheDay(Date date) {
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0);
        return date;
    }

    public static Date setEndOfTheDay(Date date) {
        date.setHours(23);
        date.setMinutes(59);
        date.setSeconds(59);
        return date;
    }

    public static String printUTCMinus3FromUTCDate(Date date) {
        DateTimeFormatter formatter = DateHelper.dateAndTimeWithSecondsDashFormatter.withZone(
            DateTimeZone.forID("America/Argentina/San_Luis")
        );

        return formatter.print(new DateTime(date));
    }

    public static DateTime getUruguayanCurrentDateTime() {
        return DateTime.now().minusHours(3);
    }

    /*
     * Since 29th, 30th and 31st day of months do not exists in every month, we
     * treat those as an "extension" of the 28th, and call them "merged day", so we
     * can guarantee every user gets their balance restored.
     */
    public static boolean isMergedDay(DateTime dateTime) {
        return Arrays.stream(MERGED_DAYS).anyMatch(day -> day == dateTime.getDayOfMonth());
    }

    public static String getMonthName(int month) {
        String monthName = "wrong";

        if (month >= 1 && month <= 12) {
            monthName = MONTHS_NAMES[month - 1];
        }
        return monthName;
    }
}
