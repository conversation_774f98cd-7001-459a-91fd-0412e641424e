package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.MachineModel;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class MachineModelSerializer {

    private MachineModelSerializer() {}

    public static JSONObject machineModelListToJson(List<MachineModel> machineModel, int level)
        throws APIException {
        JSONObject machineModelJson = new JSONObject();

        JSONArray machineModelListJson = new JSONArray();
        for (MachineModel model : machineModel) {
            machineModelListJson.put(machineModelToJson(model, level));
        }

        try {
            machineModelJson.put("machineModels", machineModelListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing machine model list : " + e.getMessage());
        }
        return machineModelJson;
    }

    public static JSONObject machineModelToJson(MachineModel machineModel, int level)
        throws APIException {
        JSONObject machineModelJson = new JSONObject();

        try {
            machineModelJson.put("id", machineModel.getId());
            machineModelJson.put("name", machineModel.getName());
            machineModelJson.put(
                "mp100",
                machineModel.getParameter() != null ? machineModel.getParameter().getMp100() : ""
            );
            machineModelJson.put(
                "mp500",
                machineModel.getParameter() != null ? machineModel.getParameter().getMp500() : ""
            );
            machineModelJson.put(
                "mp1200",
                machineModel.getParameter() != null ? machineModel.getParameter().getMp1200() : ""
            );
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing machine model : " + e.getMessage());
        }

        return machineModelJson;
    }
}
