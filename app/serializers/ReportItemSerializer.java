package serializers;

import domains.sale_notifier.serializers.ExternalSaleNotificationRecordSerializer;
import global.APIException;
import global.APIException.APIErrors;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import models.BillableItem;
import models.Machine;
import models.MachineUse;
import models.MachineUseResult;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import utils.TimeZoneUtils;

public class ReportItemSerializer {

    public static <T extends BillableItem> JSONObject itemListToJson(
        List<T> items,
        int level,
        Machine oldestKeepAliveMachine
    ) throws APIException {
        JSONObject itemsJson = new JSONObject();

        JSONArray itemListJson = new JSONArray();
        for (T i : items) {
            itemListJson.put(itemToJson(i, level));
        }

        try {
            itemsJson.put("items", itemListJson);
            if (oldestKeepAliveMachine != null && oldestKeepAliveMachine.getLastAlive() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date timeZonedOldestKeepAlive = TimeZoneUtils.getLocalDate(
                    oldestKeepAliveMachine.getLastAlive().getTime(),
                    true,
                    false
                );
                itemsJson.put("oldestKeepAlive", sdf.format(timeZonedOldestKeepAlive));
                itemsJson.put(
                    "serialNumber",
                    oldestKeepAliveMachine.getSerialNumber() != null
                        ? oldestKeepAliveMachine.getSerialNumber()
                        : "N/D"
                );
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing report list : " + e.getMessage());
        }
        return itemsJson;
    }

    public static <T extends BillableItem> JSONObject itemToJson(T item, int level)
        throws APIException {
        JSONObject useJson = new JSONObject();

        try {
            useJson.put("id", item.getId());
            useJson.put(
                "machine",
                item.getMachine() != null
                    ? MachineSerializer.machineToJson(item.getMachine(), level)
                    : JSONObject.NULL
            );
            useJson.put(
                "unit",
                item.getUnit() != null
                    ? UnitSerializer.unitToJson(item.getUnit(), level)
                    : JSONObject.NULL
            );
            useJson.put(
                "card",
                item.getCard() != null
                    ? CardSerializer.cardToJson(item.getCard(), level)
                    : JSONObject.NULL
            );

            SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdfTime = new SimpleDateFormat("HH:mm:ss");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            useJson.put("timestamp", sdf.format(item.getTimestamp()));
            useJson.put("date", sdfDate.format(item.getTimestamp()));
            useJson.put("time", sdfTime.format(item.getTimestamp()));
            useJson.put("concept", item.getHeadline());
            useJson.put("accredited", item.isAccredited());
            useJson.put("isMaster", item.getCard() != null && item.getCard().isMaster());
            useJson.put("reason", item.getReason());
            useJson.put("alert", item.isAlert());
            useJson.put(
                "result",
                item.getResult() != null && !item.getResult().equals("None")
                    ? MachineUseResult.getEnum(item.getResult()).getName()
                    : item.getResult() != null && item.getResult().equals("None") ? "None" : "N/A"
            );

            if (item instanceof MachineUse) {
                useJson.put("channel", ((MachineUse) item).getChannel());
            }

            useJson.put(
                "externalNotificationRecord",
                item.getLastExternalSaleNotificationRecord() != null
                    ? ExternalSaleNotificationRecordSerializer.externalSaleNotificationRecordToJSON(
                        item.getLastExternalSaleNotificationRecord()
                    )
                    : JSONObject.NULL
            );
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing machine use : " + e.getMessage());
        }

        return useJson;
    }
}
