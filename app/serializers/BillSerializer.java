package serializers;

import com.fasterxml.jackson.databind.JsonNode;
import domains.sale_notifier.serializers.ExternalSaleNotificationRecordSerializer;
import global.APIException;
import global.APIException.APIErrors;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import models.Bill;
import models.BillItem;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class BillSerializer {

    public static JSONObject billListToJson(List<Bill> bills, int level) throws APIException {
        JSONObject billsJson = new JSONObject();

        JSONArray billListJson = new JSONArray();
        for (Bill b : bills) {
            billListJson.put(billToJson(b, level));
        }

        try {
            billsJson.put("bills", billListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing bill list : " + e.getMessage());
        }
        return billsJson;
    }

    public static JSONObject billToJson(Bill bill, int level) throws APIException {
        JSONObject billJson = new JSONObject();
        SimpleDateFormat format = new SimpleDateFormat("dd-MM-yyyy");

        try {
            billJson.put("id", bill.getId());
            billJson.put("billTo", bill.getRecipientName());
            billJson.put("total", bill.getTotal());
            billJson.put("timestamp", bill.getTimestamp());
            billJson.put("state", bill.getState());
            billJson.put("serie", bill.getSerie());
            billJson.put("number", bill.getNumber());
            billJson.put("billType", bill.getBillType());
            billJson.put("billToType", bill.getBillToType());
            billJson.put(
                "billedPeriodStart",
                bill.getBilledPeriodStart() != null
                    ? bill.getBilledPeriodStart().getTime()
                    : JSONObject.NULL
            );
            billJson.put(
                "billedPeriodEnd",
                bill.getBilledPeriodEnd() != null
                    ? bill.getBilledPeriodEnd().getTime()
                    : JSONObject.NULL
            );
            billJson.put("isCreditNote", bill.isCreditNote());
            billJson.put("billCollectionStatus", bill.getBillCollectionStatus());
            billJson.put("hasCreditOrDebitNote", bill.hasCreditOrDebitNote());
            billJson.put("paymentReference", bill.getPaymentReference());
            billJson.put(
                "collector",
                bill.getDebtCollector() != null
                    ? bill.getDebtCollector().getFullName()
                    : JSONObject.NULL
            );
            billJson.put(
                "collectionDate",
                bill.getCollectionDate() != null
                    ? format.format(bill.getCollectionDate())
                    : JSONObject.NULL
            );
            billJson.put("billOrigin", bill.getBillOrigin());

            billJson.put(
                "externalNotification",
                bill.getLastExternalSaleNotificationRecord() != null
                    ? ExternalSaleNotificationRecordSerializer.externalSaleNotificationRecordToJSON(
                        bill.getLastExternalSaleNotificationRecord()
                    )
                    : JSONObject.NULL
            );
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing bill : " + e.getMessage());
        }

        return billJson;
    }

    public static JSONArray billItemsToJson(List<BillItem> items, int level) throws APIException {
        JSONArray itemsJson = new JSONArray();

        try {
            for (BillItem item : items) {
                JSONObject itemJson = new JSONObject();

                itemJson.put("itemName", item.getNomItem());
                itemJson.put("amount", item.getAmount());
                itemJson.put("unitPrice", item.getPrecioUnitario());
                itemJson.put("unit", item.getUnidadMedida());
                if (level == 2) itemJson.put("discreditedAmount", item.getDiscreditedAmount());

                itemsJson.put(itemJson);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing bill : " + e.getMessage());
        }

        return itemsJson;
    }

    public static List<BillItem> jsonToBillItems(JsonNode items, int level) throws APIException {
        List<BillItem> itemsList = new ArrayList<BillItem>();
        if (items == null) return itemsList;

        try {
            for (JsonNode item : items) {
                BillItem itemList = new BillItem();

                itemList.setItemName(
                    item.hasNonNull("itemName") ? item.get("itemName").asText() : ""
                );
                itemList.setAmount(item.hasNonNull("amount") ? item.get("amount").asInt() : 0);
                itemList.setItemUnitPrice(
                    item.hasNonNull("unitPrice") ? item.get("unitPrice").asDouble() : 0
                );
                itemList.setItemMeasureUnit(
                    item.hasNonNull("unit") ? item.get("unit").asText() : ""
                );
                itemList.setDiscreditedAmount(
                    item.hasNonNull("discreditedAmount") ? item.get("discreditedAmount").asInt() : 0
                );

                itemsList.add(itemList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing bill : " + e.getMessage());
        }

        return itemsList;
    }

    public static JSONArray stringItemsToJson(List<String> billTypes) throws APIException {
        JSONArray itemsJson = new JSONArray();

        try {
            for (String item : billTypes) itemsJson.put(item);
        } catch (Exception e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing bill : " + e.getMessage());
        }

        return itemsJson;
    }
}
