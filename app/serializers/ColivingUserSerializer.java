package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.*;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class ColivingUserSerializer {

    public static JSONObject userListToJson(List<User> users, int level) throws APIException {
        JSONObject usersJson = new JSONObject();

        JSONArray userListJson = new JSONArray();
        for (User user : users) {
            userListJson.put(userToJson(user, level));
        }

        try {
            usersJson.put("users", userListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing user list : " + e.getMessage());
        }
        return usersJson;
    }

    public static JSONObject userToJson(User user, int level) throws APIException {
        JSONObject userJson = new JSONObject();

        try {
            userJson.put("id", user.getId());
            userJson.put("firstname", user.getFirstName());
            userJson.put("lastname", user.getLastName());
            userJson.put("email", user.getEmailAddress());
            userJson.put(
                "reference",
                user.getUnit() != null ? user.getUnit().getNumber() : JSONObject.NULL
            );
            Account account = user.getMasterAccount();
            if (account != null) {
                userJson.put("createdAt", account.getCreationTimestamp().getTime());
                userJson.put(
                    "validatedAt",
                    account.getValidationDate() != null
                        ? account.getValidationDate().getTime()
                        : JSONObject.NULL
                );
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing user : " + e.getMessage());
        }

        return userJson;
    }
}
