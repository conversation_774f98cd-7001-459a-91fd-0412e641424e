package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.BrandingItem;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class BrandingItemSerializer {

    public static JSONObject itemListToJson(List<BrandingItem> items, int level)
        throws APIException {
        JSONObject itemsJson = new JSONObject();

        JSONArray itemsListJson = new JSONArray();
        for (BrandingItem b : items) {
            itemsListJson.put(itemToJson(b, level));
        }

        try {
            itemsJson.put("items", itemsListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing Branding Item list : " + e.getMessage());
        }
        return itemsJson;
    }

    public static JSONObject itemToJson(BrandingItem b, int level) throws APIException {
        JSONObject itemJson = new JSONObject();

        try {
            itemJson.put("id", b.getId());
            itemJson.put("name", b.getName());
            itemJson.put("description", b.getDescription());
            itemJson.put("price", b.getPriceCustomer());
            itemJson.put("reference", b.getReference());
            itemJson.put("imageName", b.getImageName());

            if (level >= 1) {
                itemJson.put("rateId", b.getRate().getId());
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing building : " + e.getMessage());
        }

        return itemJson;
    }
}
