package domains.reporting.builders;

import static utils.DateHelper.printDate;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;

public class PuntaShoppingMonthlyReportBuilder extends ReportBuilder {

    public byte[] processTemplate(
        String templatePath,
        String monthAndYear,
        List<Double> salesPerDay
    ) {
        try (
            InputStream fis = Files.newInputStream(Paths.get(templatePath));
            XSSFWorkbook workbook = new XSSFWorkbook(fis)
        ) {
            Sheet sheet = workbook.getSheetAt(0);

            processMetadataCells(sheet, monthAndYear);
            processCellsC18ToC49(sheet, salesPerDay);

            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                workbook.write(baos);
                return baos.toByteArray();
            } catch (IOException e) {
                play.Logger.error("Error writing workbook to byte array: {}", e.getMessage(), e);
            }
        } catch (IOException e) {
            play.Logger.error("Error reading template file: {}", e.getMessage(), e);
        }

        return null;
    }

    private void processMetadataCells(Sheet sheet, String monthAndYear) {
        // Date sent
        CellReference f9Ref = new CellReference("F9");
        Cell f9Cell = sheet.getRow(f9Ref.getRow()).getCell(f9Ref.getCol());
        f9Cell.setCellValue(printDate(DateTime.now()));

        // Month of Report
        CellReference g11Ref = new CellReference("G11");
        Cell g11Cell = sheet.getRow(g11Ref.getRow()).getCell(g11Ref.getCol());
        g11Cell.setCellValue(monthAndYear);
    }

    private void processCellsC18ToC49(Sheet sheet, List<Double> salesPerDay) {
        double total = 0d;
        // sales per day of month
        int i = 0;
        for (int row = 18; row <= 48; row++) {
            CellReference cellRef = new CellReference("C" + row);
            Cell cell = sheet.getRow(cellRef.getRow()).getCell(cellRef.getCol());

            if (i >= salesPerDay.size()) {
                cell.setCellValue(0d);
            } else {
                cell.setCellValue(salesPerDay.get(i));
                total += salesPerDay.get(i);
            }

            i++;
        }

        // total
        CellReference totalCellRef = new CellReference("C49");
        Cell cell = sheet.getRow(totalCellRef.getRow()).getCell(totalCellRef.getCol());
        cell.setCellValue(total);
    }
}
