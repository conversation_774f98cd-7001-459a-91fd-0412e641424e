package domains.sale_notifier.serializers;

import global.APIException;
import models.ExternalSaleNotificationRecord;
import org.json.JSONObject;

public class ExternalSaleNotificationRecordSerializer {

    public static JSONObject externalSaleNotificationRecordToJSON(
        ExternalSaleNotificationRecord externalSaleNotificationRecord
    ) throws APIException {
        JSONObject externalSaleNotificationJSON = new JSONObject();

        try {
            externalSaleNotificationJSON.put(
                "createdAt",
                externalSaleNotificationRecord.getCreatedAt()
            );
            externalSaleNotificationJSON.put(
                "building",
                externalSaleNotificationRecord.getBuilding().getName()
            );
            externalSaleNotificationJSON.put(
                "refundedAt",
                externalSaleNotificationRecord.getRefundedAt() != null
                    ? externalSaleNotificationRecord.getRefundedAt()
                    : JSONObject.NULL
            );
            externalSaleNotificationJSON.put(
                "externalId",
                externalSaleNotificationRecord.getExternalId()
            );
        } catch (Exception e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.BAD_JSON)
                .setDetailMessage(
                    "Error serializing external sale notification : " + e.getMessage()
                );
        }

        return externalSaleNotificationJSON;
    }
}
