package domains.sale_notifier.services.plaza_italia.soap;

import domains.sale_notifier.services.plaza_italia.soap.generated.refund.Entrada;
import domains.sale_notifier.services.wrappers.SaleWrapper;
import java.util.List;
import java.util.stream.Collectors;
import utils.ApplicationConfiguration;

public class PlazaItaliaRefundNotificationSOAPRequestBuilder {

    public List<Entrada.TAnulCFELocWS> buildSOAPRequest(List<SaleWrapper> salesRefunded) {
        return salesRefunded.stream().map(this::buildTAnulCFELocWS).collect(Collectors.toList());
    }

    private Entrada.TAnulCFELocWS buildTAnulCFELocWS(SaleWrapper saleWrapper) {
        Entrada.TAnulCFELocWS tAnulCFELocWSElement = new Entrada.TAnulCFELocWS();
        Entrada.TAnulCFELocWS.General generalElement = buildGeneralElement(saleWrapper);
        tAnulCFELocWSElement.setGeneral(generalElement);

        return tAnulCFELocWSElement;
    }

    private Entrada.TAnulCFELocWS.General buildGeneralElement(SaleWrapper saleWrapper) {
        Entrada.TAnulCFELocWS.General generalElement = new Entrada.TAnulCFELocWS.General();
        Entrada.TAnulCFELocWS.General.G1 g1Element = new Entrada.TAnulCFELocWS.General.G1();

        g1Element.setNumeroRUT(ApplicationConfiguration.getLavomatRUT());
        g1Element.setCodigoCFE(saleWrapper.getCodigoCFE());
        g1Element.setNumeroCFE(saleWrapper.getNumberoCFE());
        g1Element.setSerieCFE(saleWrapper.getSerieCFE());

        generalElement.setG1(g1Element);
        return generalElement;
    }
}
