package domains.sale_notifier.services.plaza_italia.soap.generated.refund;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>Java class for entrada complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="entrada">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="TAnulCFELocWS" maxOccurs="unbounded">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;all>
 *                   &lt;element name="General">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="G1">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;all>
 *                                       &lt;element name="NumeroRUT">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="20"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="CodigoCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                                       &lt;element name="NumeroCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                                       &lt;element name="SerieCFE">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="4"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                     &lt;/all>
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/all>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "entrada", propOrder = { "tAnulCFELocWS" })
public class Entrada {

    @XmlElement(name = "TAnulCFELocWS", required = true)
    protected List<Entrada.TAnulCFELocWS> tAnulCFELocWS;

    /**
     * Gets the value of the tAnulCFELocWS property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the tAnulCFELocWS property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTAnulCFELocWS().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Entrada.TAnulCFELocWS }
     *
     *
     */
    public List<Entrada.TAnulCFELocWS> getTAnulCFELocWS() {
        if (tAnulCFELocWS == null) {
            tAnulCFELocWS = new ArrayList<Entrada.TAnulCFELocWS>();
        }
        return this.tAnulCFELocWS;
    }

    /**
     * <p>Java class for anonymous complex type.
     *
     * <p>The following schema fragment specifies the expected content contained within this class.
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;all>
     *         &lt;element name="General">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="G1">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;all>
     *                             &lt;element name="NumeroRUT">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="20"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="CodigoCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *                             &lt;element name="NumeroCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *                             &lt;element name="SerieCFE">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="4"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                           &lt;/all>
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/all>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     *
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {})
    public static class TAnulCFELocWS {

        @XmlElement(name = "General", required = true)
        protected Entrada.TAnulCFELocWS.General general;

        /**
         * Gets the value of the general property.
         *
         * @return
         *     possible object is
         *     {@link Entrada.TAnulCFELocWS.General }
         *
         */
        public Entrada.TAnulCFELocWS.General getGeneral() {
            return general;
        }

        /**
         * Sets the value of the general property.
         *
         * @param value
         *     allowed object is
         *     {@link Entrada.TAnulCFELocWS.General }
         *
         */
        public void setGeneral(Entrada.TAnulCFELocWS.General value) {
            this.general = value;
        }

        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="G1">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;all>
         *                   &lt;element name="NumeroRUT">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="20"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="CodigoCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
         *                   &lt;element name="NumeroCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
         *                   &lt;element name="SerieCFE">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="4"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                 &lt;/all>
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = { "g1" })
        public static class General {

            @XmlElement(name = "G1", required = true)
            protected Entrada.TAnulCFELocWS.General.G1 g1;

            /**
             * Gets the value of the g1 property.
             *
             * @return
             *     possible object is
             *     {@link Entrada.TAnulCFELocWS.General.G1 }
             *
             */
            public Entrada.TAnulCFELocWS.General.G1 getG1() {
                return g1;
            }

            /**
             * Sets the value of the g1 property.
             *
             * @param value
             *     allowed object is
             *     {@link Entrada.TAnulCFELocWS.General.G1 }
             *
             */
            public void setG1(Entrada.TAnulCFELocWS.General.G1 value) {
                this.g1 = value;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;all>
             *         &lt;element name="NumeroRUT">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="20"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="CodigoCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
             *         &lt;element name="NumeroCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
             *         &lt;element name="SerieCFE">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="4"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *       &lt;/all>
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {})
            public static class G1 {

                @XmlElement(name = "NumeroRUT", required = true)
                protected String numeroRUT;

                @XmlElement(name = "CodigoCFE")
                protected int codigoCFE;

                @XmlElement(name = "NumeroCFE")
                protected int numeroCFE;

                @XmlElement(name = "SerieCFE", required = true)
                protected String serieCFE;

                /**
                 * Gets the value of the numeroRUT property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getNumeroRUT() {
                    return numeroRUT;
                }

                /**
                 * Sets the value of the numeroRUT property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setNumeroRUT(String value) {
                    this.numeroRUT = value;
                }

                /**
                 * Gets the value of the codigoCFE property.
                 *
                 */
                public int getCodigoCFE() {
                    return codigoCFE;
                }

                /**
                 * Sets the value of the codigoCFE property.
                 *
                 */
                public void setCodigoCFE(int value) {
                    this.codigoCFE = value;
                }

                /**
                 * Gets the value of the numeroCFE property.
                 *
                 */
                public int getNumeroCFE() {
                    return numeroCFE;
                }

                /**
                 * Sets the value of the numeroCFE property.
                 *
                 */
                public void setNumeroCFE(int value) {
                    this.numeroCFE = value;
                }

                /**
                 * Gets the value of the serieCFE property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getSerieCFE() {
                    return serieCFE;
                }

                /**
                 * Sets the value of the serieCFE property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setSerieCFE(String value) {
                    this.serieCFE = value;
                }
            }
        }
    }
}
