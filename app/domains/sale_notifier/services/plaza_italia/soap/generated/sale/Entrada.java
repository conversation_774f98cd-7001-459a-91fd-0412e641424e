package domains.sale_notifier.services.plaza_italia.soap.generated.sale;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>Java class for entrada complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="entrada">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="wsDeclaVtas" maxOccurs="unbounded">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;all>
 *                   &lt;element name="General">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;sequence>
 *                             &lt;element name="Cab">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;all>
 *                                       &lt;element name="NumeroRUT">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="20"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="CodigoShopping">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="15"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="NumeroContrato" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                                       &lt;element name="CodigoCanal">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="15"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="Caja" minOccurs="0">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="20"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="CodigoCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                                       &lt;element name="NumeroCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                                       &lt;element name="SerieCFE">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="20"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="DocIdCFE" minOccurs="0">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="20"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="MonedaCFE">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="3"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="FechaEmisionCFE">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="20"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="TotalMOCIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
 *                                       &lt;element name="TotalMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
 *                                       &lt;element name="ObsCab1" minOccurs="0">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="50"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="TipodeCambio" type="{http://www.w3.org/2001/XMLSchema}double"/>
 *                                       &lt;element name="ObsCab2" minOccurs="0">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="50"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                     &lt;/all>
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                             &lt;element name="Det" maxOccurs="unbounded">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;all>
 *                                       &lt;element name="CodRubro">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="15"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="ContadoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
 *                                       &lt;element name="CreditoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
 *                                       &lt;element name="DebitoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
 *                                       &lt;element name="IncluirenPromo">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="1"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="ObsLin1" minOccurs="0">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="50"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="ObsLin2" minOccurs="0">
 *                                         &lt;simpleType>
 *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                             &lt;maxLength value="50"/>
 *                                           &lt;/restriction>
 *                                         &lt;/simpleType>
 *                                       &lt;/element>
 *                                       &lt;element name="ValorVariableNumerico" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *                                     &lt;/all>
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                           &lt;/sequence>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/all>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "entrada", propOrder = { "wsDeclaVtas" })
public class Entrada {

    @XmlElement(required = true)
    protected List<Entrada.WsDeclaVtas> wsDeclaVtas;

    /**
     * Gets the value of the wsDeclaVtas property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the wsDeclaVtas property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getWsDeclaVtas().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Entrada.WsDeclaVtas }
     *
     *
     */
    public List<Entrada.WsDeclaVtas> getWsDeclaVtas() {
        if (wsDeclaVtas == null) {
            wsDeclaVtas = new ArrayList<Entrada.WsDeclaVtas>();
        }
        return this.wsDeclaVtas;
    }

    /**
     * <p>Java class for anonymous complex type.
     *
     * <p>The following schema fragment specifies the expected content contained within this class.
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;all>
     *         &lt;element name="General">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;sequence>
     *                   &lt;element name="Cab">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;all>
     *                             &lt;element name="NumeroRUT">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="20"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="CodigoShopping">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="15"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="NumeroContrato" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *                             &lt;element name="CodigoCanal">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="15"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="Caja" minOccurs="0">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="20"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="CodigoCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *                             &lt;element name="NumeroCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *                             &lt;element name="SerieCFE">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="20"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="DocIdCFE" minOccurs="0">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="20"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="MonedaCFE">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="3"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="FechaEmisionCFE">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="20"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="TotalMOCIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
     *                             &lt;element name="TotalMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
     *                             &lt;element name="ObsCab1" minOccurs="0">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="50"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="TipodeCambio" type="{http://www.w3.org/2001/XMLSchema}double"/>
     *                             &lt;element name="ObsCab2" minOccurs="0">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="50"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                           &lt;/all>
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                   &lt;element name="Det" maxOccurs="unbounded">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;all>
     *                             &lt;element name="CodRubro">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="15"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="ContadoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
     *                             &lt;element name="CreditoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
     *                             &lt;element name="DebitoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
     *                             &lt;element name="IncluirenPromo">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="1"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="ObsLin1" minOccurs="0">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="50"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="ObsLin2" minOccurs="0">
     *                               &lt;simpleType>
     *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                   &lt;maxLength value="50"/>
     *                                 &lt;/restriction>
     *                               &lt;/simpleType>
     *                             &lt;/element>
     *                             &lt;element name="ValorVariableNumerico" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
     *                           &lt;/all>
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                 &lt;/sequence>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/all>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     *
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {})
    public static class WsDeclaVtas {

        @XmlElement(name = "General", required = true)
        protected Entrada.WsDeclaVtas.General general;

        /**
         * Gets the value of the general property.
         *
         * @return
         *     possible object is
         *     {@link Entrada.WsDeclaVtas.General }
         *
         */
        public Entrada.WsDeclaVtas.General getGeneral() {
            return general;
        }

        /**
         * Sets the value of the general property.
         *
         * @param value
         *     allowed object is
         *     {@link Entrada.WsDeclaVtas.General }
         *
         */
        public void setGeneral(Entrada.WsDeclaVtas.General value) {
            this.general = value;
        }

        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;sequence>
         *         &lt;element name="Cab">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;all>
         *                   &lt;element name="NumeroRUT">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="20"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="CodigoShopping">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="15"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="NumeroContrato" type="{http://www.w3.org/2001/XMLSchema}int"/>
         *                   &lt;element name="CodigoCanal">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="15"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="Caja" minOccurs="0">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="20"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="CodigoCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
         *                   &lt;element name="NumeroCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
         *                   &lt;element name="SerieCFE">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="20"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="DocIdCFE" minOccurs="0">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="20"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="MonedaCFE">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="3"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="FechaEmisionCFE">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="20"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="TotalMOCIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
         *                   &lt;element name="TotalMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
         *                   &lt;element name="ObsCab1" minOccurs="0">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="50"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="TipodeCambio" type="{http://www.w3.org/2001/XMLSchema}double"/>
         *                   &lt;element name="ObsCab2" minOccurs="0">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="50"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                 &lt;/all>
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *         &lt;element name="Det" maxOccurs="unbounded">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;all>
         *                   &lt;element name="CodRubro">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="15"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="ContadoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
         *                   &lt;element name="CreditoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
         *                   &lt;element name="DebitoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
         *                   &lt;element name="IncluirenPromo">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="1"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="ObsLin1" minOccurs="0">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="50"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="ObsLin2" minOccurs="0">
         *                     &lt;simpleType>
         *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                         &lt;maxLength value="50"/>
         *                       &lt;/restriction>
         *                     &lt;/simpleType>
         *                   &lt;/element>
         *                   &lt;element name="ValorVariableNumerico" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
         *                 &lt;/all>
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *       &lt;/sequence>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = { "cab", "det" })
        public static class General {

            @XmlElement(name = "Cab", required = true)
            protected Entrada.WsDeclaVtas.General.Cab cab;

            @XmlElement(name = "Det", required = true)
            protected List<Entrada.WsDeclaVtas.General.Det> det;

            /**
             * Gets the value of the cab property.
             *
             * @return
             *     possible object is
             *     {@link Entrada.WsDeclaVtas.General.Cab }
             *
             */
            public Entrada.WsDeclaVtas.General.Cab getCab() {
                return cab;
            }

            /**
             * Sets the value of the cab property.
             *
             * @param value
             *     allowed object is
             *     {@link Entrada.WsDeclaVtas.General.Cab }
             *
             */
            public void setCab(Entrada.WsDeclaVtas.General.Cab value) {
                this.cab = value;
            }

            /**
             * Gets the value of the det property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the JAXB object.
             * This is why there is not a <CODE>set</CODE> method for the det property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getDet().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link Entrada.WsDeclaVtas.General.Det }
             *
             *
             */
            public List<Entrada.WsDeclaVtas.General.Det> getDet() {
                if (det == null) {
                    det = new ArrayList<Entrada.WsDeclaVtas.General.Det>();
                }
                return this.det;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;all>
             *         &lt;element name="NumeroRUT">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="20"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="CodigoShopping">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="15"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="NumeroContrato" type="{http://www.w3.org/2001/XMLSchema}int"/>
             *         &lt;element name="CodigoCanal">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="15"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="Caja" minOccurs="0">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="20"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="CodigoCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
             *         &lt;element name="NumeroCFE" type="{http://www.w3.org/2001/XMLSchema}int"/>
             *         &lt;element name="SerieCFE">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="20"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="DocIdCFE" minOccurs="0">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="20"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="MonedaCFE">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="3"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="FechaEmisionCFE">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="20"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="TotalMOCIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
             *         &lt;element name="TotalMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
             *         &lt;element name="ObsCab1" minOccurs="0">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="50"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="TipodeCambio" type="{http://www.w3.org/2001/XMLSchema}double"/>
             *         &lt;element name="ObsCab2" minOccurs="0">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="50"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *       &lt;/all>
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {})
            public static class Cab {

                @XmlElement(name = "NumeroRUT", required = true)
                protected String numeroRUT;

                @XmlElement(name = "CodigoShopping", required = true)
                protected String codigoShopping;

                @XmlElement(name = "NumeroContrato")
                protected int numeroContrato;

                @XmlElement(name = "CodigoCanal", required = true)
                protected String codigoCanal;

                @XmlElement(name = "Caja")
                protected String caja;

                @XmlElement(name = "CodigoCFE")
                protected int codigoCFE;

                @XmlElement(name = "NumeroCFE")
                protected int numeroCFE;

                @XmlElement(name = "SerieCFE", required = true)
                protected String serieCFE;

                @XmlElement(name = "DocIdCFE")
                protected String docIdCFE;

                @XmlElement(name = "MonedaCFE", required = true)
                protected String monedaCFE;

                @XmlElement(name = "FechaEmisionCFE", required = true)
                protected String fechaEmisionCFE;

                @XmlElement(name = "TotalMOCIVA")
                protected double totalMOCIVA;

                @XmlElement(name = "TotalMNSIVA")
                protected double totalMNSIVA;

                @XmlElement(name = "ObsCab1")
                protected String obsCab1;

                @XmlElement(name = "TipodeCambio")
                protected double tipodeCambio;

                @XmlElement(name = "ObsCab2")
                protected String obsCab2;

                /**
                 * Gets the value of the numeroRUT property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getNumeroRUT() {
                    return numeroRUT;
                }

                /**
                 * Sets the value of the numeroRUT property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setNumeroRUT(String value) {
                    this.numeroRUT = value;
                }

                /**
                 * Gets the value of the codigoShopping property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getCodigoShopping() {
                    return codigoShopping;
                }

                /**
                 * Sets the value of the codigoShopping property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setCodigoShopping(String value) {
                    this.codigoShopping = value;
                }

                /**
                 * Gets the value of the numeroContrato property.
                 *
                 */
                public int getNumeroContrato() {
                    return numeroContrato;
                }

                /**
                 * Sets the value of the numeroContrato property.
                 *
                 */
                public void setNumeroContrato(int value) {
                    this.numeroContrato = value;
                }

                /**
                 * Gets the value of the codigoCanal property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getCodigoCanal() {
                    return codigoCanal;
                }

                /**
                 * Sets the value of the codigoCanal property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setCodigoCanal(String value) {
                    this.codigoCanal = value;
                }

                /**
                 * Gets the value of the caja property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getCaja() {
                    return caja;
                }

                /**
                 * Sets the value of the caja property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setCaja(String value) {
                    this.caja = value;
                }

                /**
                 * Gets the value of the codigoCFE property.
                 *
                 */
                public int getCodigoCFE() {
                    return codigoCFE;
                }

                /**
                 * Sets the value of the codigoCFE property.
                 *
                 */
                public void setCodigoCFE(int value) {
                    this.codigoCFE = value;
                }

                /**
                 * Gets the value of the numeroCFE property.
                 *
                 */
                public int getNumeroCFE() {
                    return numeroCFE;
                }

                /**
                 * Sets the value of the numeroCFE property.
                 *
                 */
                public void setNumeroCFE(int value) {
                    this.numeroCFE = value;
                }

                /**
                 * Gets the value of the serieCFE property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getSerieCFE() {
                    return serieCFE;
                }

                /**
                 * Sets the value of the serieCFE property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setSerieCFE(String value) {
                    this.serieCFE = value;
                }

                /**
                 * Gets the value of the docIdCFE property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getDocIdCFE() {
                    return docIdCFE;
                }

                /**
                 * Sets the value of the docIdCFE property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setDocIdCFE(String value) {
                    this.docIdCFE = value;
                }

                /**
                 * Gets the value of the monedaCFE property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getMonedaCFE() {
                    return monedaCFE;
                }

                /**
                 * Sets the value of the monedaCFE property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setMonedaCFE(String value) {
                    this.monedaCFE = value;
                }

                /**
                 * Gets the value of the fechaEmisionCFE property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getFechaEmisionCFE() {
                    return fechaEmisionCFE;
                }

                /**
                 * Sets the value of the fechaEmisionCFE property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setFechaEmisionCFE(String value) {
                    this.fechaEmisionCFE = value;
                }

                /**
                 * Gets the value of the totalMOCIVA property.
                 *
                 */
                public double getTotalMOCIVA() {
                    return totalMOCIVA;
                }

                /**
                 * Sets the value of the totalMOCIVA property.
                 *
                 */
                public void setTotalMOCIVA(double value) {
                    this.totalMOCIVA = value;
                }

                /**
                 * Gets the value of the totalMNSIVA property.
                 *
                 */
                public double getTotalMNSIVA() {
                    return totalMNSIVA;
                }

                /**
                 * Sets the value of the totalMNSIVA property.
                 *
                 */
                public void setTotalMNSIVA(double value) {
                    this.totalMNSIVA = value;
                }

                /**
                 * Gets the value of the obsCab1 property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getObsCab1() {
                    return obsCab1;
                }

                /**
                 * Sets the value of the obsCab1 property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setObsCab1(String value) {
                    this.obsCab1 = value;
                }

                /**
                 * Gets the value of the tipodeCambio property.
                 *
                 */
                public double getTipodeCambio() {
                    return tipodeCambio;
                }

                /**
                 * Sets the value of the tipodeCambio property.
                 *
                 */
                public void setTipodeCambio(double value) {
                    this.tipodeCambio = value;
                }

                /**
                 * Gets the value of the obsCab2 property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getObsCab2() {
                    return obsCab2;
                }

                /**
                 * Sets the value of the obsCab2 property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setObsCab2(String value) {
                    this.obsCab2 = value;
                }
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;all>
             *         &lt;element name="CodRubro">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="15"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="ContadoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
             *         &lt;element name="CreditoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
             *         &lt;element name="DebitoMNSIVA" type="{http://www.w3.org/2001/XMLSchema}double"/>
             *         &lt;element name="IncluirenPromo">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="1"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="ObsLin1" minOccurs="0">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="50"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="ObsLin2" minOccurs="0">
             *           &lt;simpleType>
             *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *               &lt;maxLength value="50"/>
             *             &lt;/restriction>
             *           &lt;/simpleType>
             *         &lt;/element>
             *         &lt;element name="ValorVariableNumerico" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
             *       &lt;/all>
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {})
            public static class Det {

                @XmlElement(name = "CodRubro", required = true)
                protected String codRubro;

                @XmlElement(name = "ContadoMNSIVA")
                protected double contadoMNSIVA;

                @XmlElement(name = "CreditoMNSIVA")
                protected double creditoMNSIVA;

                @XmlElement(name = "DebitoMNSIVA")
                protected double debitoMNSIVA;

                @XmlElement(name = "IncluirenPromo", required = true)
                protected String incluirenPromo;

                @XmlElement(name = "ObsLin1")
                protected String obsLin1;

                @XmlElement(name = "ObsLin2")
                protected String obsLin2;

                @XmlElement(name = "ValorVariableNumerico")
                protected Double valorVariableNumerico;

                /**
                 * Gets the value of the codRubro property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getCodRubro() {
                    return codRubro;
                }

                /**
                 * Sets the value of the codRubro property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setCodRubro(String value) {
                    this.codRubro = value;
                }

                /**
                 * Gets the value of the contadoMNSIVA property.
                 *
                 */
                public double getContadoMNSIVA() {
                    return contadoMNSIVA;
                }

                /**
                 * Sets the value of the contadoMNSIVA property.
                 *
                 */
                public void setContadoMNSIVA(double value) {
                    this.contadoMNSIVA = value;
                }

                /**
                 * Gets the value of the creditoMNSIVA property.
                 *
                 */
                public double getCreditoMNSIVA() {
                    return creditoMNSIVA;
                }

                /**
                 * Sets the value of the creditoMNSIVA property.
                 *
                 */
                public void setCreditoMNSIVA(double value) {
                    this.creditoMNSIVA = value;
                }

                /**
                 * Gets the value of the debitoMNSIVA property.
                 *
                 */
                public double getDebitoMNSIVA() {
                    return debitoMNSIVA;
                }

                /**
                 * Sets the value of the debitoMNSIVA property.
                 *
                 */
                public void setDebitoMNSIVA(double value) {
                    this.debitoMNSIVA = value;
                }

                /**
                 * Gets the value of the incluirenPromo property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getIncluirenPromo() {
                    return incluirenPromo;
                }

                /**
                 * Sets the value of the incluirenPromo property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setIncluirenPromo(String value) {
                    this.incluirenPromo = value;
                }

                /**
                 * Gets the value of the obsLin1 property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getObsLin1() {
                    return obsLin1;
                }

                /**
                 * Sets the value of the obsLin1 property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setObsLin1(String value) {
                    this.obsLin1 = value;
                }

                /**
                 * Gets the value of the obsLin2 property.
                 *
                 * @return
                 *     possible object is
                 *     {@link String }
                 *
                 */
                public String getObsLin2() {
                    return obsLin2;
                }

                /**
                 * Sets the value of the obsLin2 property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link String }
                 *
                 */
                public void setObsLin2(String value) {
                    this.obsLin2 = value;
                }

                /**
                 * Gets the value of the valorVariableNumerico property.
                 *
                 * @return
                 *     possible object is
                 *     {@link Double }
                 *
                 */
                public Double getValorVariableNumerico() {
                    return valorVariableNumerico;
                }

                /**
                 * Sets the value of the valorVariableNumerico property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link Double }
                 *
                 */
                public void setValorVariableNumerico(Double value) {
                    this.valorVariableNumerico = value;
                }
            }
        }
    }
}
