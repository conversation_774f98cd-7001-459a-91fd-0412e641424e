package domains.auth.services.users;

import models.Account;
import models.User;
import services.accounts.AccountFactory;

public class ResetAccountValidationService extends services.BaseService {

    private User user;

    public ResetAccountValidationService(User user) {
        this.user = user;
    }

    public User getUser() {
        return user;
    }

    public String resetAccountValidation() {
        Account account = new AccountFactory(this.user.getMasterAccount())
            .waitForValidation()
            .update();
        return account.getValidationKey();
    }
}
