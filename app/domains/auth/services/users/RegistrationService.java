package domains.auth.services.users;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import models.Account;
import models.User;
import org.apache.commons.mail.EmailException;
import services.ContactSupportService;
import services.accounts.AccountFactory;
import services.card.CardFactoryService;
import utils.email.EmailService;
import views.html.generalMessage;

public class RegistrationService extends services.BaseService {

    protected User user;
    protected String accountValidationKey;

    public RegistrationService(User user) {
        this.user = user;
    }

    public RegistrationService(User user, String accountValidationKey) {
        this(user);
        this.accountValidationKey = accountValidationKey;
    }

    public void register() {
        user.save();

        createAccount();

        createVirtualCard();
    }

    public User getUser() {
        return user;
    }

    public Map<String, Object> sendValidationEmail(String server, String sessionToken) {
        String link =
            "https://" +
            server +
            "/api/v1/validateAccount?param1=" +
            user.getId() +
            "&param2=" +
            accountValidationKey;
        String message =
            "Para completar el proceso de registración por favor utilice el siguiente link para validar la cuenta: " +
            link;

        String body = generalMessage.render(message, user.getEmailAddress()).body();

        String recipients = EmailService.generateRecipientsList(
            user.getEmailAddress(),
            ContactSupportService.APP_USERS_BACKUP_EMAIL
        );

        Map<String, Object> response = new HashMap<>();
        try {
            EmailService.send(recipients, "LAVOMAT - Validación de Cuenta", body);

            response.put("token", sessionToken);
        } catch (EmailException | IOException e) {
            loggerError("Error while sending email", e.getMessage());

            response.put("emailSent", "ERROR");
        }

        return response;
    }

    private void createAccount() {
        Account newAccount = new AccountFactory().build(this.user).waitForValidation().create();
        this.accountValidationKey = newAccount.getValidationKey();
    }

    private void createVirtualCard() {
        new CardFactoryService().createRegularUserVirtualCard(this.user);
    }
}
