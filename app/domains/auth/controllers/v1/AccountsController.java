package domains.auth.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import controllers.AbstractController;
import domains.auth.dto.accounts.*;
import domains.auth.serializers.AccountSerializer;
import domains.auth.services.sessions.SessionCreationService;
import domains.auth.services.users.RegistrationService;
import domains.auth.services.users.ResetAccountValidationService;
import domains.auth.views.html.accountValidation;
import global.APIException;
import global.ErrorMessage;
import models.Account;
import org.apache.commons.lang3.StringUtils;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import policies.AccountPolicy;
import policies.ApiClient;
import policies.actions.Policy;
import queries.accounts.AccountQuery;
import security.v1.Secured;
import views.html.errorMessage;

@ErrorMessage
@global.LoggingMessage
public class AccountsController extends AbstractController {

    @Tx(readOnly = true)
    @Security.Authenticated(Secured.class)
    @Policy(AccountPolicy.class)
    public Promise<Result> getAccount() throws APIException {
        AccountPolicy allowedPolicy = this.getAllowedPolicy();
        AccountQuery query = allowedPolicy.get();

        return json(
            AccountSerializer.itemToJson(query.single(), allowedPolicy.level(ApiClient.BACKOFFICE))
        );
    }

    @Tx
    public Result validateAccount(String ownerId, String validationKey) throws APIException {
        try {
            ValidateAccountParameters params = new ValidateAccountParameters(ownerId, validationKey)
                .validate();

            params.getAccount().validate();

            return ok(
                accountValidation.render(
                    params.getUser().getFirstName(),
                    params.getUser().getEmailAddress()
                )
            );
        } catch (APIException ex) {
            if (
                ex.isError(APIException.APIErrors.USER_NOT_FOUND) ||
                ex.isError(APIException.APIErrors.ACCOUNT_NOT_FOUND) ||
                ex.isError(APIException.APIErrors.ACCOUNT_ALREADY_VALIDATED) ||
                ex.isError(APIException.APIErrors.VALIDATION_KEYS_DO_NOT_MATCH)
            ) {
                return ok(errorMessage.render(StringUtils.EMPTY, ex.getDetailMessage()));
            }

            throw ex;
        }
    }

    @Tx
    @Security.Authenticated(Secured.class)
    @Policy(AccountPolicy.class)
    public Promise<Result> resendValidationEmail(final int accountId) throws APIException {
        AccountPolicy allowedPolicy = this.getAllowedPolicy();
        AccountQuery query = allowedPolicy.update(accountId);
        Account account = query.single();

        new ResendAccountValidationEmail(account).validate();

        ResetAccountValidationService resetAccountValidationService = new ResetAccountValidationService(
            account.getOwner()
        );
        String accountValidationKey = resetAccountValidationService.resetAccountValidation();

        String sessionToken = new SessionCreationService(
            resetAccountValidationService.getUser(),
            request()
        )
            .generateToken();

        new RegistrationService(account.getOwner(), accountValidationKey)
            .sendValidationEmail(request().host(), sessionToken);

        return Promise.<Result>pure(noContent());
    }
}
