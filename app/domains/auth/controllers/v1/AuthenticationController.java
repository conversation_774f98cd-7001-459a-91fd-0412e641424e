package domains.auth.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import controllers.AbstractController;
import domains.auth.dto.users.*;
import domains.auth.serializers.SessionSerializer;
import domains.auth.services.sessions.SessionCreationService;
import domains.auth.services.sessions.SessionDestroyService;
import domains.auth.services.users.RegistrationService;
import global.APIException;
import global.ErrorMessage;
import java.io.IOException;
import java.util.Map;
import javax.inject.Inject;
import org.json.JSONException;
import play.i18n.Messages;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import security.v1.Secured;
import serializers.HashSerializer;
import services.google.GoogleReCaptchaService;

@ErrorMessage
@global.LoggingMessage
public class AuthenticationController extends AbstractController {

    @Inject
    GoogleReCaptchaService reCaptchaService;

    @Tx
    public Promise<Result> signIn() throws APIException {
        SignInParameters params = new SignInParameters(body()).validate();

        SessionCreationService service = new SessionCreationService(params.getUser(), request());

        return json(SessionSerializer.itemToJson(service.getSession(), 1));
    }

    @Tx
    public Promise<Result> signInWithRecaptcha() throws APIException {
        SignInWithRecaptchaParameters params = new SignInWithRecaptchaParameters(body(), request())
            .validate();

        boolean reCaptchaValid =
            this.reCaptchaService.validate(
                    params.getRecaptchaTokenParam(),
                    params.getUser().getEmailAddress(),
                    params.getClient()
                );

        if (!reCaptchaValid) {
            throw APIException
                .raise(APIException.APIErrors.UNAUTHORIZED)
                .setDetailMessage(Messages.get("RECAPTCHA_FAILED"));
        }

        SessionCreationService service = new SessionCreationService(params.getUser(), request());

        return json(SessionSerializer.itemToJson(service.getSession(), 1));
    }

    @Tx
    public Promise<Result> signInBuildingAdminWithRecaptcha()
        throws APIException, IOException, JSONException {
        SignInBuildingAdminWithRecaptchaParameters params = new SignInBuildingAdminWithRecaptchaParameters(
            body(),
            request()
        )
            .validate();

        boolean reCaptchaValid = reCaptchaService.validate(
            params.getRecaptchaTokenParam(),
            params.getUser().getEmailAddress(),
            params.getClient()
        );

        if (!reCaptchaValid) {
            throw APIException
                .raise(APIException.APIErrors.UNAUTHORIZED)
                .setDetailMessage(Messages.get("RECAPTCHA_FAILED"));
        }

        SessionCreationService service = new SessionCreationService(params.getUser(), request());

        return json(SessionSerializer.itemToJson(service.getSession(), 1));
    }

    @Tx
    public Promise<Result> signUp() throws APIException {
        SignUpParameters params = new SignUpParameters(body()).validate();

        RegistrationService service = new RegistrationService(params.getUser());
        service.register();

        String sessionToken = new SessionCreationService(service.getUser(), request())
            .generateToken();

        Map<String, Object> hash = service.sendValidationEmail(request().host(), sessionToken);

        return json(HashSerializer.hashToJson(hash));
    }

    @Tx
    @Security.Authenticated(Secured.class)
    public Promise<Result> signOut() throws APIException {
        SignOutParameters params = new SignOutParameters(queryString()).validate();

        SessionDestroyService service;
        if (params.hasToSignOutAllDevices()) {
            service = new SessionDestroyService(this.getContextUser());

            service.destroyAll();
        } else {
            service = new SessionDestroyService(this.getContextSession());

            service.destroyCurrent();
        }

        return Promise.pure(noContent());
    }
}
