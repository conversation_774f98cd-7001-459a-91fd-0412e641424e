package domains.auth.serializers;

import global.APIException;
import models.Session;
import org.json.JSONException;
import org.json.JSONObject;

public class SessionSerializer {

    public static JSONObject itemToJson(Session session, int level) throws APIException {
        JSONObject json = new JSONObject();
        try {
            json.put("token", session.getToken());

            if (level >= 1) {
                JSONObject accountJson = new JSONObject();

                int userLevel = 1;

                assert level >= userLevel;

                accountJson.put("user", UserSerializer.itemToJson(session.getUser(), userLevel));

                json.put("account", accountJson);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing auth session : " + e.getMessage());
        }

        return json;
    }
}
