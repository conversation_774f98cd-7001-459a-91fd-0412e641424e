package domains.auth.dto.accounts;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import java.io.IOException;
import java.net.URLDecoder;
import models.Account;
import models.User;
import org.apache.commons.lang3.StringUtils;
import queries.users.UserQuery;

public class ConfirmResetPasswordParameters extends dto.JsonBodyActionParameters {

    protected static final String USER_EMAIL_PARAM = "email";
    protected static final String RESET_KEY_PARAM = "resetKey";
    protected static final String PASSWORD_PARAM = "password";
    protected static final String CONFIRM_PASSWORD_PARAM = "confirmPassword";

    private String email;
    private final String confirmPassword;
    private final String resetKey;

    protected String password;
    protected Account account;
    protected User user;

    public ConfirmResetPasswordParameters(JsonNode body) {
        this.email = safeString(USER_EMAIL_PARAM, body, StringUtils.EMPTY);
        this.resetKey = safeString(RESET_KEY_PARAM, body, StringUtils.EMPTY);
        this.password = safeString(PASSWORD_PARAM, body, StringUtils.EMPTY);
        this.confirmPassword = safeString(CONFIRM_PASSWORD_PARAM, body, StringUtils.EMPTY);
    }

    @Override
    public ConfirmResetPasswordParameters validate() throws APIException {
        try {
            this.email = URLDecoder.decode(this.email, "UTF-8");
        } catch (IOException e) {
            throw APIException.raise(APIException.APIErrors.MISSING_PARAMETERS);
        }

        this.user = new UserQuery().filterByEmail(this.email).single();
        if (
            StringUtils.isBlank(this.resetKey) ||
            this.user == null ||
            this.user.getMasterAccount() == null
        ) {
            throw APIException.raise(APIException.APIErrors.MISSING_PARAMETERS);
        }

        this.account = this.user.getMasterAccount();
        if (!this.account.isWaitingForReset()) {
            throw APIException.raise(APIException.APIErrors.PASSWORD_NOT_WAITING_FOR_RESET);
        }

        if (!this.account.getResetKey().equals(this.resetKey)) {
            throw APIException.raise(APIException.APIErrors.RESET_KEYS_DO_NOT_MATCH);
        }

        if (!this.password.equals(this.confirmPassword)) {
            throw APIException.raise(APIException.APIErrors.PASSWORDS_DONT_MATCH);
        }

        return this;
    }

    public String getPassword() {
        return this.password;
    }

    public Account getAccount() {
        return this.account;
    }

    public User getUser() {
        return this.user;
    }
}
