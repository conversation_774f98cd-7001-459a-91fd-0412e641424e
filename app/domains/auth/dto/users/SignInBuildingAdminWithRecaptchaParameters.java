package domains.auth.dto.users;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.Role;
import play.i18n.Messages;
import play.mvc.Http;

public class SignInBuildingAdminWithRecaptchaParameters extends SignInWithRecaptchaParameters {

    public SignInBuildingAdminWithRecaptchaParameters(JsonNode body, Http.Request request)
        throws APIException {
        super(body, request);
    }

    @Override
    public SignInBuildingAdminWithRecaptchaParameters validate() throws APIException {
        super.validate();

        if (!this.user.getRole().match(Role.BUILDING_ADM)) {
            throw APIException
                .raise(APIException.APIErrors.UNAUTHORIZED)
                .setDetailMessage(Messages.get("invalid_credentials"));
        }

        return this;
    }
}
