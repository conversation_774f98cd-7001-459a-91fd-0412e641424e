package domains.auth.dto.users;

import com.fasterxml.jackson.databind.JsonNode;
import controllers.public_site.v1.routes;
import global.APIException;
import models.Role;
import org.apache.commons.lang3.StringUtils;
import play.i18n.Messages;
import play.mvc.Http;
import policies.ApiClient;

public class SignInWithRecaptchaParameters extends SignInParameters {

    protected static final String CLIENT_RECAPTCHA_RESPONSE_PARAM = "recaptchaToken";

    protected String recaptchaToken;
    protected String path;

    protected ApiClient client;

    public SignInWithRecaptchaParameters(JsonNode body, Http.Request request) throws APIException {
        super(body);
        this.recaptchaToken = safeString(CLIENT_RECAPTCHA_RESPONSE_PARAM, body, StringUtils.EMPTY);
        this.path = request != null ? request.path() : null;
    }

    @Override
    public SignInWithRecaptchaParameters validate() throws APIException {
        super.validate();

        this.client = deductClient();

        return this;
    }

    public String getRecaptchaTokenParam() {
        return this.recaptchaToken;
    }

    public ApiClient getClient() {
        return this.client;
    }

    /**
     * Deducts the client based on the request path.
     */
    protected ApiClient deductClient() {
        if (this.path == null) return null;

        if (this.path.startsWith("/asst/")) return ApiClient.ASSISTANT_WEB;
        if (this.path.startsWith("/public-site/")) return ApiClient.PUBLIC_SITE;

        return null;
    }
}
