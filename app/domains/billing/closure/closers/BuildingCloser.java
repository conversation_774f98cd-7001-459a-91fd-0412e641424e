package domains.billing.closure.closers;

import domains.billing.closure.exceptions.BuildingClosureException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import models.*;

public abstract class BuildingCloser {

    protected Building building;
    protected Unit currentUnit;
    protected Date closureDayAtMidnight;
    protected Date previousClosureDayAtMidnight;
    protected ClosureRecord recording;
    protected int minimumUsesPerUnit;
    protected List<MachineUse> usesToBill;
    protected List<CardEvent> cardsToBill;
    protected List<Bill> bills;
    protected List<BillItem> billItems;
    protected List<Attachment> attachments;

    protected BuildingCloser(
        Building building,
        Date closureDayAtMidnight,
        Date previousClosureDayAtMidnight,
        ClosureRecord recording
    ) {
        this.building = building;
        this.closureDayAtMidnight = closureDayAtMidnight;
        this.previousClosureDayAtMidnight = previousClosureDayAtMidnight;
        this.recording = recording;
        this.usesToBill = new ArrayList<>();
        this.cardsToBill = new ArrayList<>();
        this.bills = new ArrayList<>();
        this.billItems = new ArrayList<>();
        this.attachments = new ArrayList<>();
    }

    protected BuildingCloser(
        Building building,
        Unit currentUnit,
        Date closureDayAtMidnight,
        Date previousClosureDayAtMidnight,
        ClosureRecord recording
    ) {
        this.building = building;
        this.currentUnit = currentUnit;
        this.closureDayAtMidnight = closureDayAtMidnight;
        this.previousClosureDayAtMidnight = previousClosureDayAtMidnight;
        this.recording = recording;
        this.usesToBill = new ArrayList<>();
        this.cardsToBill = new ArrayList<>();
        this.bills = new ArrayList<>();
        this.billItems = new ArrayList<>();
        this.attachments = new ArrayList<>();
    }

    public abstract void close() throws BuildingClosureException;

    public void clean() {
        this.building = null;
        this.currentUnit = null;
        this.closureDayAtMidnight = null;
        this.previousClosureDayAtMidnight = null;
        this.recording = null;
        this.usesToBill = null;
        this.cardsToBill = null;
        this.bills = null;
        this.attachments = null;
    }

    public void closeAndClean() throws BuildingClosureException {
        this.close();
        this.clean();
    }

    // GETTERs
    public Building getBuilding() {
        return this.building;
    }

    public Date getClosureDayAtMidnight() {
        return this.closureDayAtMidnight;
    }

    public Date getPreviousClosureDayAtMidnight() {
        return this.previousClosureDayAtMidnight;
    }

    public ClosureRecord getRecording() {
        return this.recording;
    }

    public List<MachineUse> getUsesToBill() {
        return this.usesToBill;
    }

    public List<CardEvent> getCardToBill() {
        return this.cardsToBill;
    }

    public List<Bill> getBills() {
        return this.bills;
    }

    public List<Attachment> getAttachments() {
        return this.attachments;
    }

    public int getMinimumUsesPerUnit() {
        return this.minimumUsesPerUnit;
    }

    public List<BillItem> getBillItems() {
        return this.billItems;
    }

    public Unit getCurrentUnit() {
        return this.currentUnit;
    }

    public boolean appliesIVA() {
        return this.building.getRate().appliesIVA();
    }

    // SETTERs
    public void setMinimumUsesPerUnit(int minimumUsesPerUnit) {
        this.minimumUsesPerUnit = minimumUsesPerUnit;
    }

    // ADDers
    public void addUsesToBill(List<MachineUse> uses) {
        if (uses != null && !uses.isEmpty()) {
            this.usesToBill.addAll(uses);
        }
    }

    public void addCardsToBill(List<CardEvent> cards) {
        if (cards != null && !cards.isEmpty()) {
            this.cardsToBill.addAll(cards);
        }
    }
}
