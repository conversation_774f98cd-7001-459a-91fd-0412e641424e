package domains.billing.closure.closers;

import domains.billing.closure.exceptions.BuildingClosureException;
import domains.billing.closure.steps.ConvertBillToAttachmentsStep;
import domains.billing.closure.steps.building.GenerateBuildingBillsStep;
import domains.billing.closure.steps.per_unit.AdjustMinimumUsesPerUnitStep;
import domains.billing.closure.steps.per_unit.CalculateMinimumUsesPerUnitStep;
import java.util.Date;
import models.Building;
import models.ClosureRecord;

public class PrepaidWithMinimumPerUnitBuildingCloser extends BuildingCloser {

    public PrepaidWithMinimumPerUnitBuildingCloser(
        Building building,
        Date closureDayAtMidnight,
        Date previousClosureDayAtMidnight,
        ClosureRecord recording
    ) {
        super(building, closureDayAtMidnight, previousClosureDayAtMidnight, recording);
    }

    @Override
    public void close() throws BuildingClosureException {
        new CalculateMinimumUsesPerUnitStep().run(this);
        new AdjustMinimumUsesPerUnitStep().run(this);
        new GenerateBuildingBillsStep().run(this);
        new ConvertBillToAttachmentsStep().run(this);
    }
}
