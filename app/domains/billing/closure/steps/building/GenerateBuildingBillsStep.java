package domains.billing.closure.steps.building;

import domains.billing.BuildingService;
import domains.billing.closure.closers.BuildingCloser;
import domains.billing.closure.exceptions.BuildingClosureException;
import domains.billing.closure.steps.ClosureStep;
import domains.billing.exceptions.BillingException;
import global.APIException;
import play.Logger;

public class GenerateBuildingBillsStep implements ClosureStep {

    @Override
    public void run(BuildingCloser closer) throws BuildingClosureException {
        try {
            closer
                .getBills()
                .addAll(
                    new BuildingService()
                        .invoiceBuilding(
                            closer.getBuilding(),
                            closer.getPreviousClosureDayAtMidnight(),
                            closer.getClosureDayAtMidnight(),
                            closer.getUsesToBill(),
                            closer.getCardToBill(),
                            closer.getBillItems()
                        )
                );
            Logger.debug("Building \"{}\" was billed.", closer.getBuilding().getName());
        } catch (BillingException | APIException e) {
            e.printStackTrace();
            Logger.debug(
                "ERROR - Thrown error during Building \"{}\" is billed - error: {}.",
                closer.getBuilding().getName(),
                e.getMessage()
            );
            throw BuildingClosureException.raise(BuildingClosureException.Errors.BILLING, e);
        } catch (Exception e) {
            Logger.error("Unexpected exception when creating the bill.", e);
            throw e;
        }
    }
}
