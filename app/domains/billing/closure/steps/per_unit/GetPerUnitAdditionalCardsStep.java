package domains.billing.closure.steps.per_unit;

import domains.billing.closure.closers.BuildingCloser;
import domains.billing.closure.steps.ClosureStep;
import java.util.List;
import models.CardEvent;
import models.CardEventType;
import queries.cards.CardEventQuery;

public class GetPerUnitAdditionalCardsStep implements ClosureStep {

    @Override
    public void run(BuildingCloser closer) {
        List<CardEvent> cards = new CardEventQuery()
            .filterForBillingByUnitId(
                closer.getPreviousClosureDayAtMidnight(),
                closer.getClosureDayAtMidnight(),
                closer.getCurrentUnit().getId(),
                CardEventType.CARD_ASSIGNED_BILLABLE
            )
            .find();

        closer.addCardsToBill(cards);
    }
}
