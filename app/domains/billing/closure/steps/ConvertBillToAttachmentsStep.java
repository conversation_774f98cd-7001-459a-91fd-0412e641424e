package domains.billing.closure.steps;

import domains.billing.closure.closers.BuildingCloser;
import domains.billing.closure.exceptions.BuildingClosureException;
import domains.billing.invoicing.InvoicingService;
import domains.billing.invoicing.InvoicingServiceFactory;
import domains.billing.invoicing.exceptions.InvoicingException;
import java.util.ArrayList;
import java.util.List;
import models.Attachment;
import models.Bill;
import play.Logger;

public class ConvertBillToAttachmentsStep implements ClosureStep {

    @Override
    public void run(BuildingCloser closer) throws BuildingClosureException {
        List<Attachment> attachments = new ArrayList<>();

        int index = 1;

        InvoicingService invoicingService = null;

        for (Bill bill : closer.getBills()) {
            if (invoicingService == null) {
                invoicingService = InvoicingServiceFactory.createService(bill);
            }

            try {
                byte[] billPdfData = invoicingService.getInvoiceFile(bill);
                Logger.debug("Got PDF of Building \"{}\".", closer.getBuilding().getName());

                attachments.add(
                    new Attachment(
                        closer.getBuilding().getName() + "_Factura " + index++,
                        "pdf",
                        "application/pdf",
                        billPdfData
                    )
                );
            } catch (InvoicingException e) {
                e.printStackTrace();
                throw BuildingClosureException.raise(
                    BuildingClosureException.Errors.GETTING_BILL_PDF,
                    e,
                    closer.getBills()
                );
            }
        }
        closer.getAttachments().addAll(attachments);
    }
}
