package domains.billing.closure.mappings;

import domains.billing.closure.enums.ClosureType;
import java.util.*;
import utils.TemplateRenderer;
import views.html.closure.*;

public class NotificationEmailTemplateClosureTypeMapping {

    private static final Map<TemplateRenderer, List<ClosureType>> mapping = new HashMap<>();

    static {
        mapping.put(
            postpaid::render,
            Arrays.asList(
                ClosureType.POSTPAID,
                ClosureType.POSTPAID_RESOURCE_REIMBURSEMENT,
                ClosureType.PREPAID_AUTO_RECHARGEABLE_USES,
                ClosureType.COLIVING,
                ClosureType.POSTPAID_WITH_MINIMUM,
                ClosureType.MIXED,
                ClosureType.POSTPAID_WITH_MINIMUM_RESOURCE_REIMBURSEMENT
            )
        );
        mapping.put(
            prepaidResourceReimbursement::render,
            Collections.singletonList(ClosureType.PREPAID_RESOURCE_REIMBURSEMENT)
        );
        mapping.put(
            mixedResourceReimbursement::render,
            Collections.singletonList(ClosureType.MIXED_RESOURCE_REIMBURSEMENT)
        );
        mapping.put(
            minimumPerUnitAndResourceReimbursement::render,
            Collections.singletonList(
                ClosureType.PREPAID_WITH_MINIMUM_PER_UNIT_RESOURCE_REIMBURSEMENT
            )
        );
        mapping.put(
            minimumPerUnit::render,
            Arrays.asList(
                ClosureType.POSTPAID_WITH_MINIMUM_PER_UNIT,
                ClosureType.POSTPAID_WITH_MINIMUM_PER_UNIT_RESOURCE_REIMBURSEMENT,
                ClosureType.PREPAID_WITH_MINIMUM_PER_UNIT
            )
        );
    }

    public static Optional<TemplateRenderer> findKeysByClosureType(ClosureType closureType) {
        return mapping
            .entrySet()
            .stream()
            .filter(entry -> entry.getValue().contains(closureType))
            .map(Map.Entry::getKey)
            .findFirst();
    }

    public static boolean containsClosureType(ClosureType closureType) {
        return mapping.values().stream().anyMatch(list -> list.contains(closureType));
    }
}
