package domains.billing.invoicing.sicfe;

import domains.billing.invoicing.InvoicingService;
import domains.billing.invoicing.exceptions.InvoicingException;
import domains.billing.invoicing.sicfe.types.SicfeInvoicingServiceConfig;
import domains.billing.invoicing.types.BookingResult;
import domains.billing.invoicing.types.InvoicingResult;
import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.bind.JAXBElement;
import models.Bill;
import org.datacontract.schemas._2004._07.sicfecontract.*;
import org.tempuri.ISICFEEmisor;
import org.tempuri.ImpSICFEEmisor;
import services.bill.CFEBuilder;

public abstract class BaseSicfeService extends InvoicingService {

    protected final SicfeInvoicingServiceConfig config;
    private final URL url;

    protected BaseSicfeService(SicfeInvoicingServiceConfig config) {
        super();
        this.config = config;
        try {
            this.url = new URL(config.url);
        } catch (MalformedURLException ex) {
            loggerError("Can't create URL for SICFE: " + ex.getMessage());
            throw new RuntimeException(ex);
        }
    }

    @Override
    public InvoicingResult invoice(Bill bill) throws InvoicingException {
        CFEBuilder builder = new CFEBuilder(bill, this.config);
        String cfe;
        try {
            cfe = builder.build();
        } catch (Exception e) {
            loggerError("Can't build CFE: " + e.getMessage());
            throw new InvoicingException("Can't build CFE: " + e.getMessage());
        }

        logger("Sending:" + cfe);

        SICFERespuestaEnvioCFE response = new ImpSICFEEmisor(this.url)
            .getBasicHttpBindingISICFEEmisor()
            .envioCFE(
                config.username,
                config.password,
                config.tenant,
                config.env,
                cfe,
                bill.getReference(),
                null,
                true,
                22,
                0,
                null,
                null,
                true,
                null,
                "1.33"
            );

        if (response.getCodigo() != 0) {
            loggerError("Error sending CFE: " + response.getDescripcion().getValue());
            throw new InvoicingException(response.getDescripcion().getValue());
        }

        IdCFE idCFE = response.getIdCFE().getValue();

        return new InvoicingResult(
            idCFE.getNumero(),
            idCFE.getSerie().getValue(),
            idCFE.getTipo().toString()
        );
    }

    @Override
    public byte[] getInvoiceFile(Bill bill) throws InvoicingException {
        ISICFEEmisor emisor = new ImpSICFEEmisor(this.url).getBasicHttpBindingISICFEEmisor();
        IdCFE idCFE = new IdCFE();

        ObjectFactory factory = new ObjectFactory();
        JAXBElement<String> serieXML = factory.createIdCFESerie(bill.getSerie());
        JAXBElement<String> rucXML = factory.createIdCFERucemisor(config.senderRuc);

        idCFE.setSerie(serieXML);
        idCFE.setNumero(bill.getNumber());
        idCFE.setTipo(Integer.parseInt(bill.getTipoDoc()));
        idCFE.setRucemisor(rucXML);

        SICFERespuestaBuffer response;
        try {
            response =
                emisor.obtenerPDF(config.username, config.password, idCFE, config.tenant, null);
        } catch (Exception e) {
            e.printStackTrace();
            throw new InvoicingException("Can't get invoice PDF: " + e.getMessage());
        }

        return response.getBuffer().getValue();
    }

    @Override
    public BookingResult bookInvoiceNumber(Bill bill) throws InvoicingException {
        CFEBuilder builder = new CFEBuilder(bill, this.config);
        String cfe;

        try {
            cfe = builder.build();
        } catch (Exception e) {
            e.printStackTrace();
            throw new InvoicingException("Can't book invoice number: " + e.getMessage());
        }

        logger("Sending: " + cfe);

        ReservarNro book = new ImpSICFEEmisor(this.url)
            .getBasicHttpBindingISICFEEmisor()
            .reservarNro(config.username, config.password, config.tenant, config.env, cfe);

        return new BookingResult(
            book.getNro(),
            book.getSerie().getValue(),
            book.getTipo().toString(),
            book.getFVencimiento().toGregorianCalendar().getTime()
        );
    }

    @Override
    public void cancelBookedNumber(Bill bill) throws InvoicingException {
        CFEBuilder builder = new CFEBuilder(bill, this.config);
        String cfe;

        try {
            cfe = builder.build();
        } catch (Exception e) {
            e.printStackTrace();
            throw new InvoicingException("Can't cancel booked invoice number: " + e.getMessage());
        }

        logger("Cancelling: " + cfe);

        SICFERespuesta response = new ImpSICFEEmisor(this.url)
            .getBasicHttpBindingISICFEEmisor()
            .anularNumeracion(
                config.username,
                config.password,
                config.tenant,
                Integer.parseInt(bill.getTipoDoc()),
                bill.getSerie(),
                bill.getNumber(),
                bill.getNumber(),
                builder.getBranchNumber(),
                true
            );

        if (response.getCodigo() != 0) {
            throw new InvoicingException(response.getDescripcion().getValue());
        }
    }

    @Override
    public InvoicingResult confirmBookedNumber(Bill bill) throws InvoicingException {
        CFEBuilder builder = new CFEBuilder(bill, this.config);
        String cfe;

        try {
            cfe = builder.build();
        } catch (Exception e) {
            e.printStackTrace();
            throw new InvoicingException("Can't confirm booked invoice number: " + e.getMessage());
        }

        logger("Sending:" + cfe);

        ISICFEEmisor emisor = new ImpSICFEEmisor(this.url).getBasicHttpBindingISICFEEmisor();
        SICFERespuestaEnvioCFE response = emisor.confirmarCFE(
            config.username,
            config.password,
            config.tenant,
            config.env,
            cfe,
            bill.getReference(),
            null,
            null,
            ""
        );
        if (response.getCodigo() != 0) {
            throw new InvoicingException(response.getDescripcion().getValue());
        }

        IdCFE idCFE = response.getIdCFE().getValue();
        return new InvoicingResult(
            idCFE.getNumero(),
            idCFE.getSerie().getValue(),
            idCFE.getTipo().toString()
        );
    }
}
