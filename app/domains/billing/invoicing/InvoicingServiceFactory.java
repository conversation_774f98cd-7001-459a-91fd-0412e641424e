package domains.billing.invoicing;

import domains.billing.enums.BillingServiceTenant;
import domains.billing.invoicing.sicfe.impl.lavamar.LavamarSicfeService;
import domains.billing.invoicing.sicfe.impl.lavomat.LavomatSicfeService;
import models.Bill;
import models.BuildingType;

public class InvoicingServiceFactory {

    private InvoicingServiceFactory() {}

    public static InvoicingService createService(Bill bill) {
        BillingServiceTenant tenant = getTenant(bill);
        switch (tenant) {
            case LAVAMAR:
                return new LavamarSicfeService();
            case LAVOMAT:
            case OTHER:
            default:
                return new LavomatSicfeService();
        }
    }

    private static BillingServiceTenant getTenant(Bill bill) {
        BillingServiceTenant billingServiceTenant = BillingServiceTenant.LAVOMAT;
        if (
            bill.getAssociatedRecipient() != null &&
            bill
                .getAssociatedRecipient()
                .getBuildingType()
                .equals(BuildingType.THIRD_PARTY_LAUNDROMAT)
        ) {
            billingServiceTenant = BillingServiceTenant.LAVAMAR;
        }

        return billingServiceTenant;
    }
}
