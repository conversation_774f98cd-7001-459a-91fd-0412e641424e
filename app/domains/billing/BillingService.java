package domains.billing;

import domains.billing.invoicing.InvoicingServiceFactory;
import domains.billing.invoicing.exceptions.InvoicingException;
import domains.billing.invoicing.types.BookingResult;
import domains.billing.invoicing.types.InvoicingResult;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import models.*;
import services.BaseService;
import services.bill.CFEItem;

public class BillingService extends BaseService {

    public void publishBill(Bill bill) {
        try {
            InvoicingResult result = InvoicingServiceFactory.createService(bill).invoice(bill);

            updateBillNumbering(bill, result);
            updateBillState(bill, Bill.BillState.SENT);
        } catch (InvoicingException e) {
            handleInvoicingException(e, bill);
        }
    }

    public void updateBillNumbering(Bill bill, InvoicingResult result) {
        bill.setSerie(result.getInvoiceSeries());
        bill.setNumber(result.getInvoiceNumber());
        bill.setTipoDoc(result.getInvoiceType());
        bill.update();
    }

    public void updateBillState(Bill bill, Bill.BillState billState) {
        bill.setState(billState);
        bill.update();
    }

    public void updateBillPeriodEnd(Bill bill, BookingResult result) {
        updateBillNumbering(bill, result);
        bill.setBilledPeriodEnd(result.getBookingDateEnd());
        bill.update();
    }

    protected <T extends CFEItem> List<BillItem> toBillItems(List<T> uses) {
        List<BillItem> items = new ArrayList<>();

        for (T item : uses) {
            if (item instanceof MachineUse) {
                items.add(new BillItem((MachineUse) item));
            } else {
                items.add(new BillItem(item));
            }
        }

        return items;
    }

    /**
     * Remove machine uses which do not belong to the given building.
     */
    protected List<MachineUse> depurate(List<MachineUse> uses, Building building) {
        List<MachineUse> result = new ArrayList<>();

        for (MachineUse use : uses) {
            if (!cardBelongsToBuilding(use, building)) {
                continue;
            }
            result.add(use);
        }

        return result;
    }

    protected boolean cardBelongsToBuilding(MachineUse use, Building building) {
        return building
            .getUnits()
            .stream()
            .anyMatch(unit -> use.getCard() != null && unit.equals(use.getCard().getUnit()));
    }

    /**
     * Remove machine uses which do not belong to the given unit.
     */
    protected List<MachineUse> depurate(List<MachineUse> uses, Unit unit) {
        return uses
            .stream()
            .filter(use -> use.getCard() != null && unit.equals(use.getCard().getUnit()))
            .collect(Collectors.toList());
    }

    /**
     * Create an extra invoicing item in case the building did not register the minimum uses contracted.
     */
    protected void adjustToMinimumUses(
        Building building,
        List<BillItem> billItems,
        Date toPrice,
        Date toMinUses
    ) {
        int minimumUses = building.adjustToBuildingMinimumUses(toMinUses);
        int currentBillItemsSize = billItems.size();
        boolean appliesIVA = building.getRate().appliesIVA();

        if (minimumUses > currentBillItemsSize) {
            int diff = minimumUses - currentBillItemsSize;
            BillItem billItem = createBillItem(
                diff,
                building.getRate().getPriceCompany(toPrice),
                appliesIVA
            );
            billItems.add(billItem);
        }
    }

    protected void adjustToUnitMinimumUses(
        Building building,
        List<BillItem> billItems,
        Date toPrice,
        int totalMinUsesPerUnitToAdjust
    ) {
        boolean appliesIVA = building.getRate().appliesIVA();

        if (totalMinUsesPerUnitToAdjust > 0) {
            BillItem billItem = createBillItem(
                totalMinUsesPerUnitToAdjust,
                building.getRate().getPriceCompany(toPrice),
                appliesIVA
            );
            billItems.add(billItem);
        }
    }

    private BillItem createBillItem(int quantity, double priceCompany, boolean appliesIVA) {
        double priceWithoutIVA = priceCompany / 1.22;
        ItemBillingIndicator ivaType = appliesIVA
            ? ItemBillingIndicator.GRAVADO_A_TASA_BASICA
            : ItemBillingIndicator.EXENTO_DE_IVA;

        return new BillItem(
            new MachineUse(),
            "MACHINE_USE",
            quantity,
            priceWithoutIVA,
            ivaType.getCode()
        );
    }

    /**
     * Set the created bill to the invoiced items
     */
    protected <T extends BillableItem> void confirmBilling(List<T> items, Bill bill) {
        for (T item : items) {
            item.setBill(bill);
        }
    }

    private void handleInvoicingException(InvoicingException e, Bill bill) {
        loggerError(
            "Couldn't bill to DGI for bill id : {}, reason: {}.",
            bill.getId(),
            e.getMessage()
        );

        updateBillState(bill, Bill.BillState.ERROR_DGI);
    }
}
