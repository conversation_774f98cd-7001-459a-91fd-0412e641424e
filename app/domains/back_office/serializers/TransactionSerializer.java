package domains.back_office.serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.Bill;
import models.Transaction;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class TransactionSerializer {

    public static JSONObject listToJson(List<Transaction> transactions, int level)
        throws APIException {
        JSONObject transactionsJson = new JSONObject();

        JSONArray transactionListJson = new JSONArray();
        for (Transaction transaction : transactions) {
            transactionListJson.put(itemToJson(transaction, level));
        }

        try {
            transactionsJson.put("transactions", transactionListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing transaction list : " + e.getMessage());
        }

        return transactionsJson;
    }

    public static JSONObject itemToJson(Transaction transaction, int level) throws APIException {
        JSONObject transactionJson = new JSONObject();

        try {
            transactionJson.put("id", transaction.getId());
            transactionJson.put(
                "authorizationResult",
                transaction.getAuthorizationresult() != null
                    ? transaction.getAuthorizationresult().toString()
                    : JSONObject.NULL
            );
            if (
                transaction.getAuthorizationresult() != Transaction.AuthorizationResult.AUTHORIZED
            ) {
                transactionJson.put("errorMessage", transaction.getErrormessage());
            }

            if (level >= 1) {
                transactionJson.put("amount", transaction.getAmount());
                transactionJson.put(
                    "authorizationResultMessage",
                    transaction.getAuthorizationresultmessage() != null
                        ? transaction.getAuthorizationresultmessage()
                        : JSONObject.NULL
                );
                transactionJson.put("creationDate", transaction.getCreationDate());
                transactionJson.put("email", transaction.getEmail());
                transactionJson.put("origin", transaction.getOrigin());
                transactionJson.put("uid", transaction.getUid());
                transactionJson.put("comission", transaction.getComission());
            }
            if (level >= 2) {
                Bill bill = transaction.getBill();
                if (bill != null) {
                    transactionJson.put("serie", bill.getSerie());
                    transactionJson.put("number", bill.getNumber());
                    transactionJson.put("recipientName", bill.getRecipientName());
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing transaction : " + e.getMessage());
        }

        return transactionJson;
    }
}
