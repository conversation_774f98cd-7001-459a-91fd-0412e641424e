package domains.back_office.serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.text.SimpleDateFormat;
import java.util.List;
import models.Administration;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import policies.BasePolicy;
import policies.Level;

public class AdministrationSerializer {

    public static JSONObject listToJson(List<Administration> administrations, int level)
        throws APIException {
        JSONObject administrationsJson = new JSONObject();

        JSONArray administrationsListJson = new JSONArray();
        for (Administration administration : administrations) {
            administrationsListJson.put(itemToJson(administration, level));
        }

        try {
            administrationsJson.put("administrations", administrationsListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing administration list : " + e.getMessage());
        }
        return administrationsJson;
    }

    public static JSONObject itemToJson(Administration administration, int level)
        throws APIException {
        JSONObject administrationJson = new JSONObject();

        try {
            administrationJson.put("id", administration.getId());
            administrationJson.put("name", administration.getName());

            if (level >= Level.MINIMAL_MEDIUM_LEVEL) {
                SimpleDateFormat format = new SimpleDateFormat("dd-MM-yyyy");

                administrationJson.put("contact", administration.getContact());
                administrationJson.put("address", administration.getAddress());
                administrationJson.put(
                    "collector",
                    administration.getDebtCollector() != null
                        ? administration.getDebtCollector().getFullName()
                        : JSONObject.NULL
                );
                administrationJson.put(
                    "collectionDate",
                    administration.getCollectionDate() != null
                        ? format.format(administration.getCollectionDate())
                        : JSONObject.NULL
                );
                administrationJson.put("closureDay", administration.getClosureDay());
                administrationJson.put("mpCreationDate", administration.getMpCreationDate());
            }

            if (level >= Level.MEDIUM_LEVEL) {
                // INFO: none request this level of info
                //                    int buildingLevel = 2;
                //
                //                    JSONArray buildingsJson = new JSONArray();
                //                    for (Building b : administration.getBuildings()) {
                //                        buildingsJson.put(BuildingSerializer.buildingToJson(b, level));
                //                    }
                //
                //                    administrationJson.put("buildings", buildingsJson);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing administration to the bot: " + e.getMessage());
        }

        return administrationJson;
    }
}
