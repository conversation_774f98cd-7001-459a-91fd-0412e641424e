package domains.back_office.serializers;

import domains.back_office.services.tiny_urls.UrlService;
import global.APIException;
import java.util.List;
import models.TinyUrl;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import utils.DateHelper;

public class TinyUrlSerializer {

    public static JSONObject listToJson(List<TinyUrl> tinyUrls, int level) throws APIException {
        JSONObject tinyUrlJson = new JSONObject();

        JSONArray tinyUrlListJson = new JSONArray();
        for (TinyUrl rate : tinyUrls) {
            tinyUrlListJson.put(itemToJson(rate, level));
        }

        try {
            tinyUrlJson.put("tinyUrls", tinyUrlListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing rate list : " + e.getMessage());
        }

        return tinyUrlJson;
    }

    public static JSONObject itemToJson(TinyUrl tinyUrl, int level) throws APIException {
        JSONObject json = new JSONObject();

        try {
            json.put("id", tinyUrl.getId());
            json.put("token", tinyUrl.getToken());

            if (level >= 1) {
                json.put("description", tinyUrl.getDescription());
                json.put("destinationUrl", tinyUrl.getDestinationUrl());
                json.put("isActive", tinyUrl.isActive());
                json.put(
                    "createdAt",
                    DateHelper.printDateAndTime(new DateTime(tinyUrl.getCreatedAt()))
                );
                json.put(
                    "updatedAt",
                    DateHelper.printDateAndTime(new DateTime(tinyUrl.getUpdatedAt()))
                );
                json.put("url", UrlService.getFullUrl(tinyUrl));
            }

            if (level >= 2) {
                if (tinyUrl.getAssociatedMachine() != null) {
                    json.put(
                        "associatedTo",
                        "Machine: " + tinyUrl.getAssociatedMachine().getSerialNumber()
                    );
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing tiny url : " + e.getMessage());
        }

        return json;
    }
}
