package domains.back_office.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.dto.administrations.AdministrationParameter;
import domains.back_office.dto.administrations.CreateParameters;
import domains.back_office.dto.administrations.UpdateParameters;
import domains.back_office.serializers.AdministrationSerializer;
import domains.back_office.services.administrations.AdministrationFactory;
import global.APIException;
import java.util.List;
import models.Administration;
import play.libs.F;
import play.mvc.Result;
import policies.AdministrationPolicy;
import policies.ApiClient;
import policies.actions.Policy;
import queries.administrations.AdministrationQuery;

@Policy(AdministrationPolicy.class)
public class AdministrationsController extends BackOfficeBaseController {

    @Tx(readOnly = true)
    public F.Promise<Result> list() throws APIException {
        AdministrationPolicy allowedPolicy = this.getAllowedPolicy();
        AdministrationQuery query = allowedPolicy.list();

        return json(AdministrationSerializer.listToJson(query.find(), queryLevel(1)));
    }

    @Tx
    public F.Promise<Result> create() throws APIException {
        AdministrationPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        CreateParameters params = new CreateParameters(body()).validate();

        params.getAdministration().save();

        return F.Promise.pure(created());
    }

    @Tx
    public F.Promise<Result> update(final int administrationId) throws APIException {
        AdministrationPolicy allowedPolicy = this.getAllowedPolicy();
        AdministrationQuery query = allowedPolicy.update(administrationId);
        Administration admin = query.single();

        UpdateParameters params = new UpdateParameters(admin, body()).validate();

        AdministrationFactory factory = new AdministrationFactory();
        factory.update(admin, params);

        return F.Promise.pure(ok());
    }

    @Tx
    public F.Promise<Result> delete(final int administrationId) throws APIException {
        AdministrationPolicy allowedPolicy = this.getAllowedPolicy();
        AdministrationQuery query = allowedPolicy.delete(administrationId);
        Administration admin = query.single();

        AdministrationParameter params = new AdministrationParameter(admin).validate();

        params.getAdministration().delete();

        return F.Promise.pure(ok());
    }
}
