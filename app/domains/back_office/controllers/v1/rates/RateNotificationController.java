package domains.back_office.controllers.v1.rates;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.controllers.v1.BackOfficeBaseController;
import global.APIException;
import global.ErrorMessage;
import global.PermissionValidator;
import javax.inject.Inject;
import models.Role;
import play.libs.F;
import play.mvc.Result;
import play.mvc.Security;
import security.v1.Secured;
import services.rate.RateNotificationService;

@ErrorMessage
@global.LoggingMessage
@Security.Authenticated(Secured.class)
public class RateNotificationController extends BackOfficeBaseController {

    @Inject
    RateNotificationService rateNotificationService;

    @Tx
    public F.Promise<Result> sendBuildingAdminsNewRatePricesEmail() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                this.rateNotificationService.sendBuildingAdminsNewRatePricesEmail();
                return F.Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }

    @Tx
    public F.Promise<Result> sendLavomatAdminUpcomingRatesExpirationEmail() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                this.rateNotificationService.sendLavomatAdminUpcomingRatesExpirationEmail();
                return F.Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }

    @Tx
    public F.Promise<Result> sendBuildingAdminNewRatePricesEmail(final int rateId)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                this.rateNotificationService.sendBuildingAdminNewRatePricesEmail(rateId);
                return F.Promise.pure(ok());
            },
            Role.MASTER,
            Role.ADMIN
        );
    }
}
