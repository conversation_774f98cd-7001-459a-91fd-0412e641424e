package domains.back_office.controllers.v1.rates;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.controllers.v1.BackOfficeBaseController;
import domains.back_office.dto.rates.RateEventCreateParameters;
import domains.back_office.dto.rates.RateEventDeleteParameters;
import domains.back_office.dto.rates.RateEventUpdateParameters;
import domains.back_office.serializers.rates.RateEventSerializer;
import global.APIException;
import global.BackOfficeCop;
import java.util.List;
import models.RateEvent;
import play.libs.F;
import play.mvc.Result;
import policies.actions.Policy;
import policies.rates.RateEventPolicy;
import queries.rates.RateEventQuery;
import services.rate.RateEventFactory;

@Policy(RateEventPolicy.class)
public class RateEventsController extends BackOfficeBaseController {

    @Tx(readOnly = true)
    public F.Promise<Result> list(final int rateId) throws APIException {
        RateEventPolicy allowedPolicy = this.getAllowedPolicy();
        RateEventQuery query = allowedPolicy.list();

        List<RateEvent> events = query.filterByRate(rateId).orderByValidFrom().find();

        return json(RateEventSerializer.listToJson(events, queryLevel(1)));
    }

    @Tx
    @BackOfficeCop
    public F.Promise<Result> create(final int rateId) throws APIException {
        RateEventPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        RateEventCreateParameters params = new RateEventCreateParameters(rateId, body()).validate();

        RateEventFactory factory = new RateEventFactory(params.getRateEvent());
        factory.create();

        return F.Promise.pure(created());
    }

    @Tx
    @BackOfficeCop
    public F.Promise<Result> update(final int rateId, final int id) throws APIException {
        RateEventPolicy allowedPolicy = this.getAllowedPolicy();
        RateEventQuery query = allowedPolicy.update(id);
        RateEvent event = query.single();

        RateEventUpdateParameters params = new RateEventUpdateParameters(event, body()).validate();

        RateEventFactory factory = new RateEventFactory(event);
        factory.update(params);

        return F.Promise.pure(created());
    }

    @Tx
    public F.Promise<Result> delete(final int rateId, final int id) throws APIException {
        RateEventPolicy allowedPolicy = this.getAllowedPolicy();
        RateEventQuery query = allowedPolicy.delete(id);
        RateEvent event = query.single();

        new RateEventDeleteParameters(event).validate();

        RateEventFactory factory = new RateEventFactory(event);
        factory.delete();

        return F.Promise.pure(ok());
    }
}
