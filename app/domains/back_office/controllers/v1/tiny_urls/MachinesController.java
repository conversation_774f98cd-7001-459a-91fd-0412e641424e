package domains.back_office.controllers.v1.tiny_urls;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.controllers.v1.BackOfficeBaseController;
import domains.back_office.dto.tiny_urls.MachineAssignParameters;
import domains.back_office.dto.tiny_urls.MachineUnassignParameters;
import domains.back_office.services.tiny_urls.AssignationService;
import global.APIException;
import play.libs.F;
import play.mvc.Result;
import policies.TinyUrlPolicy;
import policies.actions.Policy;

@Policy(TinyUrlPolicy.class)
public class MachinesController extends BackOfficeBaseController {

    @Tx
    public F.Promise<Result> assign(final String token, final int machineId) throws APIException {
        TinyUrlPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.update();

        MachineAssignParameters params = new MachineAssignParameters(token, machineId).validate();

        AssignationService service = new AssignationService(params.getTinyUrl());
        service.assign(params);

        return F.Promise.<Result>pure(ok());
    }

    @Tx
    public F.Promise<Result> unassign(final String token, final int machineId) throws APIException {
        TinyUrlPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.update();

        MachineUnassignParameters params = new MachineUnassignParameters(token, machineId)
            .validate();

        AssignationService service = new AssignationService(params.getTinyUrl());
        service.unassign(params);

        return F.Promise.<Result>pure(ok());
    }
}
