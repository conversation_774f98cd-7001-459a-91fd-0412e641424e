package domains.back_office.controllers.v1.administrations;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.controllers.v1.BackOfficeBaseController;
import domains.back_office.dto.administrations.AdministrationParameter;
import domains.back_office.dto.administrations.AssignAdminDebtCollectorParameters;
import domains.back_office.services.administrations.DebtCollectorsService;
import global.APIException;
import play.libs.F;
import play.mvc.Result;
import policies.AdministrationPolicy;
import policies.actions.Policy;
import queries.administrations.AdministrationQuery;

@Policy(AdministrationPolicy.class)
public class UsersController extends BackOfficeBaseController {

    @Tx
    public F.Promise<Result> unassignDebtCollector(final int administrationId) throws APIException {
        AdministrationPolicy allowedPolicy = this.getAllowedPolicy();
        AdministrationQuery query = allowedPolicy.update(administrationId);

        AdministrationParameter params = new AdministrationParameter(query.single()).validate();

        DebtCollectorsService service = new DebtCollectorsService(params.getAdministration());
        service.unassign();

        return F.Promise.pure(ok(service.getOperationResult()));
    }

    @Tx
    public F.Promise<Result> assignDebtCollector(
        final int administrationId,
        final int debtCollectorId,
        final String collectionDate
    ) throws APIException {
        AdministrationPolicy allowedPolicy = this.getAllowedPolicy();
        AdministrationQuery query = allowedPolicy.update(administrationId);

        AssignAdminDebtCollectorParameters params = new AssignAdminDebtCollectorParameters(
            query.single(),
            debtCollectorId,
            collectionDate
        )
            .validate();

        DebtCollectorsService service = new DebtCollectorsService(params.getAdministration());
        service.assign(params);

        return F.Promise.pure(ok(service.getOperationResult()));
    }
}
