package domains.back_office.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.dto.machines.GetMachineParameters;
import domains.back_office.dto.machines.MachineCreateParameters;
import domains.back_office.serializers.machine.MachineHistorySerializer;
import domains.back_office.services.machines.MachineFactory;
import global.APIException;
import global.exceptions.machines.MachineNotFoundException;
import java.util.List;
import models.Machine;
import models.MachineHistoryRecord;
import play.libs.F;
import play.mvc.Result;
import policies.MachinePolicy;
import policies.actions.Policy;
import queries.machine_history_record.MachineHistoryRecordQuery;
import queries.machines.MachineQuery;
import serializers.MachineSerializer;

@Policy(MachinePolicy.class)
public class MachinesController extends BackOfficeBaseController {

    @Tx
    public F.Promise<Result> create() throws APIException {
        MachinePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        MachineCreateParameters dto = new MachineCreateParameters(body()).validate();

        MachineFactory factory = new MachineFactory();
        factory.create(dto);

        return F.Promise.<Result>pure(created());
    }

    @Tx(readOnly = true)
    public F.Promise<Result> list() throws APIException {
        MachinePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.list();

        GetMachineParameters parameters = new GetMachineParameters(queryString()).validate();

        MachineQuery query = new MachineQuery();
        List<Machine> machines = query
            .searchBySerialNumber(parameters.getSerialNumber())
            .filterWithoutBuilding()
            .orderByIdDesc()
            .find();

        return json(MachineSerializer.machineListToJson(machines, queryLevel(1)));
    }

    @Tx(readOnly = true)
    public F.Promise<Result> getMachineHistory(int machineId) throws APIException {
        MachinePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.get();

        Machine machine = new MachineQuery().get(machineId);
        if (machine == null) {
            throw new MachineNotFoundException();
        }

        List<MachineHistoryRecord> history = new MachineHistoryRecordQuery()
            .filterByMachineId(machineId)
            .find();

        return json(MachineHistorySerializer.serialize(machine, history));
    }
}
