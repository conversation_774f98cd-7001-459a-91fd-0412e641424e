package domains.back_office.controllers.v1.cards;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.controllers.v1.BackOfficeBaseController;
import global.APIException;
import global.exceptions.CardNotFoundException;
import models.Card;
import play.libs.F;
import play.mvc.Result;
import policies.CardPolicy;
import policies.actions.Policy;
import serializers.CardEventSerializer;

@Policy(CardPolicy.class)
public class CardEventsController extends BackOfficeBaseController {

    @Tx(readOnly = true)
    public F.Promise<Result> list(int cardId) throws APIException {
        CardPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.get();

        Card card = (Card) Card.findById(cardId);

        if (card == null) {
            throw new CardNotFoundException();
        }

        return json(CardEventSerializer.cardEventListToJson(card.getCardEvents(), queryLevel(0)));
    }
}
