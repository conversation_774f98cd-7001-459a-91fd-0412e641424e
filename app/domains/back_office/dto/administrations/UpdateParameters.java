package domains.back_office.dto.administrations;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.Administration;

public class UpdateParameters extends CreateParameters {

    protected static final String CLOSURE_DAY_PARAM = "closureDay";

    protected Integer closureDay;
    protected Administration administration;

    public UpdateParameters(Administration administration, JsonNode body) throws APIException {
        super(body);
        this.administration = administration;
        this.closureDay = safeInt(CLOSURE_DAY_PARAM, body, null);
    }

    public UpdateParameters validate() throws APIException {
        if (this.administration == null) {
            throw APIException.raise(APIException.APIErrors.ADMINISTRATION_NOT_FOUND);
        }

        super.validate();

        return this;
    }

    public Integer getClosureDay() {
        return this.closureDay;
    }

    public String getName() {
        return this.name;
    }

    public String getAddress() {
        return this.address;
    }

    public String getContact() {
        return this.contact;
    }
}
