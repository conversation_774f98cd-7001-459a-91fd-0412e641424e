package domains.back_office.dto.tiny_urls;

import domains.back_office.exceptions.TinyUrlIsNotAssociatedException;
import domains.back_office.exceptions.TinyUrlWrongAssociationException;
import global.APIException;
import models.Machine;
import models.TinyUrl;

public class MachineUnassignParameters extends AssignationParameters {

    protected int machineId;
    protected Machine machine;

    public MachineUnassignParameters(String token, final int machineId) {
        super(token);
        this.machineId = machineId;
    }

    @Override
    public MachineUnassignParameters validate() throws APIException {
        super.validate();

        if (this.tinyUrl.getAssociatedMachine() == null) {
            throw new TinyUrlIsNotAssociatedException();
        }

        this.machine = Machine.findById(this.machineId);
        if (this.machine == null) {
            throw APIException.raise(APIException.APIErrors.MACHINE_NOT_FOUND);
        }

        if (this.machine.getId() != this.tinyUrl.getAssociatedMachine().getId()) {
            throw new TinyUrlWrongAssociationException();
        }

        return this;
    }

    public Machine getMachine() {
        return this.machine;
    }
}
