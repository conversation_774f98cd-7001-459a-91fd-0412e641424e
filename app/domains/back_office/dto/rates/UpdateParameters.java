package domains.back_office.dto.rates;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.Rate;

public class UpdateParameters extends RateParameters {

    protected Rate rate;

    public UpdateParameters(Rate rate, JsonNode body) throws APIException {
        super(body);
        this.rate = rate;
    }

    @Override
    public UpdateParameters validate() throws APIException {
        if (this.rate == null) {
            throw APIException.raise(APIException.APIErrors.RATE_NOT_FOUND);
        }

        super.validate();

        return this;
    }
}
