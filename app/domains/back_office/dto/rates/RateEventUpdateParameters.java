package domains.back_office.dto.rates;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.RateEvent;

public class RateEventUpdateParameters extends RateEventParameters {

    protected RateEvent event;

    public RateEventUpdateParameters(final RateEvent event, JsonNode body) throws APIException {
        super(body);
        this.event = event;
    }

    @Override
    public RateEventUpdateParameters validate() throws APIException {
        if (this.event == null) {
            throw APIException.raise(APIException.APIErrors.RATE_EVENT_NOT_FOUND);
        }

        super.validate();

        return this;
    }
}
