package domains.back_office.dto.transactions;

import global.APIException;
import java.util.Map;

public class GetTransactionParameters extends dto.BaseFilterParameters {

    public GetTransactionParameters(Map<String, String[]> queryString) throws APIException {
        super(queryString);
    }

    public GetTransactionParameters validate() throws APIException {
        super.validate();

        return this;
    }
}
