package domains.back_office.dto.replacement;

import com.fasterxml.jackson.databind.JsonNode;
import domains.back_office.dto.parts.PartCreateParameters;
import global.APIException;
import models.Part;
import models.Replacement;

public class RaplacementCreateParameters extends PartCreateParameters {

    protected static final String ENGLISH_DESCRIPTION = "english_description";
    protected static final String MINIMUM_STOCK = "minimum_stock";
    protected static final String ANUAL_CONSUMPTION = "anual_consumption";
    protected static final String REQUEST_POINT = "request_point";
    protected static final String UNIT_PRICE = "unit_price";
    protected static final String UY_PRICE = "uy_price";
    protected static final String QUANTITY = "quantity";

    protected final String englishDescription;
    protected final int minimumStock;
    protected final int anualConsumption;
    protected final int requestPoint;
    protected final double unitPrice;
    protected final double uyPrice;
    protected final int quantity;

    public RaplacementCreateParameters(JsonNode body) throws APIException {
        super(body);
        this.englishDescription = safeString(ENGLISH_DESCRIPTION, body);
        this.minimumStock = safeInt(MINIMUM_STOCK, body, 0);
        this.anualConsumption = safeInt(ANUAL_CONSUMPTION, body, 0);
        this.requestPoint = safeInt(REQUEST_POINT, body, 0);
        this.unitPrice = safeDouble(UNIT_PRICE, body, 0.0);
        this.uyPrice = safeDouble(UY_PRICE, body, 0.0);
        this.quantity = safeInt(QUANTITY, body, 0);
    }

    public RaplacementCreateParameters validate() throws APIException {
        super.validate();
        return this;
    }

    public Replacement getReplacement() {
        Replacement replacement = new Replacement();
        replacement.setName(this.name);
        replacement.setModel(this.model);
        replacement.setDescription(this.description);
        replacement.setState(Part.PartState.NEW);
        replacement.setSerialNumber(this.serialNumber);
        replacement.setEnglishDescription(this.englishDescription);
        replacement.setMinimumStock(this.minimumStock);
        replacement.setAnualConsumption(this.anualConsumption);
        replacement.setRequestPoint(this.requestPoint);
        replacement.setUnitPrice(this.unitPrice);
        replacement.setUyPrice(this.uyPrice);
        replacement.setQuantity(this.quantity);

        return replacement;
    }
}
