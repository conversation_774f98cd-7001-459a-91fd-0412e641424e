package domains.back_office.dto.machines;

import com.fasterxml.jackson.databind.JsonNode;
import domains.back_office.dto.parts.PartCreateParameters;
import global.APIException;
import global.exceptions.BuildingNotFoundException;
import models.*;
import queries.buildings.BuildingQuery;

public class MachineCreateParameters extends PartCreateParameters {

    protected static final String ENGLISH_DESCRIPTION = "english_description";
    protected static final String UNIT_PRICE = "unit_price";
    protected static final String UY_PRICE = "uy_price";
    protected static final String AVERAGE_USE_TIME = "average_use_time";
    protected static final String MACHINE_TYPE = "machine_type";
    protected static final String REFERENCE = "reference";
    protected static final String SORT_INDEX = "sortIndex";
    protected static final String CAPACITY = "capacity";
    protected static final String MACHINE_MODEL_ID = "machine_model_id";
    protected static final String EXPECTED_USES = "expected_uses";
    protected static final String SPECIAL_RATE_ID = "specialRateId";
    protected static final String BUILDING_ID = "buildingId";

    protected final String machineEnglishDescription;
    protected final double machineUnitPrice;
    protected final double machineUyPrice;
    protected final int averageUseTime;
    protected final Machine.MachineType machineTypeString;
    protected final String reference;
    protected final int sortIndex;
    protected final int capacity;
    protected final int machineModelId;
    protected final int expectedUses;
    protected final int specialRateId;
    protected final int buildingId;
    protected Building building;
    Rate specialRate;
    MachineModel machineModel;

    public MachineCreateParameters(JsonNode body) throws APIException {
        super(body);
        this.machineEnglishDescription = safeString(ENGLISH_DESCRIPTION, body);
        this.machineUnitPrice = safeDouble(UNIT_PRICE, body, 0.0);
        this.machineUyPrice = safeDouble(UY_PRICE, body, 0.0);
        this.averageUseTime = safeInt(AVERAGE_USE_TIME, body, 0);
        this.machineTypeString =
            safeEnum(Machine.MachineType.class, MACHINE_TYPE, body, Machine.MachineType.WASHER);
        this.reference = safeString(REFERENCE, body);
        this.sortIndex = safeInt(SORT_INDEX, body, -1);
        this.capacity = safeInt(CAPACITY, body, -1);
        this.machineModelId = safeInt(MACHINE_MODEL_ID, body, 0);
        this.expectedUses = safeInt(EXPECTED_USES, body, 0);
        this.specialRateId = safeInt(SPECIAL_RATE_ID, body, 0);
        this.buildingId = safeInt(BUILDING_ID, body, 0);
    }

    public MachineCreateParameters validate() throws APIException {
        super.validate();

        this.specialRate = null;
        if (this.specialRateId > 0) {
            this.specialRate = Rate.findById(this.specialRateId);
        }

        this.machineModel = null;
        if (this.machineModelId > 0) {
            this.machineModel = MachineModel.findById(this.machineModelId);
        }

        if (this.buildingId > 0) {
            this.building = new BuildingQuery().get(this.buildingId);
            if (this.building == null) {
                throw new BuildingNotFoundException();
            }
        }

        return this;
    }

    public Machine getMachine() {
        Machine machine = new Machine(
            this.name,
            this.model,
            this.description,
            Part.PartState.NEW,
            this.serialNumber
        );
        machine.setEnglishDescription(this.machineEnglishDescription);
        machine.setUnitPrice(this.machineUnitPrice);
        machine.setUyPrice(this.machineUyPrice);
        machine.setMachineType(this.machineTypeString);
        machine.setExpectedUses(this.expectedUses);
        machine.setAverageUseTime(this.averageUseTime);
        machine.setMachineRate(this.specialRate);
        machine.setReference(this.reference);

        if (this.sortIndex >= 0) {
            machine.setSortIndex(this.sortIndex);
        }

        if (this.capacity > 0) {
            machine.setCapacity(this.capacity);
        }

        if (this.building != null) {
            machine.setBuilding(this.building);
        }

        machine.setMachineModel(this.machineModel);

        return machine;
    }
}
