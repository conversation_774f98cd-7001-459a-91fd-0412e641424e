package domains.back_office.dto.users;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.Administration;
import models.Building;
import models.Role;
import org.apache.commons.lang3.StringUtils;
import queries.administrations.AdministrationQuery;
import queries.buildings.BuildingQuery;

public class UserParameters extends dto.JsonBodyActionParameters {

    protected static final String NAME_PARAM = "name";
    protected static final String LASTNAME_PARAM = "lastname";
    protected static final String EMAIL_PARAM = "email";
    protected static final String PASSWORD_PARAM = "password";
    protected static final String PASSWORD_CONFIRMATION_PARAM = "passwordConfirm";
    protected static final String ROLE_PARAM = "role";
    protected static final String BUILDING_ID_PARAM = "buildingId";
    protected static final String ADMINISTRATION_ID_PARAM = "administrationId";

    protected BuildingQuery buildingQuery;
    protected AdministrationQuery administrationQuery;
    protected String name;
    protected String lastname;
    protected String email;
    protected String password;
    protected String passwordConfirmation;
    protected Role role;
    protected int administrationId;
    protected int buildingId;

    protected Administration administration;
    protected Building building;

    public UserParameters(JsonNode body) throws APIException {
        this.buildingQuery = new BuildingQuery();
        this.administrationQuery = new AdministrationQuery();
        this.name = safeString(NAME_PARAM, body, StringUtils.EMPTY);
        this.lastname = safeString(LASTNAME_PARAM, body, StringUtils.EMPTY);
        this.email = safeString(EMAIL_PARAM, body, StringUtils.EMPTY);
        this.password = safeString(PASSWORD_PARAM, body, StringUtils.EMPTY);
        this.passwordConfirmation =
            safeString(PASSWORD_CONFIRMATION_PARAM, body, StringUtils.EMPTY);
        this.role = safeEnum(Role.class, ROLE_PARAM, body);
        this.administrationId = safeInt(ADMINISTRATION_ID_PARAM, body, 0);
        this.buildingId = safeInt(BUILDING_ID_PARAM, body, 0);
    }

    public UserParameters validate() throws APIException {
        if (
            StringUtils.isEmpty(this.name) ||
            StringUtils.isEmpty(this.lastname) ||
            StringUtils.isEmpty(this.email) ||
            StringUtils.isEmpty(this.password) ||
            StringUtils.isEmpty(this.passwordConfirmation)
        ) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage(
                    getMissingParametersMessage(
                        NAME_PARAM,
                        LASTNAME_PARAM,
                        EMAIL_PARAM,
                        PASSWORD_PARAM,
                        PASSWORD_CONFIRMATION_PARAM
                    )
                );
        }

        if (!this.password.equals(this.passwordConfirmation)) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage("El password y la confirmación no coinciden.");
        }

        if (this.role.match(Role.TOTEM)) {
            if (this.buildingId == 0) {
                throw APIException
                    .raise(APIException.APIErrors.MISSING_PARAMETERS)
                    .setDetailMessage(getMissingParametersMessage(BUILDING_ID_PARAM));
            }

            this.building = this.buildingQuery.get(this.buildingId);
        } else if (this.role.match(Role.BUILDING_ADM)) {
            if (this.administrationId == 0) {
                throw APIException
                    .raise(APIException.APIErrors.MISSING_PARAMETERS)
                    .setDetailMessage(getMissingParametersMessage(ADMINISTRATION_ID_PARAM));
            }

            this.administration = this.administrationQuery.get(this.administrationId);
        }

        return this;
    }
}
