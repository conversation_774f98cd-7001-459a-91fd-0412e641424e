package domains.back_office.services.tiny_urls;

import models.TinyUrl;
import org.apache.commons.lang3.StringUtils;
import utils.ApplicationConfiguration;

public abstract class UrlService {

    public static String getFullUrl(TinyUrl tinyUrl) {
        String token = tinyUrl.getToken();
        if (StringUtils.isBlank(token)) {
            return StringUtils.EMPTY;
        }

        String url = ApplicationConfiguration.getApiBaseUrl();
        url += controllers.s.routes.ShortenerController.redirectByTinyUrl(token).path();

        return url;
    }
}
