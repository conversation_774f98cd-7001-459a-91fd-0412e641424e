package domains.back_office.services.card;

import domains.back_office.dto.cards.CardCreateParameters;
import global.APIException;
import javax.persistence.PersistenceException;

public class CardFactory {

    public void create(CardCreateParameters dto) throws APIException {
        try {
            dto.getCard().save();
        } catch (PersistenceException e) {
            handleRepeatedCardUuid(e);
        }
    }

    private void handleRepeatedCardUuid(PersistenceException e)
        throws PersistenceException, APIException {
        if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
            throw APIException.raise(APIException.APIErrors.CARD_UUID_ALREADY_EXISTS);
        }

        throw e;
    }
}
