package domains.totem.dto.booking;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.exceptions.MissingParametersException;
import global.exceptions.machines.MachineNotFoundException;
import models.Building;
import models.Machine;
import org.apache.commons.lang3.StringUtils;

public class CreateParameters extends dto.JsonBodyActionParameters {

    protected static final String MACHINE_SERIAL_PARAM = "serial";

    protected String machineSerial;
    protected Building building;

    protected Machine machine;

    public CreateParameters(JsonNode body, Building building) {
        this.machineSerial =
            safeStringIgnoreNullValue(MACHINE_SERIAL_PARAM, body, StringUtils.EMPTY);
        this.building = building;
    }

    @Override
    public CreateParameters validate() throws APIException {
        if (StringUtils.isBlank(this.machineSerial)) {
            throw new MissingParametersException(MACHINE_SERIAL_PARAM);
        }

        this.machine = Machine.findBySerialNumber(this.machineSerial);
        if (this.machine == null) {
            throw new MachineNotFoundException();
        }

        if (this.building == null || this.building.getId() != this.machine.getBuilding().getId()) {
            throw new MachineNotFoundException();
        }

        return this;
    }

    public Machine getMachine() {
        return this.machine;
    }
}
