package domains.payment_gateways.dto.bamboo_payment;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import models.Transaction;
import queries.transactions.TransactionQuery;

public class PhysicalAgentTransactionCancellationParameters
    extends PhysicalAgentTransactionValidationParameters {

    private static final long MAX_TIME_TO_REJECT_IN_HOURS = 1;

    private final long orderId;
    private Transaction transaction;

    public PhysicalAgentTransactionCancellationParameters(JsonNode body) throws APIException {
        super(body);
        this.orderId = safeLong("NroOrden", body, 0);
    }

    @Override
    public PhysicalAgentTransactionCancellationParameters validate() throws APIException {
        super.validate();

        if (this.orderId == 0) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage("The \"NroOrden\" provided is zero.");
        }

        String providerTransactionId = String.valueOf(this.orderId);
        this.transaction =
            new TransactionQuery().filterByProviderTransactionId(providerTransactionId).single();
        if (this.transaction == null) {
            throw APIException
                .raise(APIException.APIErrors.TRANSACTION_NOT_FOUND)
                .setDetailMessage("The \"NroOrden\" provided was not found.");
        }

        long diffInMillis = Math.abs(
            this.transaction.getCreationDate().getTime() - new Date().getTime()
        );
        long diff = TimeUnit.HOURS.convert(diffInMillis, TimeUnit.MILLISECONDS);
        if (diff > MAX_TIME_TO_REJECT_IN_HOURS) {
            play.Logger.error(
                "Trying to cancel a transaction created at \"{}\" while now it is \"{}\".",
                this.transaction.getCreationDate(),
                new Date()
            );
            throw APIException
                .raise(APIException.APIErrors.TRANSACTION_CAN_NOT_BE_CANCELED)
                .setDetailMessage(
                    "The transaction that wants to be cancelled was created more than " +
                    MAX_TIME_TO_REJECT_IN_HOURS +
                    " hours ago."
                );
        }

        return this;
    }

    public Transaction getTransaction() {
        return this.transaction;
    }
}
