package domains.payment_gateways.dto.bamboo_payment;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import global.APIException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import models.Card;
import models.Currency;

public class PhysicalAgentTransactionConfirmationParameters
    extends PhysicalAgentTransactionValidationParameters {

    protected Currency currency;
    protected int total;
    protected long orderId;
    protected List<PhysicalAgentTransactionPaymentDetail> paymentDetails;

    public PhysicalAgentTransactionConfirmationParameters(JsonNode body) throws APIException {
        super(body);
        this.currency = Currency.findByIsoNumber(safeInt("moneda", body, 0));
        this.total = safeInt("importeTotal", body, 0);
        this.orderId = safeLong("NroOrden", body, 0);
        this.paymentDetails = safePaymentDetails(body);
    }

    @Override
    public PhysicalAgentTransactionConfirmationParameters validate() throws APIException {
        super.validate();

        if (!this.currency.equals(Currency.UYU)) {
            throw APIException.raise(APIException.APIErrors.UNSUPPORTED_CURRENCY);
        }

        if (this.orderId == 0) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage("The \"NroOrden\" provided is zero.");
        }

        if (this.paymentDetails == null || this.paymentDetails.isEmpty()) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage("The \"detallesPago\" provided is empty.");
        }

        if (this.paymentDetails.size() > 1) {
            throw APIException
                .raise(APIException.APIErrors.INVALID_TRANSACTION_COMPOSITION)
                .setDetailMessage("There is more than one items on \"detallePago\".");
        }

        PhysicalAgentTransactionPaymentDetail paymentDetail = paymentDetails.get(0);
        if (paymentDetail.getTotalPlusTaxes() != this.total) {
            throw APIException
                .raise(APIException.APIErrors.TRANSACTION_INVALID_AMOUNT)
                .setDetailMessage(
                    "There is a difference between the values of \"importeTotal\" and \"detallePago\" > \"ImportePagado\"."
                );
        }

        if (!paymentDetail.getCurrency().equals(Currency.UYU)) {
            throw APIException.raise(APIException.APIErrors.UNSUPPORTED_CURRENCY);
        }

        if (!paymentDetail.getCurrency().equals(currency)) {
            throw APIException
                .raise(APIException.APIErrors.INVALID_TRANSACTION_COMPOSITION)
                .setDetailMessage("The currencies provided are different.");
        }

        return this;
    }

    private List<PhysicalAgentTransactionPaymentDetail> safePaymentDetails(JsonNode node)
        throws APIException {
        final String key = "detallesPago";
        if (node == null || !node.has(key)) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage("The \"" + key + "\" object is missing.");
        }

        JsonNode details = node.get(key);
        JsonNode array;
        // workaround to support the case that the "detallesPago" value is stringified
        // JSON
        if (details.isTextual()) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                array = mapper.readTree(details.asText());
            } catch (IOException e) {
                array = null;
                play.Logger.error(
                    "Error while converting the {} object - get {} - error: {}",
                    key,
                    details.asText(),
                    e.getMessage()
                );
            }
        } else {
            array = details;
        }

        List<PhysicalAgentTransactionPaymentDetail> list = new ArrayList<>();
        if (array == null || !array.isArray()) {
            return list;
        }

        for (JsonNode item : array) {
            PhysicalAgentTransactionPaymentDetail detail = new PhysicalAgentTransactionPaymentDetail();
            detail.setItemId(safeInt("IdItem", item, 0));
            detail.setCurrency(Currency.findByIsoNumber(safeInt("Moneda", item, 0)));
            detail.setTotalPlusTaxes(safeInt("ImportePagado", item, 0));

            list.add(detail);
        }

        return list;
    }

    public Card getCard() {
        return this.card;
    }

    public Currency getCurrency() {
        return this.currency;
    }

    public int getTotal() {
        return this.total;
    }

    public String getAgent() {
        return this.agent;
    }

    public String getOrderId() {
        return String.valueOf(this.orderId);
    }
}
