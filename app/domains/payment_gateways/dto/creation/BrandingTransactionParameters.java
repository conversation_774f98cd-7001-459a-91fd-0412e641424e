package domains.payment_gateways.dto.creation;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.*;
import java.lang.*;

public class BrandingTransactionParameters extends TransactionParameters {

    protected Integer brandingItemId;
    protected Integer quantity;

    public BrandingTransactionParameters(JsonNode body) throws APIException {
        super(body);
        quantity = safeInt("quantity", body, 0);
        brandingItemId = safeInt("branding_item_id", body, 0);
    }

    public BrandingTransactionParameters validate() throws APIException {
        if (quantity > 10 || quantity == 0 || brandingItemId == 0) throw APIException.raise(
            APIErrors.MISSING_PARAMETERS
        );

        super.validate();

        return this;
    }

    public Integer getBrandingItemId() {
        return this.brandingItemId;
    }

    public void setBrandingItemId(Integer brandingItemId) {
        this.brandingItemId = brandingItemId;
    }

    public Integer getQuantity() {
        return this.quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
}
