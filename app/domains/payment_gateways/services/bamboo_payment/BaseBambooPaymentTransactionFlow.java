package domains.payment_gateways.services.bamboo_payment;

import domains.payment_gateways.services.BaseTransactionFlow;
import global.APIException;
import java.nio.charset.StandardCharsets;
import java.security.spec.KeySpec;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import javax.crypto.spec.IvParameterSpec;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONException;
import org.json.JSONObject;

public abstract class BaseBambooPaymentTransactionFlow extends BaseTransactionFlow {

    /**
     * Internal item identifier for transactions.
     */
    public static final int ITEM_ID = 753;

    protected abstract String getUsername();

    protected abstract String getCustomerKey();

    protected abstract String getCustomerId();

    protected abstract String getKeyword();

    protected String getSecurityTokenUsingTransactionData(JSONObject data) {
        String customerKey = getCustomerKey();

        // Informacion de la transaccion a cifrar.
        String textoPlano = "";
        try {
            if (data.getString("idTarjetaCredito").equals("1")) {
                textoPlano =
                    data.getString("idCliente") +
                    data.getString("idTarjetaCredito") +
                    data.getString("primerNombre") +
                    data.getString("primerApellido") +
                    data.getString("email") +
                    data.getString("direccionEnvio") +
                    data.getString("valorTransaccion") +
                    data.getString("cantidadCuotas") +
                    data.getString("moneda") +
                    data.getString("numeroOrden") +
                    data.getString("version") +
                    data.getString("plan") +
                    data.getString("fecha") +
                    data.getString("consumidorFinal") +
                    data.getString("importeGravado");
            } else if (
                data.getString("idTarjetaCredito").equals("3") ||
                data.getString("idTarjetaCredito").equals("4")
            ) {
                textoPlano =
                    data.getString("idCliente") +
                    data.getString("idTarjetaCredito") +
                    data.getString("primerNombre") +
                    data.getString("primerApellido") +
                    data.getString("cedula") +
                    data.getString("email") +
                    data.getString("telefono") +
                    data.getString("valorTransaccion") +
                    data.getString("cantidadCuotas") +
                    data.getString("moneda") +
                    data.getString("numeroOrden") +
                    data.getString("version") +
                    data.getString("plan") +
                    data.getString("fecha") +
                    data.getString("consumidorFinal") +
                    data.getString("importeGravado");
            } else {
                textoPlano =
                    data.getString("idCliente") +
                    data.getString("idTarjetaCredito") +
                    data.getString("primerNombre") +
                    data.getString("primerApellido") +
                    data.getString("email") +
                    data.getString("valorTransaccion") +
                    data.getString("cantidadCuotas") +
                    data.getString("moneda") +
                    data.getString("numeroOrden") +
                    data.getString("version") +
                    data.getString("plan") +
                    data.getString("fecha") +
                    data.getString("consumidorFinal") +
                    data.getString("importeGravado");
            }

            if (data.getString("consumidorFinal").equals("1")) {
                textoPlano = textoPlano + data.getString("numeroFactura");
            }
        } catch (JSONException ex) {
            logger(
                "Error getting the SecurityTokenUsingTransactionData while parsing the input json - error: {} ",
                ex.getMessage()
            );
            ex.printStackTrace();
        }

        Date formattedDate = null;
        try {
            String date = data.getString("fecha");
            formattedDate = new SimpleDateFormat("yyyy-MM-ddhh:mm:ss").parse(date);
        } catch (JSONException | ParseException ex) {
            logger(
                "Error getting the SecurityTokenUsingTransactionData while parsing the \"fecha\" value - error: {} ",
                ex.getMessage()
            );
            ex.printStackTrace();
        }

        // Vector de incializacion
        String sIV64 = new SimpleDateFormat("yyMMddhhmmss").format(formattedDate).substring(1) +
        "=";

        return this.encryptTextToMemory(textoPlano, customerKey, sIV64);
    }

    protected String decryptSecurityTokenUsingDate(String securityToken, String securityDate) {
        String customerKey = getCustomerKey();

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyMMddhhmmss");
            Date date = new SimpleDateFormat("yyyy-MM-ddhh:mm:ss").parse(securityDate);
            String sIV64 = sdf.format(date).substring(1) + "=";
            return decryptTextFromMemory(securityToken, customerKey, sIV64);
        } catch (Exception ex) {
            logger(
                "Error decrypting the SecurityTokenUsingDate while parsing the securityDate value - error: {} ",
                ex.getMessage()
            );
            ex.printStackTrace();
        }

        return "";
    }

    /**
     * '... Se encripta con algoritmo 3DS el ID del Comercio,
     * usando como vector de inicialización la frase otorgada por Bamboo...'
     */
    protected String getSecurityPasswordUsingPhrase() {
        return encryptTextToMemory(getCustomerId(), getCustomerKey(), getKeyword());
    }

    private String encryptTextToMemory(
        String secretMessage,
        String secretKey,
        String initializationVector
    ) {
        try {
            final byte[] digestOfPassword = Base64.decodeBase64(
                secretKey.getBytes(StandardCharsets.UTF_8)
            );
            KeySpec keySpec = new DESedeKeySpec(digestOfPassword);
            SecretKey key = SecretKeyFactory.getInstance("DESede").generateSecret(keySpec);

            byte[] digestOfIV = Base64.decodeBase64(
                initializationVector.getBytes(StandardCharsets.UTF_8)
            );
            IvParameterSpec iv = new IvParameterSpec(digestOfIV);

            Cipher encryptCipher = Cipher.getInstance("DESede/CBC/PKCS5Padding", "SunJCE");
            encryptCipher.init(Cipher.ENCRYPT_MODE, key, iv);

            byte[] digestOfSecretMessage = secretMessage.getBytes(StandardCharsets.UTF_8);
            byte[] cipherText = encryptCipher.doFinal(digestOfSecretMessage);

            return new String(Base64.encodeBase64(cipherText), StandardCharsets.UTF_8);
        } catch (Exception ex) {
            logger("Encryption error - error: {}", ex.getMessage());

            return "";
        }
    }

    private String decryptTextFromMemory(
        String secretMessage,
        String secretKey,
        String initializationVector
    ) {
        try {
            final byte[] digestOfPassword = Base64.decodeBase64(
                secretKey.getBytes(StandardCharsets.UTF_8)
            );
            KeySpec keySpec = new DESedeKeySpec(digestOfPassword);
            SecretKey key = SecretKeyFactory.getInstance("DESede").generateSecret(keySpec);

            byte[] digestOfIV = Base64.decodeBase64(
                initializationVector.getBytes(StandardCharsets.UTF_8)
            );
            IvParameterSpec iv = new IvParameterSpec(digestOfIV);

            Cipher decryptCipher = Cipher.getInstance("DESede/CBC/PKCS5Padding", "SunJCE");
            decryptCipher.init(Cipher.DECRYPT_MODE, key, iv);

            byte[] digestOfSecretMessage = Base64.decodeBase64(secretMessage.getBytes());
            byte[] cipherText = decryptCipher.doFinal(digestOfSecretMessage);

            return new String(cipherText, StandardCharsets.UTF_8);
        } catch (Exception ex) {
            logger("Decryption error - error: {}", ex.getMessage());

            return "";
        }
    }

    protected void validateAuthorization(String user, String password) throws APIException {
        String username = getUsername();
        String generatedPassword = getSecurityPasswordUsingPhrase();

        if (generatedPassword.equals(password) && username.equals(user)) {
            return;
        }

        throw APIException.raise(APIException.APIErrors.UNAUTHORIZED);
    }
}
