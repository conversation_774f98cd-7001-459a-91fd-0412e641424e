package domains.payment_gateways.services.mercado_pago;

import com.mercadopago.MercadoPago;
import com.mercadopago.exceptions.MPException;
import com.mercadopago.resources.Payment;
import com.mercadopago.resources.Preference;
import com.mercadopago.resources.datastructures.preference.Address;
import com.mercadopago.resources.datastructures.preference.BackUrls;
import com.mercadopago.resources.datastructures.preference.Item;
import com.mercadopago.resources.datastructures.preference.Payer;
import domains.billing.exceptions.BillingException;
import domains.payment_gateways.dto.creation.BrandingTransactionParameters;
import domains.payment_gateways.services.BaseTransactionFlow;
import domains.payment_gateways.services.ITransactionFlow;
import global.APIException;
import global.APIException.APIErrors;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import models.*;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.json.JSONException;
import org.json.JSONObject;
import play.Play;
import play.i18n.Messages;
import play.twirl.api.Html;
import queries.transactions.TransactionQuery;
import services.ContactSupportService;
import utils.CardHelper;

public class MercadoPagoTransactionFlow extends BaseTransactionFlow implements ITransactionFlow {

    public static final String ORIGIN = "MercadoPagos";
    public static final String QR_ORIGIN = "QR Payment - MercadoPagos";

    // refresh token
    public String refreshToken(
        String name,
        String email,
        String uid,
        Integer currency,
        Integer amount,
        Card card,
        String rut,
        Object[] flowParams
    ) throws IOException, JSONException, MPException, APIException {
        // Obtenemos la administracion

        logger("REFRESH TOKEN >>> INICIO");

        Administration admin = card.getUnit().getBuilding().getAdministration();
        String refresh_token = admin.getMpRefreshToken();

        URL url = new URL(
            "https://api.mercadopago.com/oauth/token?client_secret=" +
            Play.application().configuration().getString("mercadopago.access.token") +
            "&grant_type=refresh_token&refresh_token=" +
            refresh_token
        );

        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setDoOutput(true);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("accept", "application/json");
        conn.setRequestProperty("content-type", "application/x-www-form-urlencoded");

        if (conn.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
        }

        BufferedReader br = new BufferedReader(new InputStreamReader((conn.getInputStream())));

        String output;
        StringBuilder sb = new StringBuilder();
        while ((output = br.readLine()) != null) {
            sb.append(output);
        }
        logger("RESPONSE_REFRESH_TOKEN >>> " + sb.toString());

        String resp_aux = sb.toString();

        // parsear respuesta y almacenar datos del administrador
        // String resp_aux =
        // "{access_token:APP_USR-7094776074066958-022318-dc092e6f4a645444b123ba3acd7adf50-402020864,public_key:APP_USR-c9203d95-61c9-4f53-9546-4682ea547c2a,refresh_token:TG-5c71c2d39b69e6000664f862-402020864,live_mode:true,user_id:402020864,token_type:bearer,expires_in:15552000,scope:offline_access
        // read write}";

        JSONObject jsonObject = new JSONObject(resp_aux);

        String acc_tok = jsonObject.getString("access_token");
        String ref_tok = jsonObject.getString("refresh_token");
        String exp_in;

        try {
            exp_in = String.valueOf(jsonObject.getInt("expires_in"));
        } catch (JSONException e) {
            exp_in = jsonObject.getString("expires_in");
        }

        admin.setMpAccessToken(acc_tok);
        admin.setMpRefreshToken(ref_tok);
        admin.setMpTokenExpDate(exp_in);
        admin.setMpCreationDate(new Date());

        admin.update();

        return this.createCreditTransaction(
                name,
                email,
                uid,
                currency,
                amount,
                card,
                rut,
                flowParams
            );
    }

    //
    public void bindAdministration(int adm, String code, String server)
        throws IOException, JSONException {
        // Obtenemos la administracion
        Administration admin = Administration.findById(adm);

        // Con el authorization code enviado que tiene una validez de 10 minutos
        // debemos solicitar a la api de oAuth las credenciales y almacenarlas

        URL url = new URL(
            "https://api.mercadopago.com/oauth/token?client_id=" +
            Play.application().configuration().getString("mercadopago.client.id") +
            "&client_secret=" +
            Play.application().configuration().getString("mercadopago.client.secret") +
            "&grant_type=authorization_code&code=" +
            code +
            "&state=" +
            adm +
            "&redirect_uri=https://" +
            server +
            "/api/v1/transaction/bind"
        );

        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setDoOutput(true);
        conn.setRequestMethod("POST");
        conn.setRequestProperty("accept", "application/json");
        conn.setRequestProperty("content-type", "application/x-www-form-urlencoded");

        if (conn.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new RuntimeException("Failed : HTTP error code : " + conn.getResponseCode());
        }

        BufferedReader br = new BufferedReader(new InputStreamReader((conn.getInputStream())));

        String output;
        StringBuilder sb = new StringBuilder();
        while ((output = br.readLine()) != null) {
            sb.append(output);
        }
        logger("RESPONSE >>> " + sb.toString());

        String resp_aux = sb.toString();

        // parsear respuesta y almacenar datos del administrador
        // String resp_aux =
        // "{access_token:APP_USR-7094776074066958-022318-dc092e6f4a645444b123ba3acd7adf50-402020864,public_key:APP_USR-c9203d95-61c9-4f53-9546-4682ea547c2a,refresh_token:TG-5c71c2d39b69e6000664f862-402020864,live_mode:true,user_id:402020864,token_type:bearer,expires_in:15552000,scope:offline_access
        // read write}";

        JSONObject jsonObject = new JSONObject(resp_aux);

        String acc_tok = jsonObject.getString("access_token");
        String ref_tok = jsonObject.getString("refresh_token");
        String exp_in;

        try {
            exp_in = String.valueOf(jsonObject.getInt("expires_in"));
        } catch (JSONException e) {
            exp_in = jsonObject.getString("expires_in");
        }

        admin.setMpAccessToken(acc_tok);
        admin.setMpRefreshToken(ref_tok);
        admin.setMpTokenExpDate(exp_in);
        admin.setMpCreationDate(new Date());

        admin.update();
    }

    public String createCreditTransaction(
        String name,
        String email,
        String uid,
        Integer currency,
        Integer amount,
        Card card,
        String rut,
        Object[] flowParams
    ) throws MPException, JSONException, APIException {
        super.createCreditTransaction();

        String server = (String) flowParams[0];
        String check_refreshed = "0";

        if (card == null) {
            return check_refreshed;
        }

        TransactionQuery transactionQuery = new TransactionQuery()
            .filterByOrigin(ORIGIN)
            .filterByCardUid(card.getUuid())
            .orderByCreationDateDesc();

        Transaction lastTransaction = transactionQuery.first();
        expireLastPreference(lastTransaction);

        Unit unit = card.getUnit();
        Rate rate = unit != null ? unit.getBuilding().getRate() : null;
        Date now = new Date();

        if (unit == null || rate.getPriceCompany(now).equals(rate.getPriceCustomer(now))) {
            logger("CREATE_TRANSACTION >>> NO APLICA SPLITTING");
            play.Logger.debug(
                "## Balance recharge ## MercadoPagoTransactionFlow.createCreditTransaction ## NO APLICA SPLITTING - uid: {}",
                uid
            );

            Transaction transaction = new Transaction(
                name,
                email,
                Currency.findByIsoNumber(currency),
                amount,
                card.getUuid(),
                unit,
                ORIGIN
            );
            transaction.save();

            play.Logger.debug(
                "## Balance recharge ## MercadoPagoTransactionFlow.createCreditTransaction ## uid: {} - transaction.id: {}",
                uid,
                transaction.getId()
            );

            if (StringUtils.isNotBlank(rut)) {
                transaction.setRut(rut);
                transaction.update();
            }

            // MERCADO PAGO
            Preference preference = createPreference(
                server,
                transaction.getPublicId(),
                transaction.getOrigin(),
                card.getUuid(),
                "Carga de Saldo",
                1,
                currency,
                (float) amount,
                unit != null ? unit.getDireccion() : "",
                email,
                name.split(" ")[0],
                name.split(" ")[1],
                0f,
                null
            );

            transaction.setProviderTransactionId(preference.getId());
            transaction.update();

            JSONObject response = new JSONObject();
            response.put("checkout", preference.getInitPoint());

            return response.toString();
        } else {
            // aplica splitting calcular comision
            logger("CREATE_TRANSACTION >>> APLICA SPLITTING");
            play.Logger.debug(
                "## Balance recharge ## MercadoPagoTransactionFlow.createCreditTransaction ## APLICA SPLITTING - uid: {}",
                uid
            );

            Transaction transaction = new Transaction(
                name,
                email,
                Currency.findByIsoNumber(currency),
                amount,
                card.getUuid(),
                unit,
                ORIGIN
            );
            transaction.save();

            if (StringUtils.isNotBlank(rut)) {
                transaction.setRut(rut);
            }

            double lavomatComission = lavomatCommission(rate, amount);
            if (lavomatComission > 0) {
                transaction.setComission(lavomatComission);
            }

            transaction.setReasonType(Transaction.ReasonType.CREDIT);
            transaction.update();

            play.Logger.debug(
                "## Balance recharge ## MercadoPagoTransactionFlow.createCreditTransaction ## uid: {} - transaction.id: {}",
                uid,
                transaction.getId()
            );

            Administration administration = unit.getBuilding().getAdministration();

            // MERCADO PAGO
            Preference preference = createPreference(
                server,
                transaction.getPublicId(),
                transaction.getOrigin(),
                card.getUuid(),
                "Carga de Saldo",
                1,
                currency,
                (float) amount,
                unit != null ? unit.getDireccion() : "",
                email,
                name.split(" ")[0],
                name.split(" ")[1],
                (float) transaction.getComission(),
                administration
            );

            transaction.setProviderTransactionId(preference.getId());
            transaction.update();

            JSONObject response = new JSONObject();

            if (preference.getInitPoint() == null) {
                String respuesta = preference.getLastApiResponse().getStringResponse();
                logger("CREATE_TRANSACTION >>> RESPUESTA" + respuesta);

                JSONObject jsonObject = new JSONObject(respuesta);

                String message = jsonObject.getString("message");
                int stat = jsonObject.getInt("status");

                if (
                    message.equals("invalid_token") ||
                    message.equals("expired_token") &&
                    stat == 401
                ) {
                    // refresh token is needed
                    logger("ADM_TOKEN EXPIERED");
                    // check_refreshed = this.refreshToken(name, email, uid, currency,amount, card,
                    // rut, flowParams) ;
                }
            } else {
                response.put("checkout", preference.getInitPoint());
                return response.toString();
            }
        }

        return check_refreshed;
    }

    public String createBrandingTransaction(BrandingTransactionParameters params, String server)
        throws APIException, MPException, JSONException {
        super.createBrandingTransaction();

        BrandingItem item = BrandingItem.findById(params.getBrandingItemId());

        if (item == null) throw APIException.raise(APIErrors.BRANDING_ITEM_NOT_FOUND);

        TransactionQuery transactionQuery = new TransactionQuery()
            .filterByOrigin(ORIGIN)
            .filterByReason(Transaction.ReasonType.BRANDING)
            .filterByEmail(params.getEmail())
            .orderByCreationDateDesc();

        Transaction lastTransaction = transactionQuery.first();
        expireLastPreference(lastTransaction);

        double unitPrice = item.getPriceCustomer();
        double amount = params.getQuantity() * unitPrice;

        Transaction transaction = new Transaction(
            params.getName(),
            params.getEmail(),
            Currency.findByIsoNumber(params.getCurrencyIsoNumber()),
            amount,
            params.getQuantity(),
            item,
            params.getAddress(),
            ORIGIN,
            Transaction.ReasonType.BRANDING
        );
        transaction.save();

        Preference preference = createPreference(
            server,
            transaction.getPublicId(),
            transaction.getOrigin(),
            params.getEmail(),
            ("Venta de " + item.getName()),
            params.getQuantity(),
            params.getCurrencyIsoNumber(),
            (float) unitPrice,
            params.getAddress(),
            params.getEmail(),
            params.getFirstName(),
            params.getLastName(),
            0f,
            null
        );

        transaction.setProviderTransactionId(preference.getId());
        transaction.update();

        JSONObject response = new JSONObject();
        response.put("checkout", preference.getInitPoint());

        return response.toString();
    }

    // TODO: available when its fixed
    private boolean isDisableToExpirePreference = true;

    private void expireLastPreference(Transaction transaction) {
        if (isDisableToExpirePreference) {
            return;
        }

        if (
            transaction == null ||
            StringUtils.isBlank(transaction.getProviderTransactionId()) ||
            transaction.getProviderTransactionId().equals("0")
        ) {
            return;
        }

        try {
            Preference preferenceToExpire = Preference.findById(
                transaction.getProviderTransactionId()
            );
            if (preferenceToExpire == null) {
                logger(
                    "PREFERENCE NOT FOUND >>> PREFERENCE_ID: {}",
                    transaction.getProviderTransactionId()
                );
                return;
            }

            Date expirationDateTo = preferenceToExpire.getExpirationDateTo();
            if (expirationDateTo != null && new DateTime(expirationDateTo).isBeforeNow()) {
                logger(
                    "PREFERENCE ALREADY EXPIRED >>> PREFERENCE_ID: {}",
                    preferenceToExpire.getId()
                );
                return;
            }

            logger("PREFERENCE TO EXPIRED >>> PREFERENCE_ID: {}", preferenceToExpire.getId());

            preferenceToExpire.setExpirationDateTo(DateTime.now().toDate());
            preferenceToExpire.update();

            logger("PREFERENCE_EXPIRED >>> PREFERENCE_ID: {}", preferenceToExpire.getId());
        } catch (MPException exception) {
            Throwable cause = exception.getCause();

            logger(
                "Got MPException from MercadoPago API Service - error: {} - cause: {} - complete description: {}",
                exception.getMessage(),
                cause != null ? cause.getMessage() : "No deep cause",
                exception.toString()
            );
        }
    }

    public void confirmTransaction(AuditWorkflow auditWorkflow) throws Exception {
        Transaction transaction = Transaction.findById(auditWorkflow.getPaymentId());
        confirmTransaction(transaction.getPublicId());
    }

    @Override
    public Html confirmTransaction(String customerTransactionId) throws Exception {
        Bill bill = null;
        Bill billWithoutIVA = null;
        try {
            logger("CONFIRM_TRANSACTION >>> TRX_ID: " + customerTransactionId);

            Transaction transaction = new TransactionQuery()
                .filterByPublicId(customerTransactionId)
                .singleLocked();
            if (
                !Transaction.AuthorizationResult.AUTHORIZED.equals(
                    transaction.getAuthorizationresult()
                )
            ) {
                transaction.confirm();
                transaction.update();
            } else {
                logger(
                    "CONFIRM_TRANSACTION >>> Already confirmed TRX_ID: {}",
                    customerTransactionId
                );
            }

            bill = transaction.getBill();
            if (bill == null) {
                logger(
                    "CONFIRM_TRANSACTION >>> The bill is being created - TRX_ID: {}",
                    customerTransactionId
                );

                Unit unit = transaction.getUnit();

                if (unit != null) {
                    Rate rate = unit.getBuilding().getRate();

                    Administration administration = unit.getBuilding().getAdministration();
                    if (
                        rate != null &&
                        rate.isSplittingApplicable() &&
                        (
                            administration == null ||
                            administration.isMpTokenExpired() ||
                            StringUtils.isBlank(administration.getMpAccessToken())
                        )
                    ) {
                        double billAmountWithoutIVA =
                            transaction.getAmount() - transaction.getComission();

                        billWithoutIVA =
                            createBillTaxFree(
                                billAmountWithoutIVA,
                                transaction.getCurrency().getIsoNumber(),
                                transaction
                            );
                    }
                }

                if (QR_ORIGIN.equals(transaction.getOrigin())) {
                    bill =
                        createBillWithAssociatedRecipient(
                            transaction.getAmount(),
                            transaction.getCurrency(),
                            transaction,
                            transaction.getRecipientBuilding()
                        );
                } else {
                    bill =
                        createBill(transaction.getAmount(), transaction.getCurrency(), transaction);
                }

                if (bill.getNumber() != null && bill.getNumber() != 0) {
                    updateCards(transaction, transaction.getAmount());
                }

                if (transaction.getReasonType() == Transaction.ReasonType.BRANDING) {
                    BrandingItemRequest request = new BrandingItemRequest(transaction);
                    request.save();
                    ContactSupportService.notifyPostman(request);
                }
            }

            transaction = Transaction.findById(transaction.getId());

            String preferenceId = transaction.getProviderTransactionId();
            Preference preference = Preference.findById(preferenceId);
            if (preference != null) {
                preference.setExpirationDateTo(new Date());
                preference.update();

                logger("PREFERENCE_EXPIRED >>> PREFERENCE_ID: {}", preferenceId);
            }

            if (transaction.getReasonType() == Transaction.ReasonType.BRANDING) {
                BrandingItemRequest request = new BrandingItemRequest(transaction);
                request.save();
                ContactSupportService.notifyPostman(request);
            }

            sendBillPDF(transaction, bill, billWithoutIVA);

            return views.html.generalMessage.render(
                "Su operación fue realizada con éxito! Le envíaremos un correo con el detalle.",
                transaction.getName()
            );
        } catch (BillingException e) {
            if (bill != null) {
                loggerError(
                    "Couldn't bill to DGI for bill id : {}, reason: {}.",
                    bill.getId(),
                    e.getMessage()
                );

                billingService.updateBillState(bill, Bill.BillState.ERROR_DGI);
            }
            if (billWithoutIVA != null) {
                loggerError(
                    "Couldn't bill to DGI for bill id : {}, reason: {}.",
                    billWithoutIVA.getId(),
                    e.getMessage()
                );
                billingService.updateBillState(billWithoutIVA, Bill.BillState.ERROR_DGI);
            }
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.INTERNAL_SERVER_ERROR, e)
                .withParentDetailMessage()
                .withParentStackTrace();
        }
    }

    @Override
    public void notifyTransaction(String topic, String id) {
        logger("NOTIFY_TRANSACTION >>> IPN_DATA: " + topic + "-" + id);
        play.Logger.debug(
            "## MP call ## MercadoPagoTransactionFlow.notifyTransaction ## topic: {} - id: {}",
            topic,
            id
        );

        Thread thread = new Thread(handleNotificationRunnable(topic, id));
        thread.start();
    }

    @Override
    public Html rejectTransaction(String customerTransactionId) {
        logger("REJECT_TRANSACTION >>> TRX_ID: " + customerTransactionId);

        Transaction transaction = getTransaction(customerTransactionId);

        /*
         * JSONObject response = new JSONObject();
         *
         * JSONObject transactionJson = new JSONObject();
         * transactionJson.put("error_message", transaction.getErrormessage());
         * transactionJson.put("authorization_code",
         * transaction.getAuthorizationcode());
         *
         * response.put("transaction_info", transactionJson);
         */

        // return json(response.toString());

        return views.html.generalMessage.render(
            "transacción fallida, se ha registrado el siguiente error: " +
            transaction.getErrormessage(),
            transaction.getName()
        );
    }

    @Deprecated
    public String buyCreditsBot(
        String name,
        String last_name,
        String email,
        Integer currency,
        String amount,
        String lavomat_card,
        String server
    ) throws MPException, APIException, JSONException {
        Card card = CardHelper.getCardByUid(lavomat_card);

        if (card != null) {
            Unit unit = card.getUnit();
            Rate rate = unit.getBuilding().getRate();
            Date now = new Date();

            if (unit != null) {
                if (!rate.isSplittingApplicable(now)) {
                    logger("BOT_CREATE_TRANSACTION >>> NO APLICA SPLITTING");

                    Transaction transaction = new Transaction(
                        name,
                        email,
                        Currency.findByIsoNumber(currency),
                        Integer.parseInt(amount),
                        lavomat_card,
                        unit,
                        ORIGIN
                    );
                    transaction.save();

                    // MERCADO PAGO

                    MercadoPago.SDK.setAccessToken(
                        Play.application().configuration().getString("mercadopago.access.token")
                    );
                    MercadoPago.SDK.setClientId(
                        Play.application().configuration().getString("mercadopago.client.id")
                    );
                    MercadoPago.SDK.setClientSecret(
                        Play.application().configuration().getString("mercadopago.client.secret")
                    );

                    // Create a preference object
                    Preference preference = new Preference();

                    // BackUrls
                    BackUrls backUrls = new BackUrls();

                    backUrls.setSuccess("www.lavomat.com.uy");
                    backUrls.setFailure(
                        "https://" +
                        server +
                        "/api/v1/transaction/reject?param1=" +
                        transaction.getPublicId() +
                        "&param2=" +
                        transaction.getOrigin()
                    );

                    // Create an item object
                    Item item = new Item();
                    item
                        .setId(lavomat_card)
                        .setTitle("Carga de Saldo")
                        .setQuantity(1)
                        .setCurrencyId(Currency.findByIsoNumber(currency).getIsoCode())
                        .setUnitPrice(Float.parseFloat(amount));

                    // Create a payer object
                    Payer payer = new Payer();
                    Address address = new Address().setStreetName(unit.getDireccion());
                    payer
                        .setEmail(email)
                        .setName(name)
                        .setSurname(last_name)
                        .setDateCreated(new Date().toString())
                        .setAddress(address);

                    // Setting preference properties
                    preference.setPayer(payer);
                    preference.appendItem(item);
                    preference.setBackUrls(backUrls);
                    preference.setBinaryMode(true);
                    preference.setExternalReference("Transaction_" + transaction.getPublicId());
                    preference.setNotificationUrl(
                        "https://" + server + "/api/v1/transaction/notify"
                    );
                    preference.setAutoReturn(Preference.AutoReturn.approved);

                    // Save and posting preference
                    preference.save();

                    JSONObject response = new JSONObject();

                    // response.put("checkout", preference.getInitPoint());
                    // return json(response.toString());

                    response.put(
                        "fulfillmentText",
                        "Ingrese aqui para finalizar la compra: " + preference.getInitPoint()
                    );

                    return response.toString();
                } else {
                    // aplica splitting calcular comision

                    logger("BOT_CREATE_TRANSACTION >>> APLICA SPLITTING");

                    double comision_adm = (double) rate.getPriceCompany(now) /
                    (double) rate.getPriceCustomer(now);
                    double comision_lavomat = Float.parseFloat(amount) * comision_adm;

                    if (comision_lavomat > Float.parseFloat(amount)) {
                        throw APIException
                            .raise(APIErrors.SPLIT_PAYMENT_COMISION_NOT_VALID)
                            .setDetailMessage(Messages.get("SPLIT_PAYMENT_COMISION_NOT_VALID"));
                    }

                    logger("CREATE_TRANSACTION >>> COMISION FINAL" + comision_lavomat);

                    Transaction transaction = new Transaction(
                        name,
                        email,
                        Currency.findByIsoNumber(currency),
                        Integer.parseInt(amount),
                        lavomat_card,
                        unit,
                        ORIGIN
                    );
                    transaction.save();

                    // MERCADO PAGO

                    String adm_acc_token = unit
                        .getBuilding()
                        .getAdministration()
                        .getMpAccessToken();

                    MercadoPago.SDK.setAccessToken(adm_acc_token);
                    MercadoPago.SDK.setClientId(
                        Play.application().configuration().getString("mercadopago.client.id")
                    );
                    MercadoPago.SDK.setClientSecret(
                        Play.application().configuration().getString("mercadopago.client.secret")
                    );

                    // Create a preference object
                    Preference preference = new Preference();

                    // BackUrls
                    BackUrls backUrls = new BackUrls();

                    backUrls.setSuccess("www.lavomat.com.uy");
                    backUrls.setFailure(
                        "https://" +
                        server +
                        "/api/v1/transaction/reject?param1=" +
                        transaction.getPublicId() +
                        "&param2=" +
                        transaction.getOrigin()
                    );

                    // Create an item object
                    Item item = new Item();
                    item
                        .setId(lavomat_card)
                        .setTitle("Carga de Saldo")
                        .setQuantity(1)
                        .setCurrencyId(Currency.findByIsoNumber(currency).getIsoCode())
                        .setUnitPrice(Float.parseFloat(amount));

                    // Create a payer object
                    Payer payer = new Payer();
                    Address address = new Address().setStreetName(unit.getDireccion());
                    payer
                        .setEmail(email)
                        .setName(name)
                        .setSurname(last_name)
                        .setDateCreated(new Date().toString())
                        .setAddress(address);

                    // Setting preference properties
                    preference.setPayer(payer);
                    preference.appendItem(item);
                    preference.setBackUrls(backUrls);
                    preference.setBinaryMode(true);
                    preference.setExternalReference("Transaction_" + transaction.getPublicId());
                    preference.setNotificationUrl(
                        "https://" + server + "/api/v1/transaction/notify"
                    );
                    preference.setAutoReturn(Preference.AutoReturn.approved);

                    preference.setMarketplaceFee((float) comision_lavomat);

                    // Save and posting preference
                    preference.save();

                    JSONObject response = new JSONObject();

                    // response.put("checkout", preference.getInitPoint());
                    response.put(
                        "fulfillmentText",
                        "Ingrese aqui para finalizar la compra: " + preference.getInitPoint()
                    );

                    return response.toString();
                }
            }
        }

        return "0";
    }

    @Override
    protected void handleInternalException(Exception ex) {
        if (ex instanceof MPException) {
            MPException mpException = (MPException) ex;
            Throwable cause = mpException.getCause();

            logger(
                "A MPException was gotten from the MercadoPago API Service - error: {} - cause: {} - complete description: {}",
                mpException.getMessage(),
                cause != null ? cause.getMessage() : "No deep cause",
                mpException.toString()
            );
        }
    }

    private Preference createPreference(
        String server,
        String transactionId,
        String transactionOrigin,
        String internalReference,
        String transactionDescription,
        int quantity,
        Integer currency,
        float amount,
        String address,
        String email,
        String name,
        String surname,
        float fee,
        Administration administration
    ) throws MPException {
        boolean isAdmTokenExpired = administration != null && administration.isMpTokenExpired();
        String admAccessToken = administration != null
            ? administration.getMpAccessToken()
            : StringUtils.EMPTY;

        MercadoPago.SDK.setAccessToken(
            StringUtils.isBlank(admAccessToken) || isAdmTokenExpired
                ? Play.application().configuration().getString("mercadopago.access.token")
                : admAccessToken
        );
        MercadoPago.SDK.setClientId(
            Play.application().configuration().getString("mercadopago.client.id")
        );
        MercadoPago.SDK.setClientSecret(
            Play.application().configuration().getString("mercadopago.client.secret")
        );

        // Create a preference object
        Preference preference = new Preference();

        // BackUrls
        BackUrls backUrls = new BackUrls();
        // backUrls.setSuccess("https://"+server+"/api/v1/transaction/confirm?param1="+tx);
        backUrls.setSuccess("www.lavomat.com.uy");
        backUrls.setFailure(
            "https://" +
            server +
            "/api/v1/transaction/reject?param1=" +
            transactionId +
            "&param2=" +
            transactionOrigin
        );

        // Create an item object
        Item item = new Item();
        item
            .setId(internalReference)
            .setTitle(transactionDescription)
            .setQuantity(quantity)
            .setCurrencyId(Currency.findByIsoNumber(currency).getIsoCode())
            .setUnitPrice(amount);

        // Create a payer object
        Payer payer = new Payer();
        payer
            .setEmail(email)
            .setName(name)
            .setSurname(surname)
            .setDateCreated(new Date().toString())
            .setAddress(new Address().setStreetName(address));

        // Setting preference properties
        preference.setPayer(payer);
        preference.appendItem(item);
        preference.setBackUrls(backUrls);
        preference.setBinaryMode(true);
        preference.setExternalReference("Transaction_" + transactionId);
        preference.setNotificationUrl(
            "https://" + server + "/api/v1/transaction/notify?source_news=ipn"
        );
        preference.setAutoReturn(Preference.AutoReturn.approved);

        DateTime now = DateTime.now();
        preference.setExpirationDateFrom(now.toDate());
        DateTime expiration = now.plusDays(1);
        preference.setExpirationDateTo(expiration.toDate());
        preference.setExpires(true);

        if (isAdministrationFeeBased(fee, admAccessToken, isAdmTokenExpired)) {
            preference.setMarketplaceFee(fee);
        }

        // Save and posting preference
        preference.save();

        return preference;
    }

    private boolean isAdministrationFeeBased(
        float fee,
        String admAccessToken,
        boolean isAdmTokenExpired
    ) {
        return fee != 0f && !isAdmTokenExpired && StringUtils.isNotBlank(admAccessToken);
    }

    private Runnable handleNotificationRunnable(String topic, String id) {
        try {
            play.Logger.debug(
                "## MP call ## MercadoPagoTransactionFlow.handleNotificationRunnable ## topic: {} - id: {}",
                topic,
                id
            );

            // Primer paso en almacenar la informacion que llega de la IPN
            IpnNotification notification = new IpnNotification(topic, id);
            notification.save();

            play.Logger.debug(
                "## MP call ## MercadoPagoTransactionFlow.handleNotificationRunnable ## saving IPN - topic: {} - id: {} - notification.id: {}",
                topic,
                id,
                notification.getId()
            );

            MercadoPago.SDK.setAccessToken(
                Play.application().configuration().getString("mercadopago.access.token")
            );
            MercadoPago.SDK.setClientId(
                Play.application().configuration().getString("mercadopago.client.id")
            );
            MercadoPago.SDK.setClientSecret(
                Play.application().configuration().getString("mercadopago.client.secret")
            );
            Payment payment = Payment.findById(id);

            play.Logger.debug(
                "## MP call ## MercadoPagoTransactionFlow.handleNotificationRunnable ## getting payment info from MP - topic: {} - id: {} - payment.ExternalReference: {} - payment.StatusDetail: {} - payment.AuthorizationCode: {} - payment.AuthorizationCode: {}",
                topic,
                id,
                payment.getExternalReference(),
                payment.getStatusDetail(),
                payment.getAuthorizationCode(),
                payment.getAuthorizationCode()
            );

            // Segundo paso es buscar el id de pago y obtener referencia externa (id de
            // transaccion interno)
            String transactionId = getTransactionIdByPayment(payment);

            play.Logger.debug(
                "## MP call ## MercadoPagoTransactionFlow.handleNotificationRunnable ## transaction_id? - topic: {} - id: {} - transaction_id: {}",
                topic,
                id,
                transactionId
            );

            if (StringUtils.isNotBlank(transactionId)) {
                Transaction transaction = getTransaction(transactionId);

                if (payment.getStatusDetail() != null) {
                    play.Logger.debug(
                        "## MP call ## MercadoPagoTransactionFlow.handleNotificationRunnable ## updating transaction - topic: {} - id: {} - transaction_id: {}",
                        topic,
                        id,
                        transactionId
                    );

                    // Actualizar la transaccion interna con los datos del fallo
                    transaction.setErrormessage(payment.getStatusDetail());
                    transaction.setAuthorizationcode(payment.getStatus().name());

                    play.Logger.debug(
                        "## MP call ## MercadoPagoTransactionFlow.handleNotificationRunnable ## payment approved? - topic: {} - id: {} - payment.status.name: {}",
                        topic,
                        id,
                        payment.getStatus().name()
                    );
                    play.Logger.debug(
                        "## MP call ## MercadoPagoTransactionFlow.handleNotificationRunnable ## already billed? - topic: {} - id: {} - transaction.bill.id: {}",
                        topic,
                        id,
                        transaction.getBill() == null ? "No BILL" : transaction.getBill().getId()
                    );
                    if (
                        payment.getStatus().name().equals("approved") &&
                        transaction.getBill() == null
                    ) {
                        play.Logger.debug(
                            "## MP call ## MercadoPagoTransactionFlow.handleNotificationRunnable ## START PAYMENT CONFIRMATION PROCESS - topic: {} - id: {}",
                            topic,
                            id
                        );

                        logger("START PAYMENT CONFIRMATION PROCESS");
                        this.confirmTransaction(transactionId);
                        logger("PAYMENT CONFIRMATION PROCESS");
                        notification = IpnNotification.findById(notification.getId());
                        notification.setTransaction(transaction);
                        notification.update();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    private String getTransactionIdByPayment(Payment payment) {
        String externalReference = payment.getExternalReference();
        logger(
            "NOTIFY_TRANSACTION >>> PAYMENT: {} - {}",
            externalReference,
            payment.getStatusDetail()
        );

        String transactionId = StringUtils.EMPTY;
        if (externalReference != null) {
            String[] splittedData = externalReference.split("_");

            if (splittedData.length > 1) {
                transactionId = splittedData[1];
            }
        }

        return transactionId;
    }
}
