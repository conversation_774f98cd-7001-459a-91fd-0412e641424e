package domains.payment_gateways.controllers.v1.transaction;

import com.fasterxml.jackson.databind.JsonNode;
import com.play4jpa.jpa.db.Tx;
import domains.payment_gateways.services.bancard.BancardTransactionFlow;
import dto.bancard.BancardParameters;
import global.APIException;
import play.libs.F;
import play.mvc.Result;

public class BancardController extends TransactionBaseController {

    /**
     * POST /api/v1/transaction/buySingleConfirm
     */
    @Tx
    public F.Promise<Result> confirm() throws APIException {
        BancardTransactionFlow service = new BancardTransactionFlow();
        service.logger("BODY: {} ", body());

        BancardParameters parameters = new BancardParameters(body()).validate();
        try {
            if (parameters.isConfirmable()) {
                service.confirmTransaction(parameters.getTransactionId());
            } else {
                service.rejectTransaction(parameters.getTransactionId());
            }
        } catch (Exception e) {
            service.handleException(e);
        }

        return F.Promise.<Result>pure(ok());
    }

    /**
     * POST /api/v1/transaction/buySingleRollback
     */
    @Tx
    public F.Promise<Result> rollback() throws APIException {
        BancardTransactionFlow service = new BancardTransactionFlow();
        JsonNode body = request().body().asJson();
        service.logger("BODY: {} ", body);

        BancardParameters parameters = new BancardParameters(body()).validate();

        try {
            service.rollbackTransaction(parameters.getTransactionId());
        } catch (Exception e) {
            service.handleException(e);
        }

        return F.Promise.<Result>pure(ok());
    }
}
