package domains.payment_gateways.controllers.v1.transaction;

import com.fasterxml.jackson.databind.JsonNode;
import com.play4jpa.jpa.db.Tx;
import domains.payment_gateways.services.mercado_pago.MercadoPagoTransactionFlow;
import global.APIException;
import global.BackOfficeCop;
import global.BackOfficeCopEntity;
import global.BackOfficeCopHandler;
import models.Currency;
import play.libs.F;
import play.mvc.Result;
import play.twirl.api.Html;

public class MercadoPagoController extends TransactionBaseController {

    /**
     * GET /api/v1/transaction/bind
     *
     * Vincular administraciones que cobran gastos de agua y electricidad
     */
    @Tx
    @BackOfficeCop(name = "Creación Token MercadoPago", anonymous = true)
    public F.Promise<Result> bindAdministration(int state, String code) throws APIException {
        try {
            String server = request().host();
            new MercadoPagoTransactionFlow().bindAdministration(state, code, server);

            return F.Promise.<Result>pure(
                ok(views.html.generalMessage.render("Su operación fue realizada con éxito!", ""))
            );
        } catch (Exception e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.INTERNAL_SERVER_ERROR, e)
                .withParentDetailMessage()
                .withParentStackTrace();
        }
    }

    /**
     * GET /api/v1/transaction/confirm
     *
     * Calls when the success backUrl is executed.
     * Side effects: increments internal balance and sends pdf bill by mail.
     */
    @Deprecated // no se llama desde MP en ningun momento
    @Tx
    public F.Promise<Result> confirm(String customerTransactionId) throws APIException {
        MercadoPagoTransactionFlow service = new MercadoPagoTransactionFlow();

        play.Logger.debug(
            "## MP call ## confirmTransaction ## called? - customerTransactionId: {}",
            customerTransactionId
        );

        try {
            Html response = service.confirmTransaction(customerTransactionId);

            return F.Promise.<Result>pure(ok(response));
        } catch (Exception e) {
            service.handleException(e);
        }

        return unreachableReturn();
    }

    /**
     * POST /api/v1/transaction/notify
     *
     * Mercado Pago exclusive.
     *
     * Receive SUCCESS or FAILURE notifications which could come from the QR
     * or credit/branding transactions.
     *
     * On success:
     * 1. confirm transaction
     * 1. increment internal balance if it is a credit transaction
     * 2. send bill by email
     *
     * On failure:
     * 1. save error info on transaction
     */
    @Tx
    public F.Promise<Result> notifyTx(String topic, String id) throws APIException {
        MercadoPagoTransactionFlow service = new MercadoPagoTransactionFlow();
        play.Logger.debug("## MP call ## notifyTransaction ## topic: {} - id: {}", topic, id);

        try {
            service.notifyTransaction(topic, id);

            // Envia un HTTP STATUS 200 (OK) a mercado pagos para notificar que se recibió
            // la IPN
            return F.Promise.<Result>pure(ok());
        } catch (Exception e) {
            service.handleException(e);
        }

        return unreachableReturn();
    }

    /**
     * GET /api/v1/transaction/bot
     *
     * Fulfillment for DialogFlow Chat Bot
     *
     * `@Deprecate`. No se llama desde ningun bot.
     * Parese ser una prueba: ed9ba0d buy credits bot tests
     */
    @Deprecated
    @Tx
    public F.Promise<Result> buyCreditsBot() throws APIException {
        MercadoPagoTransactionFlow service = new MercadoPagoTransactionFlow();
        JsonNode body = request().body().asJson();

        service.logger("BODY: {}", body);

        String name = body.get("queryResult").get("parameters").get("name").asText().trim();
        String last_name = body
            .get("queryResult")
            .get("parameters")
            .get("last_name")
            .asText()
            .trim();
        String email = body.get("queryResult").get("parameters").get("email").asText().trim();
        String amount = body.get("queryResult").get("parameters").get("amount").asText().trim();
        String lavomat_card = body
            .get("queryResult")
            .get("parameters")
            .get("lavomat_card")
            .asText()
            .trim();
        String server = request().host();

        try {
            String response = service.buyCreditsBot(
                name,
                last_name,
                email,
                Currency.UYU.getIsoNumber(),
                amount,
                lavomat_card,
                server
            );

            return json(response);
        } catch (Exception e) {
            service.handleException(e);
        }

        return json("0");
    }
}
