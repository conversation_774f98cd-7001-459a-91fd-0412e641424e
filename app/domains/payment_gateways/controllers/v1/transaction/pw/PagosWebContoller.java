package domains.payment_gateways.controllers.v1.transaction.pw;

import com.play4jpa.jpa.db.Tx;
import domains.payment_gateways.controllers.v1.transaction.TransactionBaseController;
import domains.payment_gateways.services.bamboo_payment.PagosWebTransactionFlow;
import global.APIException;
import java.util.Map;
import models.Transaction;
import play.libs.F;
import play.mvc.Result;

/**
 * Used to handle Pagos Web operations.
 * Pagos Web is supported by Bamboo Payments.
 * These operations are used from Public-Site or Mobile-App clients.
 */
public class PagosWebContoller extends TransactionBaseController {

    /**
     * POST  /api/v1/transaction/pw/confirm
     */
    @Tx
    public Result confirm() throws APIException {
        PagosWebTransactionFlow service = new PagosWebTransactionFlow();
        Map<String, String[]> body = request().body().asFormUrlEncoded();
        service.logger("BODY_PW_CONFIRM: {} ", request().body().asJson());

        String codigoAuth = "";
        String message = "";
        boolean is_valid = false;
        String customerTransactionId = "";
        boolean is_confirmed = false;

        try {
            codigoAuth = body.get("codigoAutorizacion")[0];
            message = body.get("mensaje")[0];
            customerTransactionId = body.get("numeroOrden")[0];
            service.logger(
                "BODY_PW_CONFIRM_parameters: codigoAuth: {} - message: {} - customerTransactionId: {} ",
                codigoAuth,
                message,
                customerTransactionId
            );
        } catch (Exception e) {
            service.handleException(e);
        }

        try {
            is_valid = service.checkPagosWebTransaction(body);
            if (is_valid) {
                if (codigoAuth.equals("0") || message.equals("Venta Aprobada")) {
                    is_confirmed = true;
                    service.confirmTransaction(customerTransactionId);
                } else {
                    if (!message.equals("Venta Pendiente")) {
                        service.cancelTransaction(customerTransactionId, message);
                    } else {
                        Transaction transaction = getTransaction(customerTransactionId);
                        transaction.setAuthorizationresultmessage("Venta Pendiente");
                    }
                }
            } else {
                throw APIException
                    .raise(APIException.APIErrors.MISSING_PARAMETERS)
                    .setDetailMessage("No paso chequeo de veracidad de PagosWeb");
            }
        } catch (Exception e) {
            service.handleException(e);
        }

        if (is_confirmed) {
            return ok(
                views.html.generalMessage.render(
                    "Su transacción fue realizada con éxito!",
                    " estimado Sr. Cliente"
                )
            );
        } else {
            return ok(
                views.html.generalMessage.render(
                    "Ha ocurrido un error y su transacción no pude ser realizada.",
                    "estimado Sr. Cliente"
                )
            );
        }
    }

    /**
     * POST  /api/v1/transaction/pw/notify
     */
    @Tx
    public F.Promise<Result> notifyTx() throws APIException {
        PagosWebTransactionFlow service = new PagosWebTransactionFlow();
        service.logger("BODY_PW_CONFIRM: {} ", request().body().asJson());

        String codigoAuth = "";
        String message = "";
        boolean isValid = false;
        String customerTransactionId = "";

        Map<String, String[]> body = request().body().asFormUrlEncoded();
        try {
            codigoAuth = body.get("codigoAutorizacion")[0];
            message = body.get("mensaje")[0];
            customerTransactionId = body.get("numeroOrden")[0];
        } catch (Exception e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.INTERNAL_SERVER_ERROR, e)
                .withParentDetailMessage()
                .withParentStackTrace();
        }

        try {
            isValid = service.checkPagosWebTransaction(body);

            if (isValid) {
                if (
                    codigoAuth.equals("0") ||
                    message.equals("Venta Aprobada") ||
                    message.equals("Transaccion Aprobada")
                ) {
                    service.notifyTransaction("payment_pagos_web", customerTransactionId);
                } else {
                    service.cancelTransaction(customerTransactionId, message);
                }
            } else {
                throw APIException
                    .raise(APIException.APIErrors.MISSING_PARAMETERS)
                    .setDetailMessage("No paso chequeo de veracidad de PagosWeb");
            }
        } catch (Exception e) {
            service.handleException(e);
        }

        return F.Promise.<Result>pure(ok());
    }
}
