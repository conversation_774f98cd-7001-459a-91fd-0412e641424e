package domains.payment_gateways.controllers.v1.mercado_pago;

import com.play4jpa.jpa.db.Tx;
import domains.payment_gateways.controllers.v1.PaymentGatewaysBaseController;
import dto.store.CreateStoreParameters;
import global.APIException;
import global.exceptions.BuildingNotFoundException;
import java.io.IOException;
import models.Building;
import models.Machine;
import models.POS;
import models.Store;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import play.libs.F;
import play.mvc.Result;
import play.mvc.Security;
import policies.actions.Policy;
import policies.stores.PoSPolicy;
import policies.stores.StorePolicy;
import security.v1.Secured;
import utils.ApplicationConfiguration;
import utils.HttpRequest;

@Security.Authenticated(Secured.class)
public class StoreController extends PaymentGatewaysBaseController {

    /**
     * POST /api/v1/mercado-pago/store
     *
     * Registers a building as a MercadoPago's store.
     * All laundromats have to be registered in this way.
     *
     */
    @Tx
    @Policy(StorePolicy.class)
    public F.Promise<Result> create() throws APIException, IOException, JSONException {
        this.getAllowedPolicy().creation();

        String buildingId = safeString("building_id", body());
        String streetName = safeString("street_name", body());
        String streetNumber = safeString("street_number", body());
        String cityName = safeString("city_name", body());
        Double latitude = safeDouble("latitude", body());
        Double longitude = safeDouble("longitude", body());
        String zipCode = safeString("zip_code", body());
        String details = safeString("details", body());

        Building building = Building.findById(Integer.parseInt(buildingId));
        if (building == null) {
            throw new BuildingNotFoundException();
        }

        Store store = new Store(
            building,
            streetName,
            streetNumber,
            zipCode,
            latitude,
            longitude,
            details
        );
        store.save();
        store.setExternalId("Store_" + store.getId());
        store.update();

        JSONObject location = new JSONObject();
        location.put("zip_code", zipCode);
        location.put("street_number", streetNumber);
        location.put("street_name", streetName);
        location.put("city_name", StringUtils.isNotBlank(cityName) ? cityName : building.getCity());
        location.put("state_name", building.getDepartment());
        location.put("latitude", latitude);
        location.put("longitude", longitude);
        location.put("reference", details);

        JSONObject body = new JSONObject();
        body.put("name", building.getName());
        body.put("location", location);
        body.put("external_id", store.getExternalId());

        play.Logger.debug("JSON >>> {}", body.toString());

        // doc:
        // https://www.mercadopago.com.uy/developers/en/reference/stores/_users_user_id_stores/post
        HttpRequest request = new HttpRequest(
            "https://api.mercadopago.com/users/" +
            ApplicationConfiguration.getMercadoPagoUserId() +
            "/stores?access_token=" +
            ApplicationConfiguration.getMercadoPagoAccessToken()
        )
            .headers("accept", "application/json", "content-type", "application/json; utf-8")
            .post(body);

        JSONObject responseBody = request.json();
        play.Logger.debug("RESPONSE >>> {}", responseBody.toString());

        if (request.errored()) {
            throw new RuntimeException("Failed : HTTP error code : " + request.status());
        }

        store.setMPGeneratedId(responseBody.getString("id"));
        store.update();

        JSONObject response = new JSONObject();
        response.put("result", responseBody.toString());

        return json(response);
    }

    /**
     * POST /api/v1/mercado-pago/store/pos
     *
     * Registers a machine as a MercadoPago's Point of Sale (POS)
     * All laundromats' machines have to be registered in this way.
     *
     */
    @Tx
    @Policy(PoSPolicy.class)
    public F.Promise<Result> createPoS() throws APIException, IOException, JSONException {
        this.getAllowedPolicy().creation();

        CreateStoreParameters dto = new CreateStoreParameters(body()).validate();
        POS pos = dto.getPOS();
        pos.save();

        String externalId = dto.getMachine().getSerialNumber();
        String server = request().host();
        String order_url = "https://" + server + "/api/v1/qr/order?pos=" + externalId;
        pos.setExternalId(externalId);
        pos.setURL(order_url);

        JSONObject body = new JSONObject();
        body.put("name", pos.getName());
        body.put("fixed_amount", pos.getFixedAmount());
        body.put("store_id", dto.getStore().getMPGeneratedId());
        body.put("external_id", externalId);
        body.put("url", order_url);

        // DOC: https://www.mercadopago.com.uy/developers/en/reference/pos/_pos/post
        HttpRequest request = new HttpRequest(
            "https://api.mercadopago.com/pos?access_token=" +
            ApplicationConfiguration.getMercadoPagoAccessToken()
        )
            .headers("accept", "application/json", "content-type", "application/json; utf-8")
            .post(body);

        JSONObject responseBody = request.json();
        play.Logger.debug("RESPONSE >>> " + responseBody.toString());

        if (request.errored()) {
            throw new RuntimeException("Failed : HTTP error code : " + request.status());
        }

        pos.setMPPosId(responseBody.getString("id"));
        JSONObject qr = new JSONObject(responseBody.getString("qr"));
        pos.setQrImage(qr.getString("image"));
        pos.setQrTemplateDoc(qr.getString("template_document"));
        pos.setQrTemplateImage(qr.getString("template_image"));

        pos.update();

        JSONObject result = new JSONObject();
        result.put("result", responseBody.toString());
        return json(result);
    }
}
