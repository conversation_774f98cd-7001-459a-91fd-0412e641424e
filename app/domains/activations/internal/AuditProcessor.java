package domains.activations.internal;

import domains.activations.Channel;
import java.util.Date;
import models.AuditWorkflow;
import models.Role;
import models.Transaction;
import models.User;

/**
 * AuditProcessor is responsible for managing the audit trail of activation processes.
 * It initializes the audit and keeps it updates along the activation process.
 */
class AuditProcessor {

    private final AuditWorkflow audit;

    AuditProcessor() {
        this.audit = new AuditWorkflow();
        this.audit.setUserId(0);
        this.audit.setGroupId(0);
    }

    /**
     * 1. Initializes the audit.
     */
    void init(Parameters params) {
        User user = params.getUser();
        String channel = params.getChannel();

        if (Channel.APPLICATION_CHANNEL.equals(channel)) {
            this.audit.setUserId(user.getId());
            this.audit.setGroupId(params.getGroupId());
        } else if (Channel.WHATSAPP_CHANNEL.equals(channel)) {
            this.audit.setBotMachineActivation(true);
        } else if (Channel.ASSISTANT_CHANNEL.equals(channel) && Role.TOTEM != user.getRole()) {
            this.audit.setUserId(user.getId());
        }
    }

    /**
     * 2. Once activation is feasible, the details of the activation are set.
     */
    void validated(Parameters params) {
        Integer machineId = params.getMachine() != null ? params.getMachine().getId() : null;
        Integer buildingId = params.getBuilding() != null ? params.getBuilding().getId() : null;
        String cardUid = params.getCard() != null ? params.getCard().getUuid() : null;

        this.audit.setDetails(
                "Starting activation process for " +
                params.getChannel() +
                ": MachineID:" +
                machineId +
                "|" +
                "BuildingID:" +
                buildingId +
                "|" +
                "UUID:" +
                cardUid
            );
        this.audit.setStatus(AuditWorkflow.Status.IN_PROGRESS);
        this.audit.setDate(new Date());
        this.audit.save();
    }

    /**
     * 3. Before publishing the activation, store payment information.
     */
    void preparePublishing(Parameters params) {
        String channel = params.getChannel();
        if (Channel.TOTEM_CHANNEL.equals(channel) || Channel.QR_CHANNEL.equals(channel)) {
            Transaction transaction = params.getTransaction();
            if (transaction != null) {
                this.audit.setPaymentId(transaction.getId());
            }
        }
    }

    /**
     * 4. Init publishing activation.
     */
    void publishing() {
        AuditWorkflow audit = new AuditWorkflow(
            "Publishing Broker Message",
            AuditWorkflow.Status.IN_PROGRESS,
            this.audit.getId(),
            this.audit.getUser(),
            this.audit.getGroup()
        );
        audit.save();
    }

    /**
     * 5. If the publishing is successful.
     */
    void published() {
        AuditWorkflow audit = new AuditWorkflow(
            "Message published",
            AuditWorkflow.Status.IN_PROGRESS,
            this.audit.getId(),
            this.audit.getUser(),
            this.audit.getGroup()
        );
        audit.save();
    }

    /**
     * 6. If the publishing fails.
     */
    void failed(Exception ex) {
        AuditWorkflow audit = new AuditWorkflow(
            "Error Message was not published:" + ex.getCause(),
            AuditWorkflow.Status.MQTT_ERROR,
            this.audit.getId(),
            this.audit.getUser(),
            this.audit.getGroup()
        );
        audit.save();
    }

    /**
     * 7. If the activation at all fails.
     */
    void aborted(Exception ex) {
        AuditWorkflow audit = new AuditWorkflow(
            ex.getMessage(),
            AuditWorkflow.Status.ERROR,
            this.audit.getId()
        );
        audit.save();
    }

    public int getId() {
        return this.audit.getId();
    }
}
