package domains.activations;

import java.util.Arrays;
import java.util.List;

public class Channel {

    public static final String WHATSAPP_CHANNEL = "WP";
    public static final String APPLICATION_CHANNEL = "APP";
    public static final String TOTEM_CHANNEL = "TOTEM";
    public static final String QR_CHANNEL = "QR";
    public static final String RPI_CHANNEL = "RPI";
    public static final String ASSISTANT_CHANNEL = "ASSISTANT";

    /**
     * Channels that required the existence of a card to perform their checks
     */
    public static final List<String> CARD_DEPENDENT_CHANNELS = Arrays.asList(
        APPLICATION_CHANNEL,
        ASSISTANT_CHANNEL,
        RPI_CHANNEL,
        WHATSAPP_CHANNEL
    );
}
