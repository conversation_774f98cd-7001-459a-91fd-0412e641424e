package domains.activations.mqtt;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.function.Consumer;
import models.Machine;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import services.BaseService;
import utils.ApplicationConfiguration;

public class LMMQTTProducer extends BaseService {

    protected static final String BUILDING_TOPIC = "building_id/";
    protected static final String TEST_TOPIC = "test";
    protected static final int CONNECTION_TIMEOUT = 20; // ideal 10, default 30

    private Runnable onPublishing;
    private Runnable onPublished;
    private Consumer<Exception> onError;

    public LMMQTTProducer onPublishing(Runnable runnable) {
        this.onPublishing = runnable;

        return this;
    }

    public LMMQTTProducer onPublished(Runnable runnable) {
        this.onPublished = runnable;

        return this;
    }

    public LMMQTTProducer onError(Consumer<Exception> consumer) {
        this.onError = consumer;

        return this;
    }

    public String run(int buildingId, Machine machine, String json) {
        return runProducer(buildingId, machine, json);
    }

    private String runProducer(int buildingId, Machine machine, String json) {
        StringBuilder debuggerOutput = new StringBuilder();
        MqttClient mqttClient = null;

        try {
            String broker = ApplicationConfiguration.getMqttBrokerIp();
            String clientId = MqttClient.generateClientId();
            MemoryPersistence persistence = new MemoryPersistence();
            int qos = 2;
            String topic = getMqttTopic(machine, buildingId);

            debuggerOutput
                .append("Creating MQTT client with ID: ")
                .append(clientId)
                .append(System.lineSeparator());
            mqttClient = new MqttClient(broker, clientId, persistence);

            MqttConnectOptions connOpts = new MqttConnectOptions();
            connOpts.setCleanSession(true);
            connOpts.setConnectionTimeout(CONNECTION_TIMEOUT);
            connOpts.setAutomaticReconnect(false);

            debuggerOutput
                .append("Connecting to broker: ")
                .append(broker)
                .append(" with timeout: ")
                .append(CONNECTION_TIMEOUT)
                .append("s")
                .append(System.lineSeparator());

            mqttClient.connect(connOpts);
            debuggerOutput
                .append("Connected")
                .append(System.lineSeparator())
                .append("Publishing message: ")
                .append(json)
                .append(System.lineSeparator());

            if (this.onPublishing != null) {
                this.onPublishing.run();
            }

            MqttMessage message = new MqttMessage(json.getBytes(StandardCharsets.UTF_8));
            message.setQos(qos);
            mqttClient.publish(topic, message);
            debuggerOutput.append("Message published").append(System.lineSeparator());

            if (this.onPublished != null) {
                this.onPublished.run();
            }
        } catch (Exception ex) {
            debuggerOutput
                .append("Error Message was not published:")
                .append(System.lineSeparator())
                .append("- Message: ")
                .append(ex.getMessage())
                .append(System.lineSeparator());

            if (ex instanceof MqttException) {
                MqttException mex = (MqttException) ex;
                debuggerOutput
                    .append("- ReasonCode: ")
                    .append(mex.getReasonCode())
                    .append(System.lineSeparator())
                    .append("- LocalizedMessage: ")
                    .append(mex.getLocalizedMessage())
                    .append(System.lineSeparator())
                    .append("- MqttException: ")
                    .append(mex)
                    .append(System.lineSeparator());
            } else {
                debuggerOutput.append("- Exception: ").append(ex).append(System.lineSeparator());
            }

            debuggerOutput
                .append("- Cause: ")
                .append(ex.getCause())
                .append(System.lineSeparator())
                .append("- StackTrace: ")
                .append(Arrays.toString(ex.getStackTrace()))
                .append(System.lineSeparator());

            if (this.onError != null) {
                this.onError.accept(ex);
            }
        } finally {
            try {
                if (mqttClient != null) {
                    debuggerOutput
                        .append("Disconnecting MQTT client...")
                        .append(System.lineSeparator());
                    if (mqttClient.isConnected()) {
                        mqttClient.disconnect();
                        debuggerOutput
                            .append("MQTT client disconnected.")
                            .append(System.lineSeparator());
                    }
                    mqttClient.close();
                    debuggerOutput.append("MQTT client closed.").append(System.lineSeparator());
                }
            } catch (MqttException e) {
                debuggerOutput
                    .append("Error during client cleanup: ")
                    .append(e.getMessage())
                    .append(System.lineSeparator());
            }
        }

        logger(debuggerOutput.toString());
        return debuggerOutput.toString();
    }

    private String getMqttTopic(Machine machine, int buildingId) {
        String topic = BUILDING_TOPIC + buildingId;
        if (ApplicationConfiguration.isProd() && machine.getIsTopicEnable()) {
            return topic;
        }

        logger(
            "Sending messages to \"{}\" topic is disabled for non production environments. It will be send to \"{}\" topic instead.",
            topic,
            TEST_TOPIC
        );
        return TEST_TOPIC;
    }

    @Override
    protected String getLoggerClassSuffix() {
        // Remove the suffix replacement since it's wanted to print the whole
        // name of the class.
        return "";
    }
}
