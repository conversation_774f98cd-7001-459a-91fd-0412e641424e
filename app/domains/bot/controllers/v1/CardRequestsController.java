package domains.bot.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.bot.dto.cards.GetRequestCardAcceptanceParameters;
import domains.bot.dto.cards.GetRequestCardParameters;
import domains.bot.services.administrations.ContactAdministrationService;
import play.libs.F;
import play.mvc.Result;
import policies.CardRequestPolicy;
import policies.actions.Policy;

@Policy(CardRequestPolicy.class)
public class CardRequestsController extends BotBaseController {

    @Tx
    public F.Promise<Result> requestCard(Integer buildingId) throws Exception {
        CardRequestPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        GetRequestCardParameters params = new GetRequestCardParameters(buildingId, body())
            .validate();

        ContactAdministrationService service = new ContactAdministrationService();
        service.requestNewCard(
            params.getEmail(),
            params.getFirstName(),
            params.getLastName(),
            params.getPhone(),
            params.getBuilding(),
            params.getUnit(),
            params.getUnitNumber(),
            params.getTower(),
            request().host()
        );

        return F.Promise.<Result>pure(noContent());
    }

    @Tx
    public F.Promise<Result> requestCardAcceptance(Integer buildingId) throws Exception {
        CardRequestPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        GetRequestCardAcceptanceParameters params = new GetRequestCardAcceptanceParameters(
            buildingId,
            body()
        )
            .validate();

        ContactAdministrationService service = new ContactAdministrationService();
        service.requestCardActivation(
            params.getUid(),
            params.getEmail(),
            params.getFirstName(),
            params.getLastName(),
            params.getPhone(),
            params.getBuilding(),
            params.getUnit(),
            params.getTower(),
            params.getCard(),
            request().host()
        );

        return F.Promise.<Result>pure(noContent());
    }
}
