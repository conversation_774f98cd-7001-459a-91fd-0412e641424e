package domains.bot.dto.cards;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.APIErrors;
import models.Card;

public class GetHistoryParameters extends dto.JsonBodyActionParameters {

    protected String uid;
    protected Card card;
    protected String email;

    public GetHistoryParameters() {}

    public GetHistoryParameters(String uid, JsonNode body) throws APIException {
        this.uid = uid;

        email = safeString("email", body, "");
    }

    public GetHistoryParameters validate() throws APIException {
        if (uid == null || uid.isEmpty() || email.isEmpty()) throw APIException.raise(
            APIErrors.MISSING_PARAMETERS
        );

        this.uid = utils.CardHelper.sanitizeUid(uid);

        this.card = Card.findByUID(uid);

        if (card == null) throw APIException.raise(APIErrors.CARD_NOT_FOUND);

        return this;
    }

    public String getUid() {
        return uid;
    }

    public Card getCard() {
        return card;
    }

    public String getEmail() {
        return email;
    }
}
