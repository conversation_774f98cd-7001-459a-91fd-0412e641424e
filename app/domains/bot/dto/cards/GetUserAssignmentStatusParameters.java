package domains.bot.dto.cards;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.APIErrors;

public class GetUserAssignmentStatusParameters extends dto.JsonBodyActionParameters {

    protected String uid;
    protected String email;

    public GetUserAssignmentStatusParameters() {}

    public GetUserAssignmentStatusParameters(String uid, JsonNode body) throws APIException {
        this.uid = uid;
        email = safeString("email", body, "");
    }

    public GetUserAssignmentStatusParameters validate() throws APIException {
        if (uid == null || uid.isEmpty() || email.isEmpty()) throw APIException.raise(
            APIErrors.MISSING_PARAMETERS
        );

        this.uid = utils.CardHelper.sanitizeUid(uid);

        return this;
    }

    public String getUid() {
        return uid;
    }

    public String getEmail() {
        return email;
    }
}
