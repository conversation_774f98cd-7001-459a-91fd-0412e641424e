package domains.public_site.dto.cards;

import domains.payment_gateways.services.cards.RechargeableCardValidationService;
import global.APIException;
import global.exceptions.CardNotFoundException;
import global.exceptions.MissingParametersException;
import models.Building;
import models.Card;
import models.Country;
import models.Unit;
import org.apache.commons.lang3.StringUtils;

public class CardPaymentMethodsParameters extends dto.ActionParameters {

    protected String uuid;
    protected Card card;

    public CardPaymentMethodsParameters(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public CardPaymentMethodsParameters validate() throws APIException {
        if (StringUtils.isBlank(this.uuid)) {
            throw new MissingParametersException("uuid");
        }

        this.uuid = utils.CardHelper.sanitizeUid(this.uuid);
        this.card = Card.findByUID(this.uuid);

        if (this.card == null) {
            throw new CardNotFoundException();
        }

        new RechargeableCardValidationService(this.card).validate();

        return this;
    }

    public Card getCard() {
        return this.card;
    }

    public Country getCountry() throws APIException {
        if (getBuilding() != null && Country.PARAGUAY.equals(getBuilding().getCountry())) {
            return Country.PARAGUAY;
        }

        return Country.URUGUAY;
    }

    public Building getBuilding() throws APIException {
        return this.card.getUnit() != null ? this.card.getBuilding() : null;
    }
}
