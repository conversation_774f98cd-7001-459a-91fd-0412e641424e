package domains.public_site.serializers.cards;

import global.APIException;
import java.util.List;
import models.Rate;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class PaymentMethodSerializer {

    public static JSONObject toJson(
        List<String> paymentMethods,
        Rate regularRate,
        Rate specialRate
    ) throws APIException {
        JSONObject json = new JSONObject();

        JSONArray paymentMethodsJson = new JSONArray();
        for (String name : paymentMethods) {
            paymentMethodsJson.put(methodToJson(name));
        }

        try {
            json.put("payment_methods", paymentMethodsJson);

            if (regularRate != null) {
                json.put("price_customer", regularRate.getPriceCustomer());
            }

            if (specialRate != null) {
                json.put("machine_rate", specialRate.getPriceCustomer());
                json.put("special_rate_message", specialRate.getDescriptiveMessage());
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing payment method : " + e.getMessage());
        }

        return json;
    }

    private static JSONObject methodToJson(String name) throws APIException {
        JSONObject methodJson = new JSONObject();

        try {
            methodJson.put("name", name);

            return methodJson;
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing payment method : " + e.getMessage());
        }
    }
}
