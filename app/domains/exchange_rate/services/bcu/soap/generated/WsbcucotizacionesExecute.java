package domains.exchange_rate.services.bcu.soap.generated;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Entrada" type="{Cotiza}wsbcucotizacionesin"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "entrada" })
@XmlRootElement(name = "wsbcucotizaciones.Execute")
public class WsbcucotizacionesExecute {

    @XmlElement(name = "Entrada", required = true)
    protected Wsbcucotizacionesin entrada;

    /**
     * Gets the value of the entrada property.
     *
     * @return
     *     possible object is
     *     {@link Wsbcucotizacionesin }
     *
     */
    public Wsbcucotizacionesin getEntrada() {
        return entrada;
    }

    /**
     * Sets the value of the entrada property.
     *
     * @param value
     *     allowed object is
     *     {@link Wsbcucotizacionesin }
     *
     */
    public void setEntrada(Wsbcucotizacionesin value) {
        this.entrada = value;
    }
}
