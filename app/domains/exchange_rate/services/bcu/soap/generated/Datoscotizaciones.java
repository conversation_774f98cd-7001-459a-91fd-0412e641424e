package domains.exchange_rate.services.bcu.soap.generated;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>Java class for datoscotizaciones complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="datoscotizaciones">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="datoscotizaciones.dato" type="{Cotiza}datoscotizaciones.dato" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "datoscotizaciones", propOrder = { "datoscotizacionesDato" })
public class Datoscotizaciones {

    @XmlElement(name = "datoscotizaciones.dato")
    protected List<DatoscotizacionesDato> datoscotizacionesDato;

    /**
     * Gets the value of the datoscotizacionesDato property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the datoscotizacionesDato property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDatoscotizacionesDato().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DatoscotizacionesDato }
     *
     *
     */
    public List<DatoscotizacionesDato> getDatoscotizacionesDato() {
        if (datoscotizacionesDato == null) {
            datoscotizacionesDato = new ArrayList<DatoscotizacionesDato>();
        }
        return this.datoscotizacionesDato;
    }
}
