package domains.assistant.controllers.v1.users;

import com.play4jpa.jpa.db.Tx;
import domains.assistant.controllers.v1.AssistantBaseController;
import domains.assistant.dto.cards.AssignCardParameters;
import domains.assistant.dto.cards.UnassignCardParameters;
import domains.assistant.serializers.CardSerializer;
import global.APIException;
import models.User;
import play.libs.F;
import play.mvc.Result;
import play.mvc.Security;
import policies.CardPolicy;
import policies.Level;
import policies.actions.Policy;
import queries.cards.CardAssociatedToUserQuery;
import security.v1.Secured;
import services.card.AssignationUserService;

@Policy(CardPolicy.class)
@Security.Authenticated(Secured.class)
public class CardsController extends AssistantBaseController {

    @Tx(readOnly = true)
    public F.Promise<Result> list() throws APIException {
        CardPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.list();

        User user = allowedPolicy.getRequesterUser();
        CardAssociatedToUserQuery query = new CardAssociatedToUserQuery(user);

        return json(new CardSerializer(query.find()).toJsonList(queryLevel(Level.MINIMUM_LEVEL)));
    }

    @Tx
    public F.Promise<Result> assign() throws APIException {
        CardPolicy allowedPolicy = this.getAllowedPolicy();

        AssignCardParameters params = new AssignCardParameters(
            allowedPolicy.getRequesterUser(),
            body()
        );

        allowedPolicy.update(params.getCard());

        params.validate();

        AssignationUserService service = new AssignationUserService(
            params.getCard(),
            allowedPolicy.getRequesterUser()
        );
        service.assign(params.getAlias());

        return F.Promise.pure(created());
    }

    @Tx
    public F.Promise<Result> unassign(String uid) throws APIException {
        CardPolicy allowedPolicy = this.getAllowedPolicy();

        UnassignCardParameters params = new UnassignCardParameters(
            allowedPolicy.getRequesterUser(),
            uid
        );

        allowedPolicy.update(params.getCard());

        params.validate();

        AssignationUserService service = new AssignationUserService(
            params.getCard(),
            allowedPolicy.getRequesterUser()
        );
        service.unassign();

        return F.Promise.pure(noContent());
    }
}
