package domains.assistant.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.assistant.dto.buildings.GetBuildingBySlugParameters;
import domains.assistant.serializers.BuildingSerializer;
import domains.assistant.serializers.buildings.PricingRuleSerializer;
import global.APIException;
import java.util.List;
import models.Building;
import play.libs.F;
import play.libs.F.Promise;
import play.mvc.Result;
import policies.Level;
import policies.actions.Policy;
import policies.buildings.BuildingPolicy;
import queries.buildings.BuildingQuery;
import queries.soap_dispensers.SoapDispenserQuery;

@Policy(BuildingPolicy.class)
public class BuildingsController extends AssistantBaseController {

    @Tx(readOnly = true)
    public Promise<Result> info(String slug) throws APIException {
        BuildingPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.get();

        GetBuildingBySlugParameters params = new GetBuildingBySlugParameters(slug).validate();

        return json(
            new BuildingSerializer(params.getBuilding())
                .append(
                    BuildingSerializer.HAS_SOAP_DISPENSERS_ATTRIBUTE,
                    b -> new SoapDispenserQuery().filterByBuildingId(b.getId()).any()
                )
                .toJson(queryLevel(1))
        );
    }

    @Tx(readOnly = true)
    public Promise<Result> laundromats() throws APIException {
        BuildingPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.list();

        List<Building> buildings = new BuildingQuery().filterLaundromats().find();
        return json(new BuildingSerializer(buildings).toJsonList(queryLevel(1)));
    }

    @Tx(readOnly = true)
    public F.Promise<Result> pricing(String slug) throws APIException {
        BuildingPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.get();

        GetBuildingBySlugParameters params = new GetBuildingBySlugParameters(slug).validate();
        services.building.BuildingPricingService service = new services.building.BuildingPricingService(
            params.getBuilding()
        );

        return json(
            new PricingRuleSerializer(service.getPricingRules())
                .toJsonList(queryLevel(Level.MINIMAL_MEDIUM_LEVEL))
        );
    }
}
