package domains.assistant.serializers;

import java.util.List;
import models.Machine;
import org.json.JSONObject;
import policies.Level;
import serializers.BaseSerializer;

public class MachineSerializer extends BaseSerializer<Machine> {

    public static final String STATUS_ATTRIBUTE = "status";
    public static final String REMAINING_TIME_ATTRIBUTE = "remainingTime";
    public static final String HAS_SOAP_DISPENSERS_ATTRIBUTE = "hasSoapDispensers";

    public MachineSerializer(Machine entity) {
        super(entity);
    }

    public MachineSerializer(List<Machine> entities) {
        super(entities, "machines");
    }

    @Override
    protected JSONObject itemToJson(Machine entity, int level) throws Throwable {
        JSONObject json = new JSONObject();

        json.put("index", entity.getSortIndex());

        if (level >= Level.MINIMAL_MEDIUM_LEVEL) {
            json.put("type", entity.getMachineType());
            json.put("capacity", entity.getCapacity());

            this.setAdditionalParam(json, entity, STATUS_ATTRIBUTE);
            this.setAdditionalParam(json, entity, REMAINING_TIME_ATTRIBUTE);
            this.setAdditionalParam(json, entity, HAS_SOAP_DISPENSERS_ATTRIBUTE);

            json.put(
                "rate",
                new RateSerializer(entity.getMachineRate()).toJson(Level.MINIMAL_MEDIUM_LEVEL)
            );
        }

        return json;
    }
}
