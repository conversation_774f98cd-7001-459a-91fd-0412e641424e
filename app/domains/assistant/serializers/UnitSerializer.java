package domains.assistant.serializers;

import java.util.List;
import models.Unit;
import org.json.JSONObject;
import serializers.BaseSerializer;

public class UnitSerializer extends BaseSerializer<Unit> {

    public UnitSerializer(Unit entity) {
        super(entity);
    }

    public UnitSerializer(List<Unit> entities) {
        super(entities, "units");
    }

    @Override
    protected JSONObject itemToJson(Unit unit, int level) throws Throwable {
        JSONObject json = new JSONObject();

        json.put("number", unit.getNumber());

        return json;
    }
}
