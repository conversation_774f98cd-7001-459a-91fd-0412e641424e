package domains.assistant.serializers;

import java.util.List;
import org.json.JSONObject;
import serializers.BaseSerializer;
import utils.WebReportWrapper;

public class WebReportItemWrapperSerializer
    extends BaseSerializer<WebReportWrapper.WebReportItemWrapper> {

    public WebReportItemWrapperSerializer(List<WebReportWrapper.WebReportItemWrapper> entities) {
        super(entities, "activity");
    }

    @Override
    protected JSONObject itemToJson(WebReportWrapper.WebReportItemWrapper entity, int level)
        throws Throwable {
        JSONObject json = new JSONObject();

        json.put("timestamp", entity.getTimestamp().getTime());
        json.put("type", entity.getType());
        json.put("amount", entity.getPrice());

        return json;
    }
}
