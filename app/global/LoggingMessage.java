package global;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import play.mvc.With;

@With(LoggingMessageHandler.class)
@Target({ ElementType.TYPE, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface LoggingMessage {
    boolean auth() default false;

    boolean queryString() default true;

    boolean body() default true;

    boolean headers() default false;
}
