package global;

import play.Play;
import play.libs.F.Promise;
import play.mvc.Action;
import play.mvc.Controller;
import play.mvc.Http.Context;
import play.mvc.Http.Request;
import play.mvc.Result;

public class ForceHttps extends Action<Controller> {

    private static final String SSL_HEADER = "X-Forwarded-Proto";
    private static final String FORWARD_HEADER = "X-Forwarded-For";

    @Override
    public Promise<Result> call(Context ctx) throws Throwable {
        final Promise<Result> result;
        String host = ctx.request().host();

        if (Play.isProd() && !isHttpsRequest(ctx.request()) && isForwardedRequest(ctx.request())) {
            result = Promise.<Result>pure(redirect("https://" + host + ctx.request().uri()));
        } else {
            result = this.delegate.call(ctx);
        }
        return result;
    }

    private static boolean isHttpsRequest(Request request) {
        return (
            request.getHeader(SSL_HEADER) != null && request.getHeader(SSL_HEADER).contains("https")
        );
    }

    private static boolean isForwardedRequest(Request request) {
        return request.getHeader(FORWARD_HEADER) != null;
    }
}
