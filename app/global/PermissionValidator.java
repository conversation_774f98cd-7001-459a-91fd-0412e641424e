package global;

import models.Role;
import models.User;
import play.libs.F.Promise;
import play.mvc.Http.Context;
import play.mvc.Result;

public class PermissionValidator {

    public interface PromiseCallback {
        public Promise<Result> execute() throws APIException;
    }

    public static Promise<Result> runIfHasRole(PromiseCallback executionBlock, Role... roles)
        throws APIException {
        return runIfHasRole(null, executionBlock, roles);
    }

    @Deprecated
    public static Promise<Result> runIfHasRole(
        Context context,
        PromiseCallback executionBlock,
        Role... roles
    ) throws APIException {
        User sessionUser = Global.getSessionUser(Context.current());
        for (Role role : roles) {
            if (sessionUser.getRole() == (role)) {
                return executionBlock.execute();
            }
        }

        throw APIException
            .raise(APIException.APIErrors.AUTHORIZATION_FAILED)
            .setDetailMessage("Operation not permitted");
    }
}
