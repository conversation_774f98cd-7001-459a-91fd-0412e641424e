package queries.cards;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import models.Card;
import models.Unit;
import models.User;
import queries.BaseGenericQuery;

public class CardAssociatedToUserQuery extends BaseGenericQuery {

    private final User user;

    public CardAssociatedToUserQuery(User user) {
        this.user = user;
    }

    @Override
    public List<Card> find() {
        List<Card> associatedCards = new ArrayList<>();

        List<Card> virtualCards = Card.findByCardHolderPrepaidIdList(this.user.getId());
        if (!virtualCards.isEmpty()) {
            associatedCards.addAll(virtualCards);
        }

        List<Unit> associatedUnits = Unit.getAccreditatedUnitsByOwnerId(this.user);
        for (Unit unit : associatedUnits) {
            associatedCards.addAll(unit.getAssignedCards());
        }

        return associatedCards.stream().distinct().collect(Collectors.toList());
    }
}
