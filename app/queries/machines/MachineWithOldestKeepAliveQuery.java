package queries.machines;

import java.util.List;
import models.Machine;
import play.db.jpa.JPA;
import queries.BaseGenericQuery;

public class MachineWithOldestKeepAliveQuery extends BaseGenericQuery {

    private final int buildingId;

    public MachineWithOldestKeepAliveQuery(int buildingId) {
        this.buildingId = buildingId;
    }

    @Override
    public Machine get() {
        try {
            return (Machine) JPA
                .em()
                .createQuery(
                    "SELECT m " +
                    "FROM models.Machine m " +
                    "WHERE m.building.id = :buildingId " +
                    "AND m.lastAlive IS NOT NULL " +
                    "AND m.sortIndex > 0 " +
                    "ORDER BY m.lastAlive ASC",
                    Machine.class
                )
                .setMaxResults(1)
                .setParameter("buildingId", this.buildingId)
                .getSingleResult();
        } catch (Exception e) {
            play.Logger.error(
                "An error happened while getting the machine with oldest keep alive of the building {} - error: {}",
                this.buildingId,
                e.getMessage()
            );
        }

        return null;
    }
}
