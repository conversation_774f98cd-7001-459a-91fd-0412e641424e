package queries.mappings;

import java.util.List;
import models.TotemUserPosTerminalMap;
import queries.BaseModelQuery;

public class TotemPosTerminalQuery extends BaseModelQuery<TotemUserPosTerminalMap> {

    protected static class Column extends BaseColumn {

        public static final String TOTEM_USER_ID = "totemUser.id";
    }

    public TotemPosTerminalQuery() {
        super(TotemUserPosTerminalMap.class);
    }

    public TotemPosTerminalQuery filterByTotemUserId(int totemUserId) {
        if (totemUserId > 0) {
            query().join("totemUser").eq(TotemPosTerminalQuery.Column.TOTEM_USER_ID, totemUserId);
        }

        return this;
    }

    public TotemPosTerminalQuery filterByTotemUserIds(List<Integer> totemUserIds) {
        if (!totemUserIds.isEmpty()) {
            query().join("totemUser").in(TotemPosTerminalQuery.Column.TOTEM_USER_ID, totemUserIds);
        }

        return this;
    }
}
