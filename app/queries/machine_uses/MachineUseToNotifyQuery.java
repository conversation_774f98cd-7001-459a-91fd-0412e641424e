package queries.machine_uses;

import java.util.List;
import models.MachineUse;
import org.joda.time.DateTime;
import play.db.jpa.JPA;
import utils.DateHelper;

public class MachineUseToNotifyQuery {

    public MachineUse getMachineUseToNotify(int buildingId, int machineUseId) {
        return (MachineUse) JPA
            .em()
            .createNativeQuery(
                "SELECT mu.* " +
                "FROM machine_use mu " +
                "WHERE mu.result IN ( 0, 1, 5, 6, 7, 8, 30 ) " +
                "AND mu.building_id = :buildingId " +
                "AND mu.id = :machineUseId ",
                MachineUse.class
            )
            .setParameter("buildingId", buildingId)
            .setParameter("machineUseId", machineUseId)
            .getSingleResult();
    }

    public List<MachineUse> getMachineUsesToNotify(int buildingId, DateTime from, DateTime to) {
        return (List<MachineUse>) JPA
            .em()
            .createNativeQuery(
                "SELECT mu.* " +
                "FROM machine_use mu " +
                "LEFT JOIN audit_workflow a " +
                "ON mu.transaction_id = a.transaction_id " +
                "LEFT JOIN transaction t " +
                "ON a.payment_tx = t.id " +
                "LEFT JOIN bill b " +
                "ON t.bill_id = b.id " +
                "LEFT JOIN part c " +
                "ON mu.card_id = c.id " +
                "LEFT JOIN external_sale_notification_record esnr " +
                "ON mu.id = esnr.machineuse_id " +
                "AND esnr.building_id = :buildingId " +
                "WHERE mu.timestamp >= :from " +
                "AND mu.timestamp <= :to " +
                "AND mu.result IN ( 0, 1, 5, 6, 7, 8, 30 ) " +
                "AND ( c.master IS NULL OR c.master = 0 ) " +
                "AND mu.building_id = :buildingId " +
                "AND esnr.id IS NULL " +
                "AND b.id IS NULL ",
                MachineUse.class
            )
            .setParameter("buildingId", buildingId)
            .setParameter("from", DateHelper.dateAndTimeDashFormatter.print(from))
            .setParameter("to", DateHelper.dateAndTimeDashFormatter.print(to))
            .getResultList();
    }
}
