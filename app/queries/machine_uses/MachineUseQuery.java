package queries.machine_uses;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import models.Bill;
import models.Machine;
import models.MachineUse;
import models.MachineUseResult;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;
import org.hibernate.type.StandardBasicTypes;
import queries.BaseModelQuery;
import utils.StringHelper;

public class MachineUseQuery extends BaseModelQuery<MachineUse> {

    protected static class Column extends BaseColumn {

        public static final String TIMESTAMP = "timestamp";
        public static final String MACHINE_ID = "machine.id";
        public static final String MACHINE_TYPE = "machine.machineType";
        public static final String RESULT = "result";
        public static final String BUILDING_ID = "building.id";
        public static final String UNIT_ID = "unit.id";
        public static final String CARD_UUID = "card.uuid";
        public static final String CARD_ID = "card.id";
        public static final String KEYWORD = "keyword";
    }

    public MachineUseQuery() {
        super(MachineUse.class);
    }

    public MachineUseQuery filterByTimestamp(Date timestamp) {
        try {
            filterByDate(Column.TIMESTAMP, timestamp);
        } catch (ParseException ignored) {}

        return this;
    }

    public MachineUseQuery filterByPeriod(Date from, Date to) {
        try {
            filterByDates(Column.TIMESTAMP, from, to);
        } catch (ParseException ignored) {}

        return this;
    }

    public MachineUseQuery filterByMachineId(int machineId) {
        if (machineId > 0) {
            query().join("machine").eq(Column.MACHINE_ID, machineId);
        }

        return this;
    }

    public MachineUseQuery filterByMachineIds(List<Integer> machineIds) {
        if (!machineIds.isEmpty()) {
            query().join("machine").in(Column.MACHINE_ID, machineIds);
        }

        return this;
    }

    public MachineUseQuery filterByBuildingId(int buildingId) {
        if (buildingId > 0) {
            query().join("building").eq(Column.BUILDING_ID, buildingId);
        }

        return this;
    }

    public MachineUseQuery filterOnlyWashers() {
        query().join("machine").eq(Column.MACHINE_TYPE, Machine.MachineType.WASHER);

        return this;
    }

    public MachineUseQuery filterOnlyDryers() {
        query().join("machine").eq(Column.MACHINE_TYPE, Machine.MachineType.DRYER);

        return this;
    }

    public MachineUseQuery filterByResult(MachineUseResult... results) {
        if (results != null && results.length > 0) {
            List<String> resultsCodes = Arrays
                .stream(results)
                .map(x -> x.getCode().toString())
                .collect(Collectors.toList());

            query().in(Column.RESULT, resultsCodes);
        }

        return this;
    }

    public MachineUseQuery filterByAccreditedResult() {
        this.filterByResult(
                MachineUse.ACCREDITED_RESULT.toArray(
                    new MachineUseResult[MachineUse.ACCREDITED_RESULT.size()]
                )
            );

        return this;
    }

    public MachineUseQuery filterByCardId(int cardId) {
        if (cardId > 0) {
            query().join("card").eq(Column.CARD_ID, cardId);
        }

        return this;
    }

    public MachineUseQuery filterByCardUUID(String cardUUID) {
        if (!StringHelper.isBlank(cardUUID)) {
            query().join("card").eq(Column.CARD_UUID, cardUUID);
        }

        return this;
    }

    public MachineUseQuery filterByUnitId(int unitId) {
        if (unitId > 0) {
            query()
                .getCriteria()
                .createAlias("card.unit", "unit")
                .add(Restrictions.eq(Column.UNIT_ID, unitId));
        }

        return this;
    }

    public MachineUseQuery filterByUnitIds(List<Integer> unitIds) {
        if (!unitIds.isEmpty()) {
            query()
                .getCriteria()
                .createAlias("card.unit", "unit")
                .add(Restrictions.in(Column.UNIT_ID, unitIds));
        }

        return this;
    }

    public MachineUseQuery filterByDayOfWeek(List<Integer> daysOfWeek) {
        if (!daysOfWeek.isEmpty()) {
            Criterion[] criterion = daysOfWeek
                .stream()
                .map(dayOfWeek ->
                    Restrictions.sqlRestriction(
                        "DAYOFWEEK({alias}.timestamp) = ?",
                        dayOfWeek,
                        StandardBasicTypes.INTEGER
                    )
                )
                .toArray(Criterion[]::new);
            query().getCriteria().add(Restrictions.or(criterion));
        }

        return this;
    }

    public MachineUseQuery orderByTimestampDesc() {
        query().orderByDesc(Column.TIMESTAMP);

        return this;
    }

    public MachineUseQuery filterByNotBilledOrHasCreditNote() {
        query()
            .getCriteria()
            .createAlias("bill", "mu_bill", JoinType.LEFT_OUTER_JOIN)
            .add(
                Restrictions.or(
                    Restrictions.isNull("mu_bill.id"),
                    Restrictions.or(
                        Restrictions.eq("mu_bill.billType", Bill.BillType.CREDITNOTE),
                        Restrictions.eq("mu_bill.creditNote", true)
                    )
                )
            );

        return this;
    }

    public MachineUseQuery filterForBillingByUnitId(
        Date from,
        Date to,
        int unitId,
        MachineUseResult... results
    ) {
        return this.filterByPeriod(from, to)
            .filterByUnitId(unitId)
            .filterByResult(results)
            .filterByNotBilledOrHasCreditNote();
    }

    public MachineUseQuery filterForBillingByBuildingId(
        Date from,
        Date to,
        int buildingId,
        MachineUseResult... results
    ) {
        return this.filterByPeriod(from, to)
            .filterByBuildingId(buildingId)
            .filterByResult(results)
            .filterByNotBilledOrHasCreditNote()
            .filterByNotCardMaster();
    }

    public MachineUseQuery filterByNotCardMaster() {
        query()
            .getCriteria()
            .createAlias("card", "mu_card", JoinType.LEFT_OUTER_JOIN)
            .add(
                Restrictions.or(
                    Restrictions.isNull("mu_card.id"),
                    Restrictions.eq("mu_card.master", false)
                )
            );

        return this;
    }
}
