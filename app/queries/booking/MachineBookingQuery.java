package queries.booking;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import models.MachineBooking;
import org.hibernate.criterion.Restrictions;
import queries.BaseModelQuery;

public class MachineBookingQuery extends BaseModelQuery<MachineBooking> {

    protected static class Column extends BaseColumn {

        public static final String PUBLIC_ID = "publicId";
        public static final String START_DATE = "startDate";
        public static final String STATUS = "status";

        public static final String MACHINE_ID = "machine.id";
    }

    public MachineBookingQuery() {
        super(MachineBooking.class);
    }

    public MachineBookingQuery filterByPublicId(String id) {
        if (id != null) {
            query().eq(Column.PUBLIC_ID, id);
        }

        return this;
    }

    public MachineBookingQuery filterByMachineId(int machineId) {
        if (machineId > 0) {
            query().join("machine").eq(Column.MACHINE_ID, machineId);
        }

        return this;
    }

    public MachineBookingQuery filterByBuildingId(int buildingId) {
        if (buildingId > 0) {
            query()
                .getCriteria()
                .createAlias("machine.building", "building")
                .add(Restrictions.eq("building.id", buildingId));
        }

        return this;
    }

    public MachineBookingQuery filterByStartDate(Date startDate) {
        if (startDate != null) {
            query().ge(Column.START_DATE, startDate);
        }

        return this;
    }

    public MachineBookingQuery excludeByStatus(
        final MachineBooking.MachineBookingStatus... statuses
    ) {
        this.query().notIn(Column.STATUS, Arrays.asList(statuses));
        return this;
    }
}
