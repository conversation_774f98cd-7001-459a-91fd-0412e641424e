package queries.buildings;

import java.util.Arrays;
import models.Building;
import models.BuildingType;
import models.Card;
import models.Role;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;
import queries.BaseModelQuery;

public class BuildingQuery extends BaseModelQuery<Building> {

    protected static class Column extends BaseColumn {

        public static final String BUILDING_TYPE = "buildingType";
        public static final String CONTRACT_TYPE = "contractType";
        public static final String SLUG = "slug";
    }

    public BuildingQuery() {
        super(Building.class);
    }

    public BuildingQuery filterByRateId(final int rateId) {
        this.query()
            .getCriteria()
            .createAlias("rate", "r", JoinType.INNER_JOIN)
            .add(Restrictions.eq("r.id", rateId));
        return this;
    }

    public BuildingQuery filterByContractTypes(final Card.ContractType... contractTypes) {
        this.query().in(Column.CONTRACT_TYPE, Arrays.asList(contractTypes));
        return this;
    }

    public BuildingQuery filterBySlug(String slug) {
        this.query().eq(Column.SLUG, slug);
        return this;
    }

    public BuildingQuery filterByBuildingType(final BuildingType... types) {
        this.query().in(Column.BUILDING_TYPE, Arrays.asList(types));
        return this;
    }

    public BuildingQuery filterLaundromats() {
        return this.filterByBuildingType(BuildingType.LAUNDROMAT);
    }

    public BuildingQuery filterByWithTotemUser() {
        this.query()
            .getCriteria()
            .createAlias("totemUsers", "u", JoinType.INNER_JOIN)
            .add(Restrictions.eq("u.role", Role.TOTEM));
        return this;
    }
}
