package queries.stores;

import models.Store;
import queries.BaseModelQuery;

public class StoreQuery extends BaseModelQuery<Store> {

    protected static class Column extends BaseColumn {

        public static final String BUILDING_ID = "building.id";
    }

    public StoreQuery() {
        super(Store.class);
    }

    public StoreQuery filterByBuildingId(int buildingId) {
        if (buildingId > 0) {
            query().join("building").eq(StoreQuery.Column.BUILDING_ID, buildingId);
        }

        return this;
    }
}
