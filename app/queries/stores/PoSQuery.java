package queries.stores;

import models.POS;
import org.apache.commons.lang3.StringUtils;
import queries.BaseModelQuery;
import queries.machines.MachineQuery;

public class PoSQuery extends BaseModelQuery<POS> {

    protected static class Column extends BaseColumn {

        public static final String EXTERNAL_ID = "externalID";
    }

    public PoSQuery() {
        super(POS.class);
    }

    public PoSQuery filterByMachineSerialNumber(String machineSerialNumber) {
        if (StringUtils.isNotBlank(machineSerialNumber)) {
            query().eq(PoSQuery.Column.EXTERNAL_ID, machineSerialNumber);
        }

        return this;
    }
}
