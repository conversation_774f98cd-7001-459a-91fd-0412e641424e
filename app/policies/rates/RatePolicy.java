package policies.rates;

import models.Rate;
import models.Role;
import play.mvc.Http;
import policies.ApiClient;
import policies.BaseModelPolicy;
import policies.Level;
import queries.rates.RateQuery;

public class RatePolicy extends BaseModelPolicy<Rate> {

    protected final RateQuery query;

    public RatePolicy(Http.Context ctx) {
        super(ctx);
        this.query = new RateQuery();
    }

    protected RateQuery query() {
        return this.query;
    }

    @Override
    public Role[] creationPermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public Role[] readPermission() {
        return new Role[] { Role.MASTER, Role.ASSISTANT };
    }

    @Override
    public Role[] updatePermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public int level(ApiClient client) {
        super.level(client);

        if (client.match(ApiClient.BACKOFFICE)) {
            return Level.MINIMAL_MEDIUM_LEVEL;
        }

        return Level.MINIMUM_LEVEL;
    }
}
