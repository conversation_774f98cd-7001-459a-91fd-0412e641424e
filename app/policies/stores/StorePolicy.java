package policies.stores;

import models.Role;
import models.Store;
import play.mvc.Http;
import policies.ApiClient;
import policies.BaseModelPolicy;
import policies.Level;
import queries.stores.StoreQuery;

public class StorePolicy extends BaseModelPolicy<Store> {

    protected final StoreQuery query;

    public StorePolicy(Http.Context ctx) {
        super(ctx);
        this.query = new StoreQuery();
    }

    protected StoreQuery query() {
        return this.query;
    }

    @Override
    public Role[] creationPermission() {
        return new Role[] { Role.MASTER, Role.DEVELOPER };
    }

    @Override
    public int level(ApiClient client) {
        super.level(client);

        return Level.MINIMUM_LEVEL;
    }
}
