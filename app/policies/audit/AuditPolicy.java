package policies.audit;

import models.Audit;
import models.Role;
import play.mvc.Http;
import policies.ApiClient;
import policies.BaseModelPolicy;
import policies.Level;
import queries.audit.AuditQuery;

public class AuditPolicy extends BaseModelPolicy<Audit> {

    protected final AuditQuery query;

    public AuditPolicy(Http.Context ctx) {
        super(ctx);
        this.query = new AuditQuery();
    }

    protected AuditQuery query() {
        return this.query;
    }

    @Override
    public Role[] readPermission() {
        return new Role[] { Role.MASTER, Role.ASSISTANT };
    }

    @Override
    public int level(ApiClient client) {
        super.level(client);

        if (client.match(ApiClient.BACKOFFICE)) {
            return Level.MEDIUM_HIGH_LEVEL;
        }

        return Level.MINIMUM_LEVEL;
    }
}
