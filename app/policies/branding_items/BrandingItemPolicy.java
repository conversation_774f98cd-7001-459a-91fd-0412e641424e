package policies.branding_items;

import models.BrandingItem;
import models.Role;
import play.mvc.Http;
import policies.ApiClient;
import policies.BaseModelPolicy;
import policies.Level;
import queries.branding_items.BrandingItemQuery;

public class BrandingItemPolicy extends BaseModelPolicy<BrandingItem> {

    protected final BrandingItemQuery query;

    public BrandingItemPolicy(Http.Context ctx) {
        super(ctx);
        this.query = new BrandingItemQuery();
    }

    protected BrandingItemQuery query() {
        return this.query;
    }

    @Override
    public Role[] creationPermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public Role[] readPermission() {
        return EVERYONE;
    }

    @Override
    public Role[] updatePermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public Role[] deletePermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public int level(ApiClient client) {
        super.level(client);

        if (Role.MASTER == role) {
            return Level.MINIMAL_MEDIUM_LEVEL;
        }

        return Level.MINIMUM_LEVEL;
    }
}
