package policies;

import global.APIException;
import java.util.List;
import models.Role;
import play.mvc.Http;

public abstract class BaseNonModelPolicy<T> extends BasePolicy<T> {

    protected BaseNonModelPolicy(Http.Context ctx) {
        super(ctx);
    }

    /* Operation */

    /**
     * Validate the permission to perform a list action
     * @return the list of items which is allowed to access
     */
    public List<T> list() throws APIException {
        return this.list(permissions.READ);
    }

    /**
     * Validate the permission to perform a list action
     * @return the list of items which is allowed to access
     */
    public List<T> list(Role... roles) throws APIException {
        authorize(roles);

        return null;
    }

    /**
     * Validate the permission to perform a get action
     */
    public void get(Role... roles) throws APIException {
        authorize(roles);
    }

    /**
     * Validate the permission to perform a get action
     */
    public void get() throws APIException {
        this.get(permissions.READ);
    }

    /**
     * Validate the permission to perform a get action
     * @return the item itself
     */
    public T get(int id) throws APIException {
        return this.get(id, permissions.READ);
    }

    /**
     * Validate the permission to perform a get action
     * @return the item itself
     */
    public T get(int id, Role... roles) throws APIException {
        authorize(roles);

        return null;
    }

    /**
     * Validate the permission to perform an update action
     * @return the item itself
     */
    public T update(int id) throws APIException {
        return this.update(id, permissions.UPDATE);
    }

    /**
     * Validate the permission to perform an update action
     * @return the item itself
     */
    public T update(int id, Role... roles) throws APIException {
        authorize(roles);

        return null;
    }

    /**
     * Validate the permission to perform a delete action
     * @return the item itself
     */
    public T delete(int id) throws APIException {
        return this.delete(id, permissions.DELETE);
    }

    /**
     * Validate the permission to perform a delete action
     * @return the item itself
     */
    public T delete(int id, Role... roles) throws APIException {
        authorize(roles);

        return null;
    }
}
