package dto.utilities;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.Machine;

public class MQTTMessageParameters extends dto.JsonBodyActionParameters {

    private int buildingId;
    private String json;
    private String machineSerialNumber;
    private Machine machine;

    private MQTTMessageParameters() {}

    public MQTTMessageParameters(JsonNode body) throws APIException {
        buildingId = safeInt("buildingId", body, 0);
        machineSerialNumber = safeString("serialNumber", body, "");
        json = safeString("json", body, "");
    }

    public MQTTMessageParameters validate() throws APIException {
        if (
            buildingId == 0 || machineSerialNumber.isEmpty() || json.isEmpty()
        ) throw APIException.raise(APIException.APIErrors.MISSING_PARAMETERS);

        machine = Machine.findBySerialNumber(machineSerialNumber);
        if (machine == null) throw APIException.raise(APIException.APIErrors.MACHINE_NOT_FOUND);

        return this;
    }

    public int getBuildingId() {
        return buildingId;
    }

    public String getJson() {
        return json;
    }

    public Machine getMachine() {
        return machine;
    }
}
