package dto.building;

import global.APIException;
import models.Building;

public class GetBuildingParameters extends dto.ActionParameters {

    protected int buildingId;
    protected Building building;

    public GetBuildingParameters(int buildingId) throws APIException {
        this.buildingId = buildingId;
    }

    public GetBuildingParameters validate() throws APIException {
        if (buildingId > 0) {
            building = Building.findById(buildingId);
        }

        if (building == null) {
            throw APIException.raise(APIException.APIErrors.BUILDING_NOT_FOUND);
        }

        return this;
    }

    public Building getBuilding() {
        return building;
    }
}
