package dto.building;

import com.fasterxml.jackson.databind.JsonNode;
import domains.billing.closure.enums.ClosureType;
import global.APIException;
import global.exceptions.BuildingNotFoundException;
import models.Building;

public class UpdateClosureTypeParameterToBuildingParameters extends dto.JsonBodyActionParameters {

    protected static final String CLOSURE_TYPE = "closureType";

    protected ClosureType closureType;
    protected Building building;

    public UpdateClosureTypeParameterToBuildingParameters(JsonNode body, Building building)
        throws APIException {
        this.closureType = safeEnum(ClosureType.class, CLOSURE_TYPE, body);
        this.building = building;
    }

    public UpdateClosureTypeParameterToBuildingParameters validate() throws APIException {
        if (this.building == null) {
            throw new BuildingNotFoundException();
        }

        return this;
    }

    public ClosureType getClosureType() {
        return this.closureType;
    }

    public Building getBuilding() {
        return this.building;
    }
}
