package dto.card;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import java.util.Date;
import models.Card;

public class UpdateCardParameters extends dto.JsonBodyActionParameters {

    protected final int id;
    protected final String uid;
    protected final Boolean master;
    protected final Card.ContractType contractType;
    protected final Date startTime;
    protected final Date endTime;
    protected final Double discount;
    protected Card card;

    public UpdateCardParameters(int id, JsonNode body) throws APIException {
        this.id = id;
        uid = safeString("uuid", body);
        master = safeBooleanNullable("master", body, null);
        contractType = safeEnumNullable(Card.ContractType.class, "contract_type", body, null);
        startTime = safeDateNullable("start_time", body, null);
        endTime = safeDateNullable("end_time", body, null);
        discount = safeDoubleNullable("discount", body, null);
    }

    @Override
    public UpdateCardParameters validate() throws APIException {
        card = (Card) Card.findById(id);
        if (card == null) {
            throw APIException.raise(APIException.APIErrors.CARD_NOT_FOUND);
        }

        return this;
    }

    public Card getCard() {
        return card;
    }

    public String getUid() {
        return uid;
    }

    public Boolean getMaster() {
        return master;
    }

    public Card.ContractType getContractType() {
        return contractType;
    }

    public Date getStartTime() {
        return startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public Double getDiscount() {
        return discount;
    }
}
