package dto;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.APIErrors;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import utils.DateHelper;

public abstract class JsonBodyActionParameters extends dto.ActionParameters {

    // STRING
    public String safeString(String key, JsonNode node) {
        return safeString(key, node, null);
    }

    protected String safeString(String key, JsonNode node, String defaultValue) {
        if (node != null && node.has(key)) return node.get(key).asText();

        return defaultValue;
    }

    protected String safeStringIgnoreNullValue(String key, JsonNode node, String defaultValue) {
        if (node != null && node.has(key) && node.hasNonNull(key)) return node.get(key).asText();

        return defaultValue;
    }

    // INTEGER
    protected int safeInt(String key, JsonNode node) throws APIException {
        return safeInt(key, node, null);
    }

    protected int safeInt(String key, JsonNode node, Integer defaultValue) throws APIException {
        Integer value = safeIntNullable(key, node, defaultValue);
        if (value != null) return value;
        if (defaultValue != null) return defaultValue;

        throw APIException
            .raise(APIErrors.MISSING_PARAMETERS)
            .setDetailMessage(getMissingParametersMessage(key));
    }

    protected Integer safeIntNullable(String key, JsonNode node, Integer defaultValue) {
        if (node != null && node.has(key)) return node.get(key).asInt();

        return defaultValue;
    }

    // SHORT
    protected Short safeShort(String key, JsonNode node, Short defaultValue) throws APIException {
        Short value = safeShortNullable(key, node, defaultValue);
        if (value != null) return value;
        if (defaultValue != null) return defaultValue;

        throw APIException
            .raise(APIErrors.MISSING_PARAMETERS)
            .setDetailMessage(getMissingParametersMessage(key));
    }

    protected Short safeShortNullable(String key, JsonNode node, Short defaultValue) {
        if (node != null && node.has(key)) return Short.valueOf(node.get(key).asText());

        return defaultValue;
    }

    protected Integer safeIntNullableIgnoreNullValue(
        String key,
        JsonNode node,
        Integer defaultValue
    ) {
        if (node != null && node.has(key) && node.hasNonNull(key)) return node.get(key).asInt();

        return defaultValue;
    }

    protected List<Integer> safeIntArray(String key, JsonNode node) {
        List<Integer> safeInts = new ArrayList<Integer>();
        JsonNode array = node.get(key);
        if (array.isArray()) {
            for (JsonNode integer : array) {
                safeInts.add(integer.asInt());
            }
        }
        return safeInts;
    }

    // DOUBLE
    protected double safeDouble(String key, JsonNode node) throws APIException {
        return safeDouble(key, node, null);
    }

    protected double safeDouble(String key, JsonNode node, Double defaultValue)
        throws APIException {
        Double value = safeDoubleNullable(key, node, defaultValue);
        if (value != null) return value;

        if (defaultValue != null) return defaultValue;

        throw APIException
            .raise(APIErrors.MISSING_PARAMETERS)
            .setDetailMessage(getMissingParametersMessage(key));
    }

    protected double safeDoubleNullable(String key, JsonNode node, Double defaultValue) {
        if (node != null && node.has(key)) return node.get(key).asDouble();

        return defaultValue;
    }

    // BOOLEAN
    protected Boolean safeBoolean(String key, JsonNode node) throws APIException {
        return safeBoolean(key, node, false);
    }

    protected Boolean safeBoolean(String key, JsonNode node, Boolean defaultValue)
        throws APIException {
        Boolean value = safeBooleanNullable(key, node, defaultValue);
        if (value != null) return value;

        if (defaultValue != null) return defaultValue;

        throw APIException
            .raise(APIErrors.MISSING_PARAMETERS)
            .setDetailMessage(getMissingParametersMessage(key));
    }

    protected Boolean safeBooleanNullable(String key, JsonNode node, Boolean defaultValue) {
        if (node != null && node.has(key)) return node.get(key).asBoolean();

        return defaultValue;
    }

    // DATE
    protected Date safeDate(String key, JsonNode node) throws APIException {
        return safeDate(key, node, null);
    }

    protected Date safeDate(String key, JsonNode node, Date defaultValue) throws APIException {
        Date value = safeDateNullable(key, node, defaultValue);
        if (value != null) return value;

        return defaultValue;
    }

    protected Date safeDateNullable(String key, JsonNode node, Date defaultValue) {
        return safeDateNullable(key, node, defaultValue, DateHelper.utcDateFormat);
    }

    protected Date safeDateNullable(
        String key,
        JsonNode node,
        Date defaultValue,
        SimpleDateFormat pattern
    ) {
        return safeDateNullable(safeString(key, node), defaultValue, pattern);
    }

    // FLOAT
    protected Float safeFloat(String key, JsonNode node) throws APIException {
        return safeFloat(key, node, null);
    }

    protected Float safeFloat(String key, JsonNode node, Float defaultValue) throws APIException {
        if (node != null && node.has(key)) {
            JsonNode value = node.get(key);
            if (value.isNumber()) {
                return value.floatValue();
            }
        }

        if (defaultValue != null) return defaultValue;

        throw APIException
            .raise(APIErrors.MISSING_PARAMETERS)
            .setDetailMessage(getMissingParametersMessage(key));
    }

    // LONG
    protected long safeLong(String key, JsonNode node) throws APIException {
        return safeLong(key, node, 0);
    }

    protected long safeLong(String key, JsonNode node, long defaultValue) throws APIException {
        if (node != null && node.has(key)) {
            JsonNode value = node.get(key);
            if (value.isNumber()) {
                return value.longValue();
            }

            if (value.isTextual()) {
                return Long.parseLong(value.asText());
            }
        }

        if (defaultValue > 0) return defaultValue;

        throw APIException
            .raise(APIErrors.MISSING_PARAMETERS)
            .setDetailMessage(getMissingParametersMessage(key));
    }

    // ENUM
    protected <E extends Enum<E>> E safeEnum(Class<E> enumClass, String key, JsonNode node)
        throws APIException {
        return safeEnum(enumClass, key, node, null);
    }

    protected <E extends Enum<E>> E safeEnum(
        Class<E> enumClass,
        String key,
        JsonNode node,
        E defaultValue
    ) throws APIException {
        E value = safeEnumNullable(enumClass, key, node, defaultValue);
        if (value != null) {
            return value;
        }

        if (defaultValue != null) {
            return defaultValue;
        }

        throw APIException
            .raise(APIErrors.MISSING_PARAMETERS)
            .setDetailMessage(getMissingParametersMessage(key));
    }

    protected <E extends Enum<E>> E safeEnumNullable(
        Class<E> enumClass,
        String key,
        JsonNode node,
        E defaultValue
    ) throws APIException {
        String value = safeString(key, node);
        if (StringUtils.isNotBlank(value)) {
            try {
                return Enum.valueOf(enumClass, value.toUpperCase());
            } catch (IllegalArgumentException e) {
                throw APIException
                    .raise(APIException.APIErrors.UNSUPPORTED_VALUE)
                    .setDetailMessage(
                        "The received value for \"" +
                        key +
                        "\" (\"" +
                        value +
                        "\") is unexpected according to the declared values."
                    );
            }
        }

        return defaultValue;
    }
    // POC: OBJECT
}
