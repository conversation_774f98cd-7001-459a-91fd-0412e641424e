package dto.notifications;

import domains.billing.closure.enums.ClosureType;
import java.util.Date;
import models.Building;

public class ClosureEmailParams {

    private final String admin;
    private final String closureType;
    private final String from;
    private final String to;
    private final String month;
    private final String year;
    private final String building;
    private final String resourceReimbursementAmount;
    private final String totalUses;
    private final String billedMinUses;
    private final String billedAmount;

    public ClosureEmailParams(
        ClosureType closureType,
        Date from,
        Date to,
        Building building,
        String resourceReimbursementAmount,
        String totalUses,
        String billedMinUses,
        String billedAmount
    ) {
        this.admin = building.getAdministration().getName();
        this.closureType = closureType.toString();
        this.from = from.toString();
        this.to = to.toString();
        this.month = from.getMonth() + "";
        this.year = from.getTime() + "";
        this.building = building.getName();
        this.resourceReimbursementAmount = resourceReimbursementAmount;
        this.totalUses = totalUses;
        this.billedMinUses = billedMinUses;
        this.billedAmount = billedAmount;
    }

    public String getAdmin() {
        return this.admin;
    }

    public String getClosureType() {
        return this.closureType;
    }

    public String getFrom() {
        return this.from;
    }

    public String getTo() {
        return this.to;
    }

    public String getMonth() {
        return this.month;
    }

    public String getYear() {
        return this.year;
    }

    public String getBuilding() {
        return this.building;
    }

    public String getResourceReimbursementAmount() {
        return this.resourceReimbursementAmount;
    }

    public String getTotalUses() {
        return this.totalUses;
    }

    public String getBilledMinUses() {
        return this.billedMinUses;
    }

    public String getBilledAmount() {
        return this.billedAmount;
    }
}
