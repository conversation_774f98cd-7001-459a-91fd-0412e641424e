package dto.machine_model;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.APIErrors;
import models.MaintenanceParameter;

public class MachineModelParameters extends dto.maintenance.MaintenanceParameterParameters {

    String name;

    public MachineModelParameters(JsonNode body) throws APIException {
        super(body);
        name = safeString("name", body);
    }

    public MachineModelParameters validate() throws APIException {
        super.validate();

        if (name.isEmpty()) throw APIException.raise(APIErrors.MISSING_PARAMETERS);

        return this;
    }

    public String getName() {
        return this.name;
    }

    public MaintenanceParameter getMaintenanceParameter() {
        return new MaintenanceParameter(this.mp100, this.mp500, this.mp1200);
    }
}
