package dto.user;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.*;
import org.apache.commons.lang3.StringUtils;

public class CreateColivingUserParameters extends dto.JsonBodyActionParameters {

    protected String firstName;
    protected String lastName;
    protected String email;
    protected String reference;

    public CreateColivingUserParameters() {}

    public CreateColivingUserParameters(JsonNode body) throws APIException {
        this.firstName = safeString("firstname", body);
        this.lastName = safeString("lastname", body);
        this.email = safeString("email", body);
        this.reference = safeString("reference", body);
    }

    public CreateColivingUserParameters validate() throws APIException {
        if (
            StringUtils.isBlank(this.firstName) ||
            StringUtils.isBlank(this.lastName) ||
            StringUtils.isBlank(this.email) ||
            StringUtils.isBlank(this.reference)
        ) {
            throw APIException.raise(APIErrors.MISSING_PARAMETERS);
        }

        return this;
    }

    public String getFirstName() {
        return this.firstName;
    }

    public String getLastName() {
        return this.lastName;
    }

    public String getEmail() {
        return this.email;
    }

    public String getReference() {
        return this.reference;
    }
}
