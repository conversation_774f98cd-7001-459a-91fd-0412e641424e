package dto.maintenance;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.APIErrors;
import models.MaintenanceParameter;

public class MaintenanceParameterParameters extends dto.JsonBodyActionParameters {

    protected static final String MP_100 = "mp100";
    protected static final String MP_500 = "mp500";
    protected static final String MP_1200 = "mp1200";

    protected Integer mp100;
    protected Integer mp500;
    protected Integer mp1200;

    public MaintenanceParameterParameters(JsonNode body) throws APIException {
        this.mp100 = safeInt(MP_100, body, 0);
        this.mp500 = safeInt(MP_500, body, 0);
        this.mp1200 = safeInt(MP_1200, body, 0);
    }

    public MaintenanceParameterParameters validate() throws APIException {
        if (this.mp100 < 1 || this.mp500 < 1 || this.mp1200 < 1) throw APIException.raise(
            APIErrors.MISSING_PARAMETERS
        );

        if (this.mp100 > this.mp500) throw APIException.raise(APIErrors.INVALID_MP_PARAMETERS);

        return this;
    }

    public MaintenanceParameter getMaintenanceParameter() {
        return new MaintenanceParameter(this.mp100, this.mp500, this.mp1200);
    }
}
