package dto.maintenance;

import com.fasterxml.jackson.databind.JsonNode;
import dto.ActionParameters;
import global.APIException;
import global.APIException.APIErrors;
import java.util.Date;
import java.util.List;
import models.Building;
import models.Maintenance;
import models.Maintenance.MaintenanceType;
import org.apache.commons.lang3.StringUtils;

public class CreateMaintenanceParameters extends dto.JsonBodyActionParameters {

    private List<Integer> maintenanceTypesCodes;
    private Building building;
    private Date timestamp;
    private String technician;

    public CreateMaintenanceParameters(JsonNode body, Integer buildingId) throws APIException {
        this.technician = safeString("technician", body);
        this.timestamp = safeDate("timestamp", body, new Date());
        this.maintenanceTypesCodes = safeIntArray("maintenance_type", body);
        this.building = Building.findById(buildingId);
    }

    public CreateMaintenanceParameters validate() throws APIException {
        if (
            this.maintenanceTypesCodes == null ||
            this.maintenanceTypesCodes.isEmpty() ||
            StringUtils.isBlank(this.technician)
        ) {
            throw APIException.raise(APIErrors.MISSING_PARAMETERS);
        }

        if (this.building == null) {
            throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
        }

        return this;
    }

    public List<Integer> getMaintenanceTypeCodes() {
        return this.maintenanceTypesCodes;
    }

    public Building getBuilding() {
        return this.building;
    }

    public Date getTimestamp() {
        return this.timestamp;
    }

    public String getTechnician() {
        return this.technician;
    }

    public boolean isMP1200() {
        return this.maintenanceTypesCodes.contains(Maintenance.MaintenanceType.MP1200.getCode());
    }
}
