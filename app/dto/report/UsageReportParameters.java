package dto.report;

import dto.QueryStringActionParameters;
import global.APIException;
import global.APIException.*;
import java.lang.*;
import java.util.*;
import models.Card;
import org.apache.commons.lang3.StringUtils;
import utils.CardHelper;

public class UsageReportParameters extends QueryStringActionParameters {

    protected static final String CLIENT_RECAPTCHA_RESPONSE = "recaptchaToken";
    protected static final String EMAIL_PARAM = "email";
    protected static final String CARD_PARAM = "card";
    protected static final String FROM_PARAM = "from";
    protected static final String UNTIL_PARAM = "until";

    protected String email;
    protected String cardUid;
    protected Date from;
    protected Date until;

    protected String recaptchaToken;

    private final long ONE_YEAR_IN_MILLIS = 365L * 24L * 60L * 60L * 1000L;

    public UsageReportParameters(Map<String, String[]> queryString) throws APIException {
        this.email = safeString(EMAIL_PARAM, queryString);
        this.cardUid = safeString(CARD_PARAM, queryString);
        this.from = safeDateNullable(FROM_PARAM, queryString);
        this.until = safeDateNullable(UNTIL_PARAM, queryString);
        this.recaptchaToken = safeString(CLIENT_RECAPTCHA_RESPONSE, queryString);
    }

    public UsageReportParameters validate() throws APIException {
        if (
            StringUtils.isEmpty(this.email) ||
            StringUtils.isEmpty(this.cardUid) ||
            this.from == null ||
            this.until == null
        ) {
            throw APIException.raise(APIErrors.MISSING_PARAMETERS);
        }

        this.cardUid = CardHelper.sanitizeUid(cardUid);

        Card card = Card.findByUID(cardUid);
        if (card == null) {
            throw APIException.raise(APIErrors.CARD_NOT_FOUND);
        }

        if (card.isPrepaid() && !card.isEnabled()) {
            throw APIException.raise(APIErrors.INACTIVE_CARD);
        }

        // validate from until
        long dateDiff = this.until.getTime() - this.from.getTime();
        if (dateDiff > ONE_YEAR_IN_MILLIS) {
            throw APIException.raise(APIErrors.INVALID_DATE_PARAMETER);
        }

        return this;
    }

    public String getEmail() {
        return this.email;
    }

    public String getCardUid() {
        return this.cardUid;
    }

    public String getrecaptchaToken() {
        return this.recaptchaToken;
    }

    public Date getFrom() {
        return this.from;
    }

    public Date getUntil() {
        return this.until;
    }
}
