package dto.card_request;

import global.APIException;
import global.APIException.APIErrors;
import java.util.Map;

public class GetRejectActivationParameters extends dto.QueryStringActionParameters {

    protected String token;
    protected String reason;

    public GetRejectActivationParameters() {}

    public GetRejectActivationParameters(Map<String, String[]> body) throws APIException {
        this.token = safeString("token", body);
        this.reason = safeString("reason", body);
    }

    public GetRejectActivationParameters validate() throws APIException {
        if (token == null || token.isEmpty()) {
            throw APIException
                .raise(APIErrors.MISSING_PARAMETERS)
                .setDetailMessage(getMissingParametersMessage("token"));
        }

        if (reason == null || reason.isEmpty()) {
            throw APIException
                .raise(APIErrors.MISSING_PARAMETERS)
                .setDetailMessage(getMissingParametersMessage("reason"));
        }

        return this;
    }

    public String getToken() {
        return token;
    }

    public String getReason() {
        return reason;
    }
}
