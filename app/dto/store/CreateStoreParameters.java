package dto.store;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.exceptions.BuildingNotFoundException;
import global.exceptions.PosAlreadyExistsException;
import global.exceptions.StoreNotFoundException;
import global.exceptions.machines.MachineNotFoundException;
import models.Building;
import models.Machine;
import models.POS;
import models.Store;
import queries.buildings.BuildingQuery;
import queries.machines.MachineQuery;
import queries.stores.PoSQuery;
import queries.stores.StoreQuery;

public class CreateStoreParameters extends dto.JsonBodyActionParameters {

    protected static final String MACHINE_SERIAL = "machine_serial";
    protected static final String BUILDING_ID = "building_id";
    protected static final String DETAILS = "details";

    protected String machineSerial;
    protected int buildingId;
    protected String details;
    protected Machine machine;
    protected Store store;

    public CreateStoreParameters(JsonNode body) throws APIException {
        this.machineSerial = safeString(MACHINE_SERIAL, body);
        this.buildingId = safeInt(BUILDING_ID, body);
        this.details = safeString(DETAILS, body);
    }

    public CreateStoreParameters validate() throws APIException {
        Building building = new BuildingQuery().get(this.buildingId);
        if (building == null) {
            throw new BuildingNotFoundException();
        }

        this.machine = new MachineQuery().filterBySerialNumber(this.machineSerial).single();
        if (this.machine == null) {
            throw new MachineNotFoundException();
        }

        this.store = new StoreQuery().filterByBuildingId(this.buildingId).single();
        if (this.store == null) {
            throw new StoreNotFoundException();
        }

        boolean posAlreadyExists = new PoSQuery()
            .filterByMachineSerialNumber(this.machineSerial)
            .any();
        if (posAlreadyExists) {
            throw new PosAlreadyExistsException();
        }

        return this;
    }

    public String getMachineSerial() {
        return this.machineSerial;
    }

    public int getBuildingId() {
        return this.buildingId;
    }

    public String getDetails() {
        return this.details;
    }

    public Machine getMachine() {
        return this.machine;
    }

    public Store getStore() {
        return this.store;
    }

    public POS getPOS() {
        POS pos = new POS(this.machine, this.store, this.details);
        return pos;
    }
}
