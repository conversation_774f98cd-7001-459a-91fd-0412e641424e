package dto.bill;

import global.APIException;
import java.util.Map;
import models.Card;
import models.Part;
import models.Unit;

public class GetBillSearchParameters extends dto.QueryStringActionParameters {

    protected int cardId;
    protected int limit;
    protected int unitId;

    public GetBillSearchParameters(int cardId, Map<String, String[]> queryString)
        throws APIException {
        this.cardId = cardId;
        this.limit = safeInt("limit", queryString, 0);
    }

    public GetBillSearchParameters validate() throws APIException {
        Card card = null;
        if (cardId != 0) {
            card = (Card) Card.findById(cardId);
        }

        if (card == null) {
            throw APIException.raise(APIException.APIErrors.CARD_NOT_FOUND);
        }

        if (card.getContractType() != Card.ContractType.PREPAID) {
            throw APIException.raise(APIException.APIErrors.CARD_NOT_PREPAID);
        }

        if (card.getState() != Part.PartState.ACTIVE) {
            throw APIException.raise(APIException.APIErrors.INACTIVE_CARD);
        }

        Unit unit = card.getUnit();
        if (unit == null) {
            throw APIException.raise(APIException.APIErrors.UNIT_NOT_FOUND);
        }

        unitId = unit.getId();

        return this;
    }

    public int getLimit() {
        return limit;
    }

    public int getUnitId() {
        return unitId;
    }
}
