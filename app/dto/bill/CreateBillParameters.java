package dto.bill;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.APIErrors;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import models.Bill;
import models.BillItem;
import models.Building;
import models.Unit;
import org.apache.commons.lang3.StringUtils;
import utils.DateHelper;
import utils.RUT;

public class CreateBillParameters extends dto.JsonBodyActionParameters {

    protected static final String TIMESTAMP = "timestamp";
    protected static final String PERIOD_START = "periodStart";
    protected static final String PERIOD_END = "periodEnd";
    protected static final String CURRENCY = "currency";
    protected static final String PAYMENT_METHOD = "paymentMethod";
    protected static final String BILL_TO_TYPE = "billToType";
    protected static final String BUILDING_ID = "buildingId";
    protected static final String UNIT_ID = "unitId";
    protected static final String RECIPIENT = "recipient";
    protected static final String NAME = "name";
    protected static final String DOC_TYPE = "docType";
    protected static final String DOC = "doc";
    protected static final String ADDRESS = "address";
    protected static final String CITY = "city";
    protected static final String DEPARTMENT = "department";
    protected static final String COUNTRY = "country";
    protected static final String COUNTRY_CODE = "countryCode";
    protected static final String ITEMS = "items";
    protected static final String IS_EXPORTATION = "isExportation";

    protected Date timestamp;
    protected Date periodStart;
    protected Date periodEnd;
    protected String currency;
    protected String paymentMethod;
    protected String billToType;
    protected int buildingId;
    protected int unitId;
    protected String recipientName;
    protected String recipientDocType;
    protected String recipientDoc;
    protected String recipientAddress;
    protected String recipientCity;
    protected String recipientDepartment;
    protected String recipientCountry;
    protected String recipientCountryCode;
    protected boolean isExportation;
    protected JsonNode itemJson;
    protected Bill bill;

    public CreateBillParameters(JsonNode body) throws APIException {
        this.timestamp = safeDateNullable(TIMESTAMP, body, new Date(), DateHelper.simpleDateFormat);
        this.periodStart = safeDateNullable(PERIOD_START, body, null, DateHelper.simpleDateFormat);
        this.periodEnd = safeDateNullable(PERIOD_END, body, null, DateHelper.simpleDateFormat);
        this.currency = safeString(CURRENCY, body, StringUtils.EMPTY);
        this.paymentMethod = safeString(PAYMENT_METHOD, body, null);
        this.billToType = safeString(BILL_TO_TYPE, body, "MISC");
        this.isExportation = safeBoolean(IS_EXPORTATION, body, false);

        this.buildingId = safeInt(BUILDING_ID, body, -1);
        this.unitId = safeInt(UNIT_ID, body, -1);

        JsonNode recipientNode = body.get(RECIPIENT);
        if (recipientNode != null) {
            this.recipientName =
                recipientNode.hasNonNull(NAME)
                    ? recipientNode.get(NAME).asText()
                    : StringUtils.EMPTY;
            this.recipientDocType =
                recipientNode.hasNonNull(DOC_TYPE)
                    ? recipientNode.get(DOC_TYPE).asText()
                    : StringUtils.EMPTY;
            this.recipientDoc =
                recipientNode.hasNonNull(DOC) ? recipientNode.get(DOC).asText() : StringUtils.EMPTY;
            this.recipientAddress =
                recipientNode.hasNonNull(ADDRESS)
                    ? recipientNode.get(ADDRESS).asText()
                    : StringUtils.EMPTY;
            this.recipientCity =
                recipientNode.hasNonNull(CITY)
                    ? recipientNode.get(CITY).asText()
                    : StringUtils.EMPTY;
            this.recipientDepartment =
                recipientNode.hasNonNull(DEPARTMENT)
                    ? recipientNode.get(DEPARTMENT).asText()
                    : StringUtils.EMPTY;
            this.recipientCountry =
                recipientNode.hasNonNull(COUNTRY)
                    ? recipientNode.get(COUNTRY).asText()
                    : StringUtils.EMPTY;
            this.recipientCountryCode =
                recipientNode.hasNonNull(COUNTRY_CODE)
                    ? recipientNode.get(COUNTRY_CODE).asText()
                    : StringUtils.EMPTY;
        } else {
            this.recipientName = StringUtils.EMPTY;
            this.recipientDocType = StringUtils.EMPTY;
            this.recipientDoc = StringUtils.EMPTY;
            this.recipientAddress = StringUtils.EMPTY;
            this.recipientCity = StringUtils.EMPTY;
            this.recipientDepartment = StringUtils.EMPTY;
            this.recipientCountry = StringUtils.EMPTY;
            this.recipientCountryCode = StringUtils.EMPTY;
        }

        this.itemJson = body.get(this.ITEMS);
    }

    @Override
    public CreateBillParameters validate() throws APIException {
        if (
            this.itemJson == null ||
            this.itemJson.size() == 0 ||
            StringUtils.isBlank(this.currency) ||
            StringUtils.isBlank(this.paymentMethod)
        ) {
            throw APIException.raise(APIErrors.MISSING_PARAMETERS);
        }

        List<BillItem> items = new ArrayList<>();
        for (JsonNode itemJson : this.itemJson) {
            int amount = itemJson.get("amount").asInt();
            double unitPrice = itemJson.get("unitPrice").asDouble();
            if (amount <= 0 || unitPrice <= 0) {
                throw APIException.raise(APIErrors.INVALID_BILL_AMOUNT);
            }
            BillItem item = new BillItem();
            item.setAmount(amount);
            item.setItemName(itemJson.get("name").asText());
            item.setItemMeasureUnit(itemJson.get("measureUnit").asText());
            item.setItemUnitPrice(unitPrice / 1.22);
            item.setItemIndDet(3);
            items.add(item);
        }

        if (this.billToType.equals(Bill.BILL_TO_TYPE_BUILDING)) {
            Building building = Building.find.byId(this.buildingId);

            if (building == null) {
                throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
            }

            if (this.periodStart == null || this.periodEnd == null) {
                throw APIException.raise(APIErrors.MISSING_PARAMETERS);
            }

            this.bill = new Bill(building, items, this.periodStart, this.periodEnd);
            this.bill.setCreationDate(this.timestamp);
            this.bill.setCurrency(this.currency);
            this.bill.setPaymentMethod(this.paymentMethod);
        } else if (this.billToType.equals(Bill.BILL_TO_TYPE_UNIT)) {
            Unit unit = Unit.find.byId(this.unitId);
            if (unit == null) {
                throw APIException.raise(APIErrors.UNIT_NOT_FOUND);
            }

            this.bill = new Bill(unit, items, this.periodStart, this.periodEnd);
            this.bill.setCreationDate(this.timestamp);
            this.bill.setCurrency(this.currency);
            this.bill.setPaymentMethod(this.paymentMethod);
        } else if (this.billToType.equals(Bill.BILL_TO_TYPE_MISC)) {
            if (
                StringUtils.isBlank(this.recipientName) ||
                StringUtils.isBlank(this.recipientDocType) ||
                StringUtils.isBlank(this.recipientDoc) ||
                StringUtils.isBlank(this.recipientAddress) ||
                StringUtils.isBlank(this.recipientCity) ||
                StringUtils.isBlank(this.recipientDepartment) ||
                StringUtils.isBlank(this.recipientCountry) ||
                StringUtils.isBlank(this.recipientCountryCode)
            ) {
                throw APIException.raise(APIErrors.MISSING_PARAMETERS);
            }

            if (
                this.recipientDocType.equals(Bill.DOC_TYPE_RUT) && !RUT.isValid(this.recipientDoc)
            ) {
                throw APIException.raise(APIErrors.INVALID_RUT);
            }

            this.bill =
                new Bill(
                    this.recipientName,
                    this.recipientDocType,
                    this.recipientDoc,
                    this.recipientAddress,
                    this.recipientCity,
                    this.recipientDepartment,
                    this.recipientCountryCode,
                    this.recipientCountry,
                    items,
                    this.periodStart,
                    this.periodEnd,
                    this.timestamp,
                    this.currency,
                    this.paymentMethod
                );
            if (this.isExportation) {
                this.bill.setBillType(Bill.BillType.EFACTEXPORTATION);
            }
        } else if (this.billToType.equals(Bill.BILL_TO_TYPE_FINAL)) {
            this.bill = new Bill(items, this.timestamp, this.currency, this.paymentMethod);

            bill.setRecipientName(StringUtils.EMPTY);
            bill.setRecipientAddress(StringUtils.EMPTY);
            bill.setRecipientCity(StringUtils.EMPTY);
            bill.setRecipientCountry(StringUtils.EMPTY);
            bill.setRecipientCountry(StringUtils.EMPTY);
            bill.setRecipientDepartment(StringUtils.EMPTY);
        }

        return this;
    }

    public Bill getBill() {
        return this.bill;
    }
}
