package dto.bancard;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.exceptions.MissingParametersException;
import org.json.JSONException;

public class BancardParameters extends dto.JsonBodyActionParameters {

    protected static final String SHOP_PROCESS_ID = "shop_process_id";

    protected String operation;
    protected long shopProcessId;
    protected int responseCode;
    protected String transactionPublicId;

    public BancardParameters(JsonNode body) throws APIException {
        try {
            this.shopProcessId = body.get("operation").get("shop_process_id").asLong();
            this.responseCode = body.get("operation").get("response_code").asInt();
        } catch (JSONException e) {
            this.shopProcessId = -1;
            this.responseCode = -1;
        }
    }

    public BancardParameters validate() throws APIException {
        if (this.shopProcessId < 1) {
            throw new MissingParametersException(SHOP_PROCESS_ID);
        }

        return this;
    }

    public String getTransactionId() {
        return String.valueOf(this.shopProcessId);
    }

    public boolean isConfirmable() {
        return this.responseCode == 0;
    }
}
