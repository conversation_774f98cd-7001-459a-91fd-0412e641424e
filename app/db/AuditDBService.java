package db;

import global.APIException;
import java.util.Arrays;
import java.util.Date;
import models.Audit;
import queries.audit.AuditQuery;

public abstract class AuditDBService extends Backfill {

    protected static AuditQuery query() {
        return new AuditQuery();
    }

    protected static void logError(Exception ex) throws APIException {
        StringBuilder errorLog = new StringBuilder();
        errorLog.append("Error task:").append(System.lineSeparator());
        errorLog.append("- Message: ").append(ex.getMessage()).append(System.lineSeparator());
        errorLog.append("- Cause: ").append(ex.getCause()).append(System.lineSeparator());
        errorLog.append("- Exception: ").append(ex).append(System.lineSeparator());
        errorLog
            .append("- StackTrace: ")
            .append(Arrays.toString(ex.getStackTrace()))
            .append(System.lineSeparator());

        ex.printStackTrace();
        throw APIException
            .raise(APIException.APIErrors.INTERNAL_SERVER_ERROR)
            .setDetailMessage(errorLog.toString());
    }

    public static Audit findAuditRecordByUid(Date to, String uid) throws APIException {
        try {
            return query()
                .filterCreationTimestampTo(to)
                .filterByAnyUid(uid)
                .orderByCreationTimestampDesc()
                .first();
        } catch (Exception ex) {
            logError(ex);
        }

        return null;
    }
}
