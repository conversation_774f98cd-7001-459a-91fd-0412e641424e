<div class="dashboard lighten-3" cg-busy="loadingBuilding">
    <div class="row">
        <div class="col-lg-12">
            <h4 class="p-15 grey-text">
                Edificio {{building.name}}
                <button
                    class="btn btn-xs btn-warning pull-right"
                    style="margin-right: 10px"
                    ng-click="refresh()"
                >
                    Recargar
                </button>
                <button
                    class="btn btn-xs btn-primary pull-right"
                    style="margin-right: 10px"
                    ng-click="showLoadUses()"
                >
                    Cargar Usos
                </button>
                <button
                    class="btn btn-xs btn-danger pull-right"
                    style="margin-right: 10px"
                    ng-click="rechargePrepaidUses()"
                    ng-if="building.prepaidRechargeableUses > 0"
                >
                    Recargar Usos PREPAGO
                </button>
            </h4>
        </div>
    </div>
    <div class="row-fluid">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <strong>Informacion general</strong>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <p style="color: red">
                                <strong>Keep Alive: </strong>{{building.oldestKeepAliveFixed |
                            date:'yyyy-MM-dd HH:mm:ss'}}
                            </p>
                            <p><strong>ID</strong>: {{building.id}}</p>
                            <p><strong>Dirección</strong>: {{building.address}}</p>
                            <p>
                                <strong>Administra</strong>: {{building.administration.name}}
                            </p>
                            <p
                                ng-style="{'color': building.isIncludedInClosure ? 'green' : 'red'}"
                            >
                                <strong ng-if="building.isIncludedInClosure"
                                >Cierre Habilitado</strong
                                >
                                <strong ng-if="!building.isIncludedInClosure"
                                >Cierre Deshabilitado</strong
                                >
                                <button
                                    ng-if="!building.isIncludedInClosure"
                                    class="btn btn-xs btn-success"
                                    ng-click="enableAdministrationClosure(building)"
                                >
                                    Habilitar
                                </button>
                                <button
                                    ng-if="building.isIncludedInClosure"
                                    class="btn btn-xs btn-danger"
                                    ng-click="disableAdministrationClosure(building)"
                                >
                                    Deshabilitar
                                </button>
                            </p>
                            <p
                                ng-style="{'color': building.isEnabledForMaintenance ? 'green' : 'red'}"
                            >
                                <strong ng-if="building.isEnabledForMaintenance"
                                >Mantenimiento Habilitado</strong
                                >
                                <strong ng-if="!building.isEnabledForMaintenance"
                                >Mantenimiento Deshabilitado</strong
                                >
                                <button
                                    ng-if="!building.isEnabledForMaintenance"
                                    class="btn btn-xs btn-success"
                                    ng-click="enableForMaintenance(building)"
                                >
                                    Habilitar
                                </button>
                                <button
                                    ng-if="building.isEnabledForMaintenance"
                                    class="btn btn-xs btn-danger"
                                    ng-click="disableForMaintenance(building)"
                                >
                                    Deshabilitar
                                </button>
                            </p>
                            <p
                                ng-style="{'color': building.isPagosWebWithSplitEnabled ? 'green' : 'red'}"
                            >
                                <strong ng-if="building.isPagosWebWithSplitEnabled"
                                >PagosWeb con Split Habilitado</strong
                                >
                                <strong ng-if="!building.isPagosWebWithSplitEnabled"
                                >PagosWeb con Split Deshabilitado</strong
                                >
                                <button
                                    ng-if="!building.isPagosWebWithSplitEnabled"
                                    class="btn btn-xs btn-success"
                                    ng-click="enablePagosWebWithSplit(building)"
                                >
                                    Habilitar
                                </button>
                                <button
                                    ng-if="building.isPagosWebWithSplitEnabled"
                                    class="btn btn-xs btn-danger"
                                    ng-click="disablePagosWebWithSplit(building)"
                                >
                                    Deshabilitar
                                </button>
                            </p>
                            <p
                                ng-style="{'color': building.isRedPagosWithSplitEnabled ? 'green' : 'red'}"
                            >
                                <strong ng-if="building.isRedPagosWithSplitEnabled"
                                >RedPagos con Split Habilitado</strong
                                >
                                <strong ng-if="!building.isRedPagosWithSplitEnabled"
                                >RedPagos con Split Deshabilitado</strong
                                >
                                <button
                                    ng-if="!building.isRedPagosWithSplitEnabled"
                                    class="btn btn-xs btn-success"
                                    ng-click="enableRedPagosWithSplit(building)"
                                >
                                    Habilitar
                                </button>
                                <button
                                    ng-if="building.isRedPagosWithSplitEnabled"
                                    class="btn btn-xs btn-danger"
                                    ng-click="disableRedPagosWithSplit(building)"
                                >
                                    Deshabilitar
                                </button>
                            </p>
                            <p>
                                <strong>Precio al Cliente/Empresa</strong>:
                                ${{building.rate.priceCustomer}}
                            </p>
                            <p>
                                <strong>Precio Tarjeta</strong>:
                                ${{building.rate.priceCardReplacement}}
                            </p>
                            <p>
                                <strong>Precio Empresa</strong>: ${{building.rate.priceCompany}}
                            </p>
                        </div>
                        <div class="col-lg-6">
                            <p>
                                <span ng-if="!building.editClosureType"
                                ><strong>Tipo de Cierre</strong>:
                                    {{buildingClosureTypes[building.closureType]}}
                                </span>
                                <span ng-if="building.editClosureType"
                                ><strong>Tipo de Cierre</strong>:
                                    <select
                                        class="form-control"
                                        ng-model="building.closureType"
                                        ng-options="key as value for (key , value) in buildingClosureTypes"
                                    ></select>
                                </span>
                                <button
                                    ng-if="!building.editClosureType"
                                    class="btn btn-xs btn-info"
                                    ng-click="building.editClosureType = true"
                                >
                                    CAMBIAR
                                </button>
                                <button
                                    ng-if="building.editClosureType"
                                    class="btn btn-xs btn-success"
                                    ng-click="editClosureType()"
                                >
                                    GUARDAR
                                </button>
                                <button
                                    ng-if="building.editClosureType"
                                    class="btn btn-xs btn-danger"
                                    ng-click="building.editClosureType = false"
                                >
                                    CANCELAR
                                </button>
                            </p>
                            <p>
                                <strong>Día de Cierre</strong>:
                                {{building.administration.closureDay}}
                            </p>
                            <div ng-if="building.showPaymentMethod">
                                <p>
                                    <span ng-if="!building.editPaymentMethod"
                                    ><strong>Método de Pago</strong>: {{
                                        findPaymentMethod(building.paymentMethod) }}
                                    </span>
                                    <span ng-if="building.editPaymentMethod"
                                    ><strong>Método de Pago</strong>:
                                        <select
                                            class="form-control"
                                            style="width: 180px; display: inline"
                                            ng-model="building.paymentMethod"
                                        >
                                            <option
                                                ng-repeat="method in paymentMethods"
                                                value="{{method.key}}"
                                            >
                                                {{method.value}}
                                            </option>
                                        </select>
                                    </span>
                                    <button
                                        ng-if="!building.editPaymentMethod"
                                        class="btn btn-xs btn-info"
                                        ng-click="building.editPaymentMethod = true"
                                    >
                                        CAMBIAR
                                    </button>
                                    <button
                                        ng-if="building.editPaymentMethod"
                                        class="btn btn-xs btn-success"
                                        ng-click="savePaymentMethod()"
                                    >
                                        GUARDAR
                                    </button>
                                    <button
                                        ng-if="building.editPaymentMethod"
                                        class="btn btn-xs btn-danger"
                                        ng-click="building.editPaymentMethod = false"
                                    >
                                        CANCELAR
                                    </button>
                                </p>
                            </div>
                            <p>
                                <strong>COBRO MIN X UNIDAD</strong>: {{
                                building.rate.minUsesPerUnit }}
                            </p>
                            <p>
                                <span ng-if="!building.editBuildingPrepaidRechargableUses"
                                ><strong>Recarga PREPAGO</strong>: {{
                                    building.prepaidRechargeableUses }}
                                </span>
                                <span ng-if="building.editBuildingPrepaidRechargableUses"
                                ><strong>Recarga PREPAGO</strong>:
                                    <input
                                        type="text"
                                        class="form-control"
                                        ng-model="building.prepaidRechargeableUses"
                                        style="width: 180px; display: inline"
                                    />
                                </span>
                                <button
                                    ng-if="!building.editBuildingPrepaidRechargableUses"
                                    class="btn btn-xs btn-info"
                                    ng-click="building.editBuildingPrepaidRechargableUses = true"
                                >
                                    CAMBIAR
                                </button>
                                <button
                                    ng-if="building.editBuildingPrepaidRechargableUses"
                                    class="btn btn-xs btn-success"
                                    ng-click="saveBuildingPrepaidRechargableUses()"
                                >
                                    GUARDAR
                                </button>
                                <button
                                    ng-if="building.editBuildingPrepaidRechargableUses"
                                    class="btn btn-xs btn-danger"
                                    ng-click="building.editBuildingPrepaidRechargableUses = false"
                                >
                                    CANCELAR
                                </button>
                            </p>

                            <p>
                                <span ng-if="!building.editBuildingMaxNumberOfUnits"
                                ><strong>Máxima cant. unidades</strong>: {{
                                    building.maxNumberOfUnits }}
                                </span>
                                <span ng-if="building.editBuildingMaxNumberOfUnits"
                                ><strong>Máxima cant. unidades</strong>:
                                    <input
                                        type="number"
                                        min="0"
                                        class="form-control"
                                        ng-model="building.maxNumberOfUnits"
                                        style="width: 180px; display: inline"
                                    />
                                </span>
                                <button
                                    ng-if="!building.editBuildingMaxNumberOfUnits"
                                    class="btn btn-xs btn-info"
                                    ng-click="building.editBuildingMaxNumberOfUnits = true"
                                >
                                    CAMBIAR
                                </button>
                                <button
                                    ng-if="building.editBuildingMaxNumberOfUnits"
                                    class="btn btn-xs btn-success"
                                    ng-click="saveBuildingMaxNumberOfUnits()"
                                >
                                    GUARDAR
                                </button>
                                <button
                                    ng-if="building.editBuildingMaxNumberOfUnits"
                                    class="btn btn-xs btn-danger"
                                    ng-click="building.editBuildingMaxNumberOfUnits = false"
                                >
                                    CANCELAR
                                </button>
                            </p>

                            <p>
                                <span ng-if="!building.editPreBlockedUses"
                                ><strong>Usos PRE Bloqueo</strong>: {{
                                    building.setting.preBlockedUses }}
                                </span>
                                <span ng-if="building.editPreBlockedUses"
                                ><strong>Máxima cant. unidades</strong>:
                                    <input
                                        type="number"
                                        min="0"
                                        class="form-control"
                                        ng-model="building.setting.preBlockedUses"
                                        style="width: 180px; display: inline"
                                    />
                                </span>
                                <button
                                    ng-if="!building.editPreBlockedUses"
                                    class="btn btn-xs btn-info"
                                    ng-click="building.editPreBlockedUses = true"
                                >
                                    CAMBIAR
                                </button>
                                <button
                                    ng-if="building.editPreBlockedUses"
                                    class="btn btn-xs btn-success"
                                    ng-click="saveBuildingPreBlockedUses()"
                                >
                                    GUARDAR
                                </button>
                                <button
                                    ng-if="building.editPreBlockedUses"
                                    class="btn btn-xs btn-danger"
                                    ng-click="building.editPreBlockedUses = false"
                                >
                                    CANCELAR
                                </button>
                            </p>

                            <p
                                ng-if="building.buildingType == 'LAUNDROMAT' || building.buildingType == 'THIRD_PARTY_LAUNDROMAT'"
                            >
                                <span ng-if="!editingBuildingTime">
                                    <strong>Horario de Local</strong>: {{ building.openingTime |
                                date: 'HH:mm' }} - {{ building.closingTime | date: 'HH:mm' }}
                                </span>
                                <span ng-if="editingBuildingTime">
                                    <strong>Horario de Local</strong>:
                                    <input
                                        type="time"
                                        class="form-control"
                                        ng-model="building.openingDateTime"
                                        style="width: 180px; display: inline"
                                    />
                                    -
                                    <input
                                        type="time"
                                        class="form-control"
                                        ng-model="building.closingDateTime"
                                        style="width: 180px; display: inline"
                                    />
                                </span>
                                <button
                                    ng-if="!editingBuildingTime"
                                    class="btn btn-xs btn-info"
                                    ng-click="editBuildingTime()"
                                >
                                    CAMBIAR
                                </button>
                                <button
                                    ng-if="editingBuildingTime"
                                    class="btn btn-xs btn-success"
                                    ng-click="saveBuildingTime()"
                                >
                                    GUARDAR
                                </button>
                                <button
                                    ng-if="editingBuildingTime"
                                    class="btn btn-xs btn-danger"
                                    ng-click="editingBuildingTime = false"
                                >
                                    CANCELAR
                                </button>
                            </p>
                        </div>
                    </div>
                    <hr class="m-5"/>
                    <div class="row">
                        <div class="col-lg-6">
                            <strong>Informacion de contacto</strong>
                            <p>
                                <span ng-if="!building.editContact"
                                >Contacto:
                                    <textarea rows="5" cols="50" disabled>
{{building.contact}}</textarea
                                    >
                                </span>
                                <span ng-if="building.editContact"
                                >Contacto:
                                    <textarea
                                        rows="5"
                                        cols="50"
                                        ng-model="building.contact"
                                    ></textarea>
                                </span>
                                <button
                                    ng-if="!building.editContact"
                                    class="btn btn-xs btn-info"
                                    ng-click="building.editContact = true"
                                >
                                    CAMBIAR
                                </button>
                                <button
                                    ng-if="building.editContact"
                                    class="btn btn-xs btn-success"
                                    ng-click="saveContact()"
                                >
                                    GUARDAR
                                </button>
                                <button
                                    ng-if="building.editContact"
                                    class="btn btn-xs btn-danger"
                                    ng-click="building.editContact = false"
                                >
                                    CANCELAR
                                </button>
                            </p>
                        </div>
                    </div>

                    <hr class="m-5"/>
                    <div class="row">
                        <div class="col-lg-12">
                            <strong>Asistente:</strong>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <span><strong>URL:</strong></span>
                            <span>{{building.assistantUrl}}</span>
                            <span
                                class="md md-content-copy pointer"
                                ng-click="copy($event, building.assistantUrl)"
                            />
                        </div>
                        <div class="col-lg-6">
                            <span
                            ><strong>Google Maps:</strong> (solo aplica a Laundromats)
                                -</span
                            >
                            <span>
                                {{building.googleMaps && building.googleMaps.name}} |
                                {{building.googleMaps && building.googleMaps.link}}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row-fluid">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div>
                    <ul class="nav nav-tabs">
                        <li ng-class="{'active': activeTab === 0 }" class="ng-scope">
                            <a ng-click="setActiveTab(0)" class="ng-binding">Unidades</a>
                        </li>
                        <li ng-class="{'active': activeTab === 1 }" class="ng-scope">
                            <a ng-click="setActiveTab(1)" class="ng-binding">Máquinas</a>
                        </li>
                        <li ng-class="{'active': activeTab === 2 }" class="ng-scope">
                            <a ng-click="setActiveTab(2)" class="ng-binding"
                            >Mantenimientos</a
                            >
                        </li>
                    </ul>
                    <div title="Unidades" ng-if="activeTab === 0" cg-busy="loadingUnits">
                        <div class="panel-heading">
                            <button
                                class="btn btn-xs btn-warning pull-right"
                                style="margin-right: 10px"
                                ng-click="loadUnits(true)"
                            >
                                Recargar
                            </button
                            >&nbsp;
                            <button
                                class="btn btn-xs btn-info pull-right"
                                style="margin-right: 10px"
                                ng-click="createUnit()"
                            >
                                Agregar Unidad
                            </button>
                            <button
                                class="btn btn-xs btn-info pull-right"
                                style="margin-right: 10px"
                                ng-click="showLoadBuidling()"
                            >
                                Importar Archivo
                            </button>
                        </div>
                        <div class="panel-body">
                            <table class="table table-full table-hover">
                                <thead>
                                <tr>
                                    <th ng-click="orderUnitsColumn('tower')">
                                        Torre
                                        <span
                                            class="sortorder"
                                            ng-show="filters.unitsOrderBy === 'tower'"
                                            ng-class="{reverse: filters.unitsReverse}"
                                        />
                                    </th>
                                    <th ng-click="orderUnitsColumn('number')">
                                        Numero
                                        <span
                                            class="sortorder"
                                            ng-show="filters.unitsOrderBy === 'number'"
                                            ng-class="{reverse: filters.unitsReverse}"
                                        />
                                    </th>
                                    <th ng-click="orderUnitsColumn('contact')">
                                        Contacto
                                        <span
                                            class="sortorder"
                                            ng-show="filters.unitsOrderBy === 'contact'"
                                            ng-class="{reverse: filters.unitsReverse}"
                                        />
                                    </th>
                                    <th>Tarjetas</th>
                                    <th>Acción</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr
                                    ng-repeat="unit in units | orderBy: filters.unitsOrderBy: filters.unitsReverse"
                                >
                                    <td>{{unit.tower}}</td>
                                    <td>{{unit.number}}</td>
                                    <td>{{unit.contact}}</td>
                                    <td>
                                        &#013;
                                        <div
                                            class="btn-group"
                                            ng-repeat="card in unit.assigned_cards"
                                        >
                                            <button
                                                style="border-radius: 15px"
                                                class="btn badge dropdown-toggle"
                                                ng-class="{'blue':card.state=='ACTIVE','gray':(card.state=='INACTIVE' && card.substate=='SUSPENDED'), 'red':(card.state=='INACTIVE' && (card.substate=='LOST'||card.substate=='DAMAGED')), 'orange':(card.state=='PRE_BLOCKED')}"
                                                style="margin-right: 2px"
                                                data-toggle="dropdown"
                                                ng-click="loadCardEvents(card)"
                                            >
                                                {{card.uuid}}
                                            </button>
                                            <ul class="dropdown-menu" style="width: 240px">
                                                <li ng-click="copy($event, card.uuid)">
                                                    <a>Copiar</a>
                                                </li>
                                                <li class="divider"></li>
                                                <li ng-if="card.state!='ACTIVE'">
                                                    <a ng-click="enableCard(card, unit)">Habilitar</a>
                                                </li>
                                                <li ng-if="card.state=='ACTIVE'">
                                                    <a ng-click="disableCard(card, unit)"
                                                    >Deshabilitar</a
                                                    >
                                                </li>
                                                <li
                                                    ng-if="building.setting.isPreBlockedUseEnabled && card.state != 'PRE_BLOCKED'"
                                                >
                                                    <a ng-click="preBlockCard(card, unit)"
                                                    >Pre Bloquear</a
                                                    >
                                                </li>
                                                <li ng-if="card.state=='ACTIVE'">
                                                    <a ng-click="lostCard(card, unit)">Perdida</a>
                                                </li>
                                                <li ng-if="card.state=='ACTIVE'">
                                                    <a ng-click="damagedCard(card, unit)">Dañada</a>
                                                </li>
                                                <li class="divider"></li>
                                                <li>
                                                    <a href ng-click="unassignCardFromUnit(card, unit)"
                                                    >Desasignar</a
                                                    >
                                                </li>
                                                <li class="divider"></li>
                                                <li class="m-15">
                                                    <p ng-repeat="cardEvent in cardEvents">
                                                        {{cardEvent}}
                                                    </p>
                                                </li>
                                            </ul>
                                        </div>
                                        <span ng-if="unit.assigned_cards.length == 0"
                                        >No tiene</span
                                        >
                                    </td>
                                    <td style="overflow: visible">
                                        <button
                                            class="btn btn-xs btn-primary"
                                            ng-click="editUnit(unit)"
                                        >
                                            Editar
                                        </button>
                                        <div class="btn-group">
                                            <button
                                                ng-if="unit.assigned_card == null"
                                                class="btn btn-xs btn-warning"
                                                ng-click="showAssignCard(unit)"
                                            >
                                                Asignar Tarjeta
                                            </button>
                                        </div>
                                        <button
                                            class="btn btn-xs btn-danger"
                                            ng-click="deleteUnit(unit)"
                                        >
                                            Borrar
                                        </button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div
                        title="Máquinas"
                        ng-if="activeTab === 1"
                        cg-busy="loadingMachines"
                    >
                        <div class="panel-heading">
                            <button
                                class="btn btn-xs btn-warning pull-right"
                                style="margin-right: 10px"
                                ng-click="loadMachines(true)"
                            >
                                Recargar
                            </button
                            >&nbsp;
                            <button
                                class="btn btn-xs btn-info pull-right"
                                style="margin-right: 10px"
                                ng-click="showAssignMachine()"
                            >
                                Agregar Máquina
                            </button>
                        </div>
                        <div class="panel-body">
                            <table class="table table-full table-hover">
                                <thead>
                                <tr>
                                    <th class="text-center" ng-click="orderMachineColumn('id')">
                                        ID
                                        <span
                                            class="sortorder"
                                            ng-show="filters.machinesOrderBy === 'id'"
                                            ng-class="{reverse: filters.machineReverse}"
                                        />
                                    </th>
                                    <th
                                        class="text-center"
                                        ng-click="orderMachineColumn('sort_index')"
                                    >
                                        Sort Index
                                        <span
                                            class="sortorder"
                                            ng-show="filters.machinesOrderBy === 'sort_index'"
                                            ng-class="{reverse: filters.machineReverse}"
                                        />
                                    </th>
                                    <th
                                        class="text-center"
                                        ng-click="orderMachineColumn('reference')"
                                    >
                                        Referencia
                                        <span
                                            class="sortorder"
                                            ng-show="filters.machinesOrderBy === 'reference'"
                                            ng-class="{reverse: filters.machineReverse}"
                                        />
                                    </th>
                                    <th
                                        class="text-center"
                                        ng-click="orderMachineColumn('serial_number')"
                                    >
                                        Número de Serie
                                        <span
                                            class="sortorder"
                                            ng-show="filters.machinesOrderBy === 'serial_number'"
                                            ng-class="{reverse: filters.machineReverse}"
                                        />
                                    </th>
                                    <th
                                        class="text-center"
                                        ng-click="orderMachineColumn('machine_type')"
                                    >
                                        Tipo
                                        <span
                                            class="sortorder"
                                            ng-show="filters.machinesOrderBy === 'machine_type'"
                                            ng-class="{reverse: filters.machineReverse}"
                                        />
                                    </th>
                                    <th
                                        class="text-center"
                                        ng-click="orderMachineColumn('last_keep_alive')"
                                    >
                                        Keep Alive
                                        <span
                                            class="sortorder"
                                            ng-show="filters.machinesOrderBy === 'last_keep_alive'"
                                            ng-class="{reverse: filters.machineReverse}"
                                        />
                                    </th>
                                    <th
                                        class="text-center"
                                        ng-click="orderMachineColumn('pending_uses')"
                                    >
                                        Usos pendientes
                                        <span
                                            class="sortorder"
                                            ng-show="filters.machinesOrderBy === 'pending_uses'"
                                            ng-class="{reverse: filters.machineReverse}"
                                        />
                                    </th>
                                    <th
                                        class="text-center"
                                        ng-click="orderMachineColumn('last_use')"
                                    >
                                        Último Uso
                                        <span
                                            class="sortorder"
                                            ng-show="filters.machinesOrderBy === 'last_use'"
                                            ng-class="{reverse: filters.machineReverse}"
                                        />
                                    </th>
                                    <th class="text-center">Usos post prox. cierre</th>
                                    <th class="text-center">Keep alive post cierre</th>
                                    <th
                                        class="text-center"
                                        ng-click="orderMachineColumn('rpi_child.serial_number')"
                                    >
                                        RPI Hijo
                                        <span
                                            class="sortorder"
                                            ng-show="filters.machinesOrderBy === 'rpi_child.serial_number'"
                                            ng-class="{reverse: filters.machineReverse}"
                                        />
                                    </th>
                                    <th class="text-center">QR</th>
                                    <th class="text-center">Acción</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr
                                    class="text-center"
                                    ng-repeat="machine in machines | orderBy: filters.machinesOrderBy: filters.machineReverse"
                                >
                                    <td class="text-center">{{ machine.id }}</td>
                                    <td class="text-center">{{ machine.sort_index }}</td>
                                    <td class="text-center">{{ machine.reference }}</td>
                                    <td class="text-center">{{ machine.serial_number }}</td>
                                    <td class="text-center">
                                        {{ machine.machine_type == 'WASHER' ? "Lavadora" :
                                        "Secadora" }}
                                    </td>
                                    <td class="text-center">
                                        <div>
                                            {{ machine.last_keep_alive | date:'yyyy/MM/dd' }}
                                        </div>
                                        <div>{{ machine.last_keep_alive | date:'HH:mm:ss'}}</div>
                                    </td>
                                    <td class="text-center">{{ machine.pending_uses }}</td>
                                    <td class="text-center">
                                        <div>{{ machine.last_use | date:'yyyy/MM/dd' }}</div>
                                        <div>{{ machine.last_use | date:'HH:mm'}}</div>
                                        Apto: {{ machine.last_unit_use }}
                                    </td>
                                    <td class="text-center">
                                        <i
                                            class="md md-check-box bold-green"
                                            ng-if="machine.uses_after_closure_date"
                                        ></i>
                                        <i
                                            class="md md-cancel bold-red"
                                            ng-if="!machine.uses_after_closure_date"
                                        ></i>
                                    </td>
                                    <td class="text-center">
                                        <i
                                            class="md md-check-box bold-green"
                                            ng-if="machine.keep_alive_after_closure_date"
                                        ></i>
                                        <i
                                            class="md md-cancel bold-red"
                                            ng-if="!machine.keep_alive_after_closure_date"
                                        ></i>
                                    </td>
                                    <td class="text-center">
                                        <span ng-if="!!machine.rpi_child">
                                            {{machine.rpi_child.serial_number}}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span ng-if="!!machine.qr"> {{machine.qr.token}} </span>
                                    </td>
                                    <td class="text-left">
                                        <button
                                            class="btn btn-xs btn-danger"
                                            ng-click="unassignMachineFromBuilding(machine)"
                                        >
                                            Desasignar
                                        </button>
                                        <button
                                            class="btn btn-xs btn-success"
                                            ng-if="!machine.rpi_child && !isRPIChild(machine)"
                                            ng-click="assignRPIChild(machine)"
                                        >
                                            Asignar RPI Hijo
                                        </button>
                                        <button
                                            class="btn btn-xs btn-warning"
                                            ng-if="!!machine.rpi_child"
                                            ng-click="unassignRPIChild(machine)"
                                        >
                                            Desasignar RPI
                                        </button>
                                        <button
                                            class="btn btn-xs btn-primary"
                                            ng-if="machine.pending_uses"
                                            ng-click="zeroingPendingUses(machine)"
                                        >
                                            0 Usos Pendientes
                                        </button>
                                        <button
                                            class="btn btn-xs purple darken-3"
                                            ng-if="!machine.qr"
                                            ng-click="assignQr(machine)"
                                        >
                                            Asignar QR
                                        </button>
                                        <button
                                            class="btn btn-xs purple darken-3"
                                            ng-if="!!machine.qr"
                                            ng-click="unassignQr(machine)"
                                        >
                                            Desasignar QR
                                        </button>
                                        <button
                                            class="btn btn-xs indigo"
                                            ng-click="showMachineHistory(machine)"
                                        >
                                            Historial
                                        </button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>

                            <toast message="toast"></toast>
                        </div>
                    </div>
                    <div
                        title="Mantenimientos"
                        ng-if="activeTab === 2"
                        cg-busy="loadingMaintenances"
                    >
                        <div class="panel-heading">
                            <div class="col-lg-6">
                                <strong>Parámetros de Mantenimiento</strong>
                                <p>
                                    <span ng-if="!building.editMP100">
                                        <strong>MP100</strong>: {{ building.mp100 }}
                                    </span>
                                    <span ng-if="building.editMP100">
                                        <strong>MP100</strong>:
                                        <input
                                            type="text"
                                            class="form-control"
                                            ng-model="building.mp100"
                                            style="width: 180px; display: inline"
                                        />
                                    </span>
                                    <button
                                        ng-if="!building.editMP100"
                                        class="btn btn-xs btn-info"
                                        ng-click="building.editMP100 = true"
                                    >
                                        CAMBIAR
                                    </button>
                                    <button
                                        ng-if="building.editMP100"
                                        class="btn btn-xs btn-success"
                                        ng-click="saveMP100Parameter()"
                                    >
                                        GUARDAR
                                    </button>
                                    <button
                                        ng-if="building.editMP100"
                                        class="btn btn-xs btn-danger"
                                        ng-click="building.editMP100 = false"
                                    >
                                        CANCELAR
                                    </button>
                                </p>

                                <p>
                                    <span ng-if="!building.editMP500"
                                    ><strong>MP500</strong>: {{ building.mp500 }}
                                    </span>
                                    <span ng-if="building.editMP500"
                                    ><strong>MP500</strong>:
                                        <input
                                            type="text"
                                            class="form-control"
                                            ng-model="building.mp500"
                                            style="width: 180px; display: inline"
                                        />
                                    </span>
                                    <button
                                        ng-if="!building.editMP500"
                                        class="btn btn-xs btn-info"
                                        ng-click="building.editMP500 = true"
                                    >
                                        CAMBIAR
                                    </button>
                                    <button
                                        ng-if="building.editMP500"
                                        class="btn btn-xs btn-success"
                                        ng-click="saveMP500Parameter()"
                                    >
                                        GUARDAR
                                    </button>
                                    <button
                                        ng-if="building.editMP500"
                                        class="btn btn-xs btn-danger"
                                        ng-click="building.editMP500 = false"
                                    >
                                        CANCELAR
                                    </button>
                                </p>

                                <p>
                                    <span ng-if="!building.editMP1200"
                                    ><strong>MP1200</strong>: {{ building.mp1200 }}
                                    </span>
                                    <span ng-if="building.editMP1200"
                                    ><strong>MP1200</strong>:
                                        <input
                                            type="text"
                                            class="form-control"
                                            ng-model="building.mp1200"
                                            style="width: 180px; display: inline"
                                        />
                                    </span>
                                    <button
                                        ng-if="!building.editMP1200"
                                        class="btn btn-xs btn-info"
                                        ng-click="building.editMP1200 = true"
                                    >
                                        CAMBIAR
                                    </button>
                                    <button
                                        ng-if="building.editMP1200"
                                        class="btn btn-xs btn-success"
                                        ng-click="saveMP1200Parameter()"
                                    >
                                        GUARDAR
                                    </button>
                                    <button
                                        ng-if="building.editMP1200"
                                        class="btn btn-xs btn-danger"
                                        ng-click="building.editMP1200 = false"
                                    >
                                        CANCELAR
                                    </button>
                                </p>
                            </div>
                            <button
                                class="btn btn-xs btn-info pull-right"
                                style="margin-right: 10px"
                                ng-click="createMaintenance()"
                            >
                                Agregar Mantenimiento
                            </button>
                        </div>
                        <div class="panel-body">
                            <table class="table table-full table-hover">
                                <thead>
                                <tr>
                                    <th ng-click="orderMaintenanceColumn('timestamp')">
                                        Fecha
                                        <span
                                            class="sortorder"
                                            ng-show="filters.maintenanceOrderBy === 'timestamp'"
                                            ng-class="{reverse: filters.maintenanceReverse}"
                                        />
                                    </th>
                                    <th ng-click="orderMaintenanceColumn('maintenanceType')">
                                        Tipo
                                        <span
                                            class="sortorder"
                                            ng-show="filters.maintenanceOrderBy === 'maintenanceType'"
                                            ng-class="{reverse: filters.maintenanceReverse}"
                                        />
                                    </th>
                                    <th ng-click="orderMaintenanceColumn('technician')">
                                        Técnico
                                        <span
                                            class="sortorder"
                                            ng-show="filters.maintenanceOrderBy === 'technician'"
                                            ng-class="{reverse: filters.maintenanceReverse}"
                                        />
                                    </th>
                                    <th>Acción</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr
                                    ng-repeat="maintenance in maintenances | orderBy: filters.maintenanceOrderBy: filters.maintenanceReverse"
                                >
                                    <td>{{maintenance.timestamp}}</td>
                                    <td>{{maintenance.maintenanceType}}</td>
                                    <td>{{maintenance.technician}}</td>
                                    <td style="overflow: visible">
                                        <button
                                            class="btn btn-xs btn-warning"
                                            ng-click="editMaintenance(maintenance)"
                                        >
                                            Editar
                                        </button>
                                        <button
                                            class="btn btn-xs btn-danger"
                                            ng-click="deleteMaintenance(maintenance)"
                                        >
                                            Borrar
                                        </button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="/unit-form.html">
    <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
        <div class="close">
            <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
        </div>

        <div class="aside-dialog">
            <div class="aside-body bs-sidebar" cg-busy="savingUnit">

                <form class="form-floating" novalidate="novalidate" ng-submit="saveItem(item)">
                    <fieldset>
                        <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' : 'Crear nueva'}} Unidad
                        </legend>

                        <div class="bs-component" ng-if="errorMessage != null">
                            <div class="alert alert-dismissible alert-danger">
                                <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                                </button>
                                <h4>Error</h4>
                                {{errorMessage}}
                            </div>
                        </div>

                        <div class="form-group filled">
                            <label class="control-label">Torre</label>
                            <input type="text" class="form-control" ng-model="item.tower" ng-disabled="!item.editing"
                                   required>
                        </div>
                        <div class="form-group filled">
                            <label class="control-label">Número</label>
                            <input type="text" class="form-control" ng-model="item.number" ng-disabled="!item.editing"
                                   required>
                        </div>

                        <div class="form-group filled">
                            <label class="control-label">Email Dueño</label>
                            <input type="text" class="form-control" ng-model="item.contact" ng-disabled="!item.editing">
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                        </div>

                    </fieldset>
                </form>

            </div>
        </div>
    </div>
</script>

<script type="text/ng-template" id="/uses-form.html">
    <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
        <div class="close">
            <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
        </div>

        <div class="aside-dialog">
            <div class="aside-body bs-sidebar" cg-busy="uploadingUses">

                <form class="form-floating" novalidate="novalidate">
                    <fieldset>
                        <legend><span ng-bind-html="item.icon"></span>Cargar archivo de Usos</legend>

                        <div class="bs-component" ng-if="errorMessage != null">
                            <div class="alert alert-dismissible alert-danger">
                                <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                                </button>
                                <h4>Error</h4>
                                {{errorMessage}}
                            </div>
                        </div>

                        <div class="form-group filled">
                            <label class="control-label">Archivo</label>
                            <input type="file" ngf-select class="form-control" required ng-model="data.file">
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" ng-click="uploadFile(data.file)">Cargar
                            </button>
                        </div>

                    </fieldset>
                </form>

            </div>
        </div>
    </div>
</script>

<script type="text/ng-template" id="/building-file-form.html">
    <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
        <div class="close">
            <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
        </div>

        <div class="aside-dialog">
            <div class="aside-body bs-sidebar" cg-busy="uploadingUses">

                <form class="form-floating" novalidate="novalidate" ng-submit="saveItem(item)">
                    <fieldset>
                        <legend><span ng-bind-html="item.icon"></span>Cargar archivo</legend>

                        <div class="bs-component" ng-if="errorMessage != null">
                            <div class="alert alert-dismissible alert-danger">
                                <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                                </button>
                                <h4>Error</h4>
                                {{errorMessage}}
                            </div>
                        </div>

                        <div class="form-group filled">
                            <label class="control-label">Archivo</label>
                            <input type="file" ngf-select class="form-control" required ng-model="data.file">
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" ng-click="uploadBuildingFile(data.file)">
                                Cargar
                            </button>
                        </div>

                    </fieldset>
                </form>

            </div>
        </div>
    </div>
</script>

<script type="text/ng-template" id="/maintenance-form.html">
    <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
        <div class="close">
            <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
        </div>

        <div class="aside-dialog">
            <div class="aside-body bs-sidebar" cg-busy="savingMaintenance">

                <form class="form-floating" ng-submit="saveMaintenance(item)">
                    <fieldset>
                        <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' : 'Crear nueva'}}
                            Mantenimiento
                        </legend>

                        <div class="bs-component" ng-if="errorMessage != null">
                            <div class="alert alert-dismissible alert-danger">
                                <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                                </button>
                                <h4>Error</h4>
                                {{errorMessage}}
                            </div>
                        </div>

                        <div class="form-group filled">
                            <label class="control-label">Mantenimiento</label>
                            <select class="form-control" style="width:180px; display:inline"
                                    ng-model="item.maintenance_type" required>
                                <option value="0">MP100</option>
                                <option value="1">MP500</option>
                                <option value="2">MP1200</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="control-label"><i class="fa fa-calendar"></i>Fecha y Hora</label><br>
                            <div class="form-group">
                                <input type="text" size="10" class="form-control" ng-model="item.timestamp"
                                       data-autoclose="1" placeholder="Fecha" bs-datepicker required>
                            </div>
                            <div class="form-group">
                                <input type="text" size="8" class="form-control" ng-model="item.timestamp"
                                       data-time-format="HH:mm" data-autoclose="1" placeholder="Hora" bs-timepicker
                                       required>
                            </div>
                        </div>

                        <div class="form-group filled">
                            <label class="control-label">Técnico</label>
                            <select class="form-control" style="width:180px; display:inline"
                                    ng-model="item.technician" required
                                    ng-options="technician as technician for technician in technicians track by technician">
                            </select>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                        </div>

                    </fieldset>
                </form>

            </div>
        </div>
    </div>
</script>
