# Automated MySQL Database Backup

## Motivation

Preservation of data is an essential part of any business. Losing a database could effectively be the end of a company. The primary way to protect your data is to regularly back it up.

## Precondition

Read-only user with enough privileges to read tables and create the backup
[backup-user-creation](/db/backup/backup-user-creation.sql)

## Backup Process

Current steps included, but not limited to:

- Backup creation
- Compression
- Removal of old backups
- Notifications about backup results

### Backup creation

```bat
mysqldump --protocol=tcp --default-character-set=utf8 --host=%db_host% --port=3306 --user=BACKUP_USER --password=%mysql_password% --quick --lock-tables=false --no-tablespaces "lavomat" > %backup_name%.sql
```

### Compression (7z)

```bat
7z a %backup_name%.zip %backup_name%.sql
```

### Removal of old backups

```bat
del %backup_name%.sql
```

### Notifications about backup results

```bat
eventcreate /ID 1 /L APPLICATION /T INFORMATION /SO mysql-backup-script /D "LAVOMAT - Backup
```

### .bat script

[script.bat](/db/backup/script.bat)

## Running the Script Regularly

### Windows OS

- Open `Task Scheduler`
- Select `Create Basic Task` on the right panel.
- Select `Trigger` to be run daily.
- Follow the steps in the wizard. On the `Action` tab, select `Start a Program` and specify the path to the backup script.
- After clicking the `Finish` button, a task will be created that will be regularly launched at the specified interval.
