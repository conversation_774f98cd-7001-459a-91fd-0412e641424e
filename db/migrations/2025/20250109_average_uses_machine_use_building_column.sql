-- AFTER DEPLOY
DROP PROCEDURE IF EXISTS FillBuildingAverageUses;

DELIMITER //
CREATE PROCEDURE FillBuildingAverageUses()
BEGIN
    INSERT INTO average_uses(from_class, building_id, average30days, average6months, average1year, created_at,
                             washer_count, dryer_count)
    SELECT 'BuildingAverageUses',
           b.id,
           CAST(IFNULL(thirtyDays.uses, 0) / (IFNULL(thirtyDays.thirtyWorkingDays, 1) *
                                              IFNULL(buildingMachines.machineCount, 1)) AS decimal(19, 2)) AS thirtyDays,
           CAST(IFNULL(sixMonths.uses, 0) / (IFNULL(sixMonths.sixMonthsWorkingDays, 1) *
                                             IFNULL(buildingMachines.machineCount, 1)) AS decimal(19, 2))  AS sixMonths,
           CAST(IFNULL(oneYear.uses, 0) / (IFNULL(oneYear.oneYearWorkingDays, 1) *
                                           IFNULL(buildingMachines.machineCount, 1)) AS decimal(19, 2))    AS oneYear,
           now()                                                                                           AS createdAt,
           buildingMachines.washerCount,
           buildingMachines.dryerCount
    FROM building b
             LEFT JOIN (SELECT mu.building_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS thirtyWorkingDays, COUNT(*) AS uses
                        FROM machine_use mu
                                 INNER JOIN part p ON mu.machine_id = p.id
                                 INNER JOIN building b2 ON mu.building_id = b2.id
                                 LEFT JOIN part c ON mu.card_id = c.id
                        WHERE mu.timestamp > DATE_SUB(NOW(), INTERVAL 30 DAY)
                          AND mu.result IN ('0', '1', '5', '6', '7', '8', '30')
                          AND p.sort_index > 0
                          AND (c.id IS NULL OR c.master IS FALSE)
                          AND b2.is_enabled_for_maintenance IS TRUE
                        GROUP BY mu.building_id) thirtyDays
                       ON b.id = thirtyDays.building_id
             LEFT JOIN (SELECT mu.building_id,
                               DATEDIFF(NOW(), MIN(mu.timestamp)) AS sixMonthsWorkingDays,
                               COUNT(*)                           AS uses
                        FROM machine_use mu
                                 INNER JOIN part p ON mu.machine_id = p.id
                                 INNER JOIN building b2 ON mu.building_id = b2.id
                                 LEFT JOIN part c ON mu.card_id = c.id
                        WHERE mu.timestamp > DATE_SUB(NOW(), INTERVAL 6 MONTH)
                          AND mu.result IN ('0', '1', '5', '6', '7', '8', '30')
                          AND p.sort_index > 0
                          AND (c.id IS NULL OR c.master IS FALSE)
                          AND b2.is_enabled_for_maintenance IS TRUE
                        GROUP BY mu.building_id) sixMonths
                       ON b.id = sixMonths.building_id
             LEFT JOIN (SELECT mu.building_id,
                               DATEDIFF(NOW(), MIN(mu.timestamp)) AS oneYearWorkingDays,
                               COUNT(*)                           AS uses
                        FROM machine_use mu
                                 INNER JOIN part p ON mu.machine_id = p.id
                                 INNER JOIN building b2 ON mu.building_id = b2.id
                                 LEFT JOIN part c ON mu.card_id = c.id
                        WHERE mu.timestamp > DATE_SUB(NOW(), INTERVAL 1 YEAR)
                          AND mu.result IN ('0', '1', '5', '6', '7', '8', '30')
                          AND p.sort_index > 0
                          AND (c.id IS NULL OR c.master IS FALSE)
                          AND b2.is_enabled_for_maintenance IS TRUE
                        GROUP BY mu.building_id) oneYear
                       ON b.id = oneYear.building_id
             LEFT JOIN (SELECT p.building_id,
                               SUM(CASE WHEN p.machine_type = 'WASHER' THEN 1 ELSE 0 END)  AS washerCount,
                               SUM(CASE WHEN p.machine_type = 'DRYER' THEN 1 ELSE 0 END)   AS dryerCount,
                               (SUM(CASE WHEN p.machine_type = 'WASHER' THEN 1 ELSE 0 END) +
                                SUM(CASE WHEN p.machine_type = 'DRYER' THEN 1 ELSE 0 END)) AS machineCount
                        FROM part p
                                 INNER JOIN building b2
                                            ON p.building_id = b2.id
                        WHERE p.from_class = 'MACHINE'
                          AND p.building_id IS NOT NULL
                          AND p.sort_index > 0
                          AND b2.is_enabled_for_maintenance IS TRUE
                        GROUP BY p.building_id) buildingMachines
                       ON buildingMachines.building_id = b.id
    WHERE b.is_enabled_for_maintenance IS TRUE;

END //
DELIMITER ;

DROP PROCEDURE IF EXISTS FillPreventiveMaintenanceBuildingEntry;

DELIMITER //
CREATE PROCEDURE FillPreventiveMaintenanceBuildingEntry()
BEGIN
    DECLARE today DATE;
    SET today = NOW();
    INSERT INTO preventive_maintenance_building_entry(building_id,
                                                      maintenance_type,
                                                      machine_id,
                                                      maintenance_date,
                                                      technician,
                                                      uses,
                                                      created_at)
    SELECT b.id      AS building_id,
           mpType.mp AS maintenance_type,
           NULL      AS machine_id,
           NULL      AS maintenance_date,
           NULL      AS technician,
           0         AS uses,
           today     AS created_at
    FROM building b
             CROSS JOIN (SELECT "MP100" AS mp
                         UNION
                         SELECT "MP500" AS mp
                         UNION
                         SELECT "MP1200" AS mp) mpType
    WHERE b.is_enabled_for_maintenance IS TRUE;

    REPLACE INTO preventive_maintenance_building_entry(building_id,
                                                       maintenance_type,
                                                       machine_id,
                                                       maintenance_date,
                                                       technician,
                                                       uses,
                                                       created_at)
    SELECT e.building_id,
           e.maintenance_type,
           NULL          AS machine_id,
           mt.timestamp  AS maintenance_date,
           mt.technician AS technician,
           0             AS uses,
           e.created_at
    FROM preventive_maintenance_building_entry e
             LEFT JOIN maintenance mt ON e.building_id = mt.building_id
    WHERE mt.id = (SELECT mt1.id
                   FROM maintenance mt1
                   WHERE e.maintenance_type = mt1.maintenance_type
                     AND e.building_id = mt1.building_id
                   ORDER BY mt1.timestamp DESC
                   LIMIT 1)
      AND e.created_at = today;

    REPLACE INTO preventive_maintenance_building_entry(building_id,
                                                       maintenance_type,
                                                       machine_id,
                                                       maintenance_date,
                                                       technician,
                                                       uses,
                                                       created_at)
    SELECT DISTINCT e.building_id,
                    e.maintenance_type,
                    mu.machine_id,
                    e.maintenance_date,
                    e.technician,
                    COUNT(distinct mu.id) AS uses,
                    e.created_at
    FROM preventive_maintenance_building_entry e
             INNER JOIN machine_use mu ON e.building_id = mu.machine_id
        AND e.maintenance_date < mu.timestamp
        AND mu.result IN ('0', '1', '5', '6', '7', '8', '30')
        AND mu.machine_id = (SELECT mu1.machine_id AS machineId
                             FROM machine_use mu1
                                      INNER JOIN part p1 ON mu1.machine_id = p1.id
                             WHERE mu1.result IN ('0', '1', '5', '6', '7', '8', '30')
                               AND mu1.timestamp > e.maintenance_date
                               AND mu1.building_id = e.building_id
                               AND p1.machine_type = CASE
                                                         WHEN e.maintenance_type IN ('MP100', 'MP500') THEN 'WASHER'
                                                         ELSE 'DRYER'
                                 END
                             GROUP BY mu1.machine_id,
                                      p1.machine_type
                             ORDER BY COUNT(distinct mu1.id) DESC,
                                      mu1.machine_id
                             LIMIT 1)
    WHERE e.created_at = today
    GROUP BY e.building_id,
             e.maintenance_type,
             e.technician,
             e.maintenance_date,
             mu.machine_id;


END //
DELIMITER ;
