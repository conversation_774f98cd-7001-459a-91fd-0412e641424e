DROP PROCEDURE IF EXISTS FillPreventiveMaintenanceBuildingEntry;

ALTER TABLE preventive_maintenance_building_entry
ADD COLUMN created_at datetime;

ALTER TABLE average_uses
ADD COLUMN created_at datetime;

DELIMITER //
CREATE PROCEDURE FillPreventiveMaintenanceBuildingEntry()
BEGIN
	DELETE FROM preventive_maintenance_building_entry WHERE building_id > 0;
    
    INSERT INTO preventive_maintenance_building_entry(building_id, maintenance_type, machine_id, maintenance_date, technician, uses, created_at)
    SELECT b.id AS building_id,	mpType.mp AS maintenance_type, NULL AS machine_id, NULL as maintenance_date, NULL AS technician, 0 AS uses, now() as created_at
	FROM building b
	CROSS JOIN (SELECT "MP100" AS mp UNION SELECT "MP300" AS mp UNION SELECT "MP1200" AS mp) mpType;
    
	REPLACE INTO preventive_maintenance_building_entry(building_id, maintenance_type, machine_id, maintenance_date, technician, uses, created_at)
	SELECT DISTINCT
		b.id AS building_id, 
		mpType.mp AS maintenance_type,
		mu.machine_id,
		mt.timestamp AS maintenance_date,
		mt.technician,
		COUNT(distinct mu.id) AS uses,
		now() as created_at

	FROM building b

	CROSS JOIN (SELECT "MP100" AS mp UNION SELECT "MP300" AS mp UNION SELECT "MP1200" AS mp) mpType

	LEFT JOIN maintenance mt
	ON b.id = mt.building_id

	LEFT JOIN part p
	ON b.id = p.building_id

	LEFT JOIN machine_use mu
	ON mt.timestamp < mu.timestamp 
	AND p.id = mu.machine_id
	AND mu.result IN ('0', '1', '5', '6', '7')

	WHERE mt.id = (SELECT mt1.id
				   FROM maintenance mt1
				   WHERE mpType.mp = mt1.maintenance_type
				   AND b.id = mt1.building_id			
				   ORDER BY mt1.timestamp DESC
				   LIMIT 1)
				   
	AND mu.machine_id = (SELECT mu1.machine_id AS machineId
					FROM machine_use mu1  
					INNER JOIN part p1 ON mu1.machine_id = p1.id 	
					WHERE mu1.result IN ('0', '1', '5', '6', '7') 
					AND mu1.timestamp > mt.timestamp
					AND p1.building_id = b.id
					AND p1.machine_type = CASE WHEN mt.maintenance_type IN ('MP100' , 'MP300') THEN 'WASHER' 
						ELSE 'DRYER' END 
					GROUP BY mu1.machine_id, p1.machine_type 
					ORDER BY COUNT(distinct mu1.id) DESC, mu1.machine_id  
					LIMIT 1)

GROUP BY 
	b.id, 
	mpType.mp,
    mt.technician, 
    mt.timestamp,
    mu.machine_id;

END
//
DELIMITER ;


DROP PROCEDURE IF EXISTS FillBuildingAverageUses;

DELIMITER //
CREATE PROCEDURE FillBuildingAverageUses()
BEGIN
	DELETE FROM average_uses WHERE id > 0;

	INSERT INTO average_uses(from_class, building_id, average30days, average6months, average1year, created_at)
	SELECT 'BuildingAverageUses', b.id, 
			CAST(IFNULL(thirtyDays.uses, 0)/(IFNULL(thirtyDays.thirtyWorkingDays, 1)*IFNULL(buildingMachines.machineCount, 1)) AS decimal(19,2)) AS thirtyDays, 
			CAST(IFNULL(sixMonths.uses, 0)/(IFNULL(sixMonths.sixMonthsWorkingDays, 1)*IFNULL(buildingMachines.machineCount, 1)) AS decimal(19,2)) AS sixMonths,  
			CAST(IFNULL(oneYear.uses, 0)/(IFNULL(oneYear.oneYearWorkingDays, 1)*IFNULL(buildingMachines.machineCount, 1)) AS decimal(19,2)) AS oneYear,
			now() as created_at
	FROM building b 
	LEFT JOIN (SELECT p.building_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS thirtyWorkingDays, COUNT(*) AS uses 
				FROM machine_use mu 
				INNER JOIN part p ON mu.machine_id = p.id 
				WHERE mu.timestamp > DATE_SUB(NOW(), INTERVAL 30 DAY)  
				AND mu.result IN ('0', '1', '5', '6', '7') 
				GROUP BY p.building_id) thirtyDays 
	ON b.id = thirtyDays.building_id 
	LEFT JOIN (SELECT p.building_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS sixMonthsWorkingDays, COUNT(*) AS uses 
				FROM machine_use mu 
				INNER JOIN part p ON mu.machine_id = p.id 
				WHERE mu.timestamp > DATE_SUB(NOW(), INTERVAL 6 MONTH) 
				AND mu.result IN ('0', '1', '5', '6', '7') 
				GROUP BY p.building_id) sixMonths 
	ON b.id = sixMonths.building_id 
	LEFT JOIN (SELECT p.building_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS oneYearWorkingDays, COUNT(*) AS uses 
				FROM machine_use mu 
				INNER JOIN part p ON mu.machine_id = p.id 
				WHERE mu.timestamp > DATE_SUB(NOW(), INTERVAL 1 YEAR) 
				AND mu.result IN ('0', '1', '5', '6', '7') 
				GROUP BY p.building_id) oneYear 
	ON b.id = oneYear.building_id
	LEFT JOIN (SELECT p.building_id, COUNT(*) AS machineCount
			   FROM part p
			   WHERE p.building_id IS NOT NULL
			   GROUP BY p.building_id) buildingMachines
	ON buildingMachines.building_id = b.id;

END
//
DELIMITER ;
